import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
  useMemo,
} from 'react';
import { isMobileOnly } from 'react-device-detect';
import type {
  ChatBoxUIAppProps,
  ChatResponse,
  CssData,
  MessageEventPayload,
  NavigatorType,
  LiveChatHeaderInfor,
  LiveChatConversation,
  ChatbotSystemState,
  VoteCollection,
  ArticleAnalyticEventPayload,
} from '../models';
import {
  getCurrentPage,
  getUserAgent,
  generateLoaderMessage,
  generateActionSendChatboxDisplayStyleEvent,
} from '../utils';
import { BOT_TRIGGER_TYPES, MESSAGE_SCREENS, randomDomId } from '@/constants';
import {
  initLiveChatInfor,
  LIVECHAT_CONVERSATION_LIMIT,
  LIVECHAT_MESSAGE_HISTORY_LIMIT,
  LIVECHAT_CONVERSATION_STATUS,
  CHATBOT_SYSTEM_TYPES,
  CHATBOT_MESSAGE_HISTORY_LIMIT,
  LIVECHAT_USER_EVENTS_CHANNEL,
  COMMON_MESSAGE_EVENTS,
  DECA_LC_CONVERSATION_ID_KEY,
} from '../constants';
import AppService from '../services/app';
import ApiService from '../services/api';
import {
  createCustomEventListener,
  removeCustomEventListener,
  sendCustomEvent,
} from '@resola-ai/utils';
import { WIDGET_TYPE } from '@/constants/widgetType';
import type { Centrifuge } from 'centrifuge';

const useChatBoxUI = (props: ChatBoxUIAppProps) => {
  const [currentPage, setCurrentPage] = useState<NavigatorType>(props.chatbox.defaultPage);
  const [chatBoxUIAvailability, setChatBoxUIAvailability] = useState(props.general?.availability);
  const [chatBoxUIState, setChatBoxUIState] = useState(props.chatbox.showChatBoxAsDefault);
  const [isDisplayDouble, setIsDisplayDouble] = useState(true);
  const [messages, setMessages] = useState<ChatResponse[]>([]);
  const [messageScreen, setMessageScreen] = useState(MESSAGE_SCREENS.conversation);
  const [liveChatConnected, setLiveChatConnected] = useState(false);
  const [connectLiveChatInfor, setConnectLiveChatInfor] =
    useState<LiveChatHeaderInfor>(initLiveChatInfor);
  const [currentConversationId, setCurrentConversationId] = useState<string>('');
  const [isConversationAssignedToOperator, setIsConversationAssignedToOperator] =
    useState<boolean>(false);
  const [isDisabledMessageInput, setIsDisabledMessageInput] = useState<boolean>(false);
  const [conversationSelected, setConversationSelected] = useState<LiveChatConversation | null>(
    null
  );
  const [livechatConversationList, setLivechatConversationList] = useState<LiveChatConversation[]>(
    []
  );
  const [chatbotSystemState, setChatbotSystemState] = useState<ChatbotSystemState>({
    type: '',
    text: '',
  });

  const [isKBCardMessaging, setIsKBCardMessaging] = useState<boolean>(false);
  const [actionButtonEvents, setActionButtonEvents] = useState<string[]>([]);
  const [submitFormMessageEvents, setSubmitFormMessageEvents] = useState<string[]>([]);
  const [chatBoxDisplayStyle, setChatBoxDisplayStyle] = useState<string>('');
  const [articleAnalyticsEvents, setArticleAnalyticsEvents] = useState<string[]>([]);
  const [isFocusInput, setIsFocusInput] = useState<boolean>(false);
  const [lastLiveChatMessage, setLastLiveChatMessage] = useState<ChatResponse | null>(null);
  const [selectedLiveChatTeamEvents, setSelectedLiveChatTeamEvents] = useState<string[]>([]);
  const [centrifugeInstance, setCentrifugeInstance] = useState<Centrifuge | null>(null);
  const [isErrorFetchLCConversations, setIsErrorFetchLCConversations] = useState<boolean>(false);
  const [isStreaming, setIsStreaming] = useState<boolean>(false);
  const [streamingMessageId, setStreamingMessageId] = useState<string>('');

  const boxId = AppService.getInstance(props.boxId).decaBoxId ?? '';
  const wsUserId = AppService.getInstance(props.boxId).getUserId();
  const botId = AppService.getInstance(props.boxId).decaBotId ?? '';
  const organizationId = AppService.getInstance(props.boxId).decaOrgId ?? '';
  const widgetsList = props.widgets || [];
  const actionColor = props.brand.actionColor;

  const urlGetLiveChatHistoryMessages = `/user/${wsUserId}/state?conversationId=${currentConversationId}&limit=${LIVECHAT_MESSAGE_HISTORY_LIMIT}`;
  const urlGetLiveChatConversationList = `/user/${wsUserId}/conversations?limit=${LIVECHAT_CONVERSATION_LIMIT}`;
  const urlGetChatbotHistoryMessages = `/user/${wsUserId}/state?order=asc&botId=${botId}&limit=${CHATBOT_MESSAGE_HISTORY_LIMIT}`;

  const isCompletedLiveChatConversation =
    conversationSelected && conversationSelected.status === LIVECHAT_CONVERSATION_STATUS.COMPLETED;
  const isConnectNotAssignedOperatorYet = liveChatConnected && !isConversationAssignedToOperator;
  const isConnectAssignedOperator = liveChatConnected && isConversationAssignedToOperator;
  const isDisconnectLiveChat = !liveChatConnected;

  const isFinishedChatbotConversation = chatbotSystemState.type === CHATBOT_SYSTEM_TYPES.FINISHED;
  const isTurnOnLiveChatIntegration = props.integrationSettings?.livechatConnected;

  const decaLCConversationIdKey = useMemo(() => {
    return DECA_LC_CONVERSATION_ID_KEY.replace('{boxId}', boxId);
  }, [boxId]);

  const userAgent = useMemo(() => getUserAgent(), []);

  const sourceInfo = useMemo(
    () => ({
      type: 'user',
      userId: wsUserId,
      meta: {
        userAgent,
      },
    }),
    [wsUserId, userAgent]
  );

  const articleAnalyticEventPayload: ArticleAnalyticEventPayload = useMemo(() => {
    return {
      event: '',
      requestId: randomDomId(),
      organizationId,
      anonymousId: wsUserId,
      context: {
        userAgent: JSON.stringify(userAgent),
        source: 'chatbot',
      },
      properties: {
        itemId: '',
      },
      sentAt: new Date().toISOString(),
    };
  }, [organizationId, wsUserId, userAgent]);

  const forceDisconnectLivechatPayload = useMemo(() => {
    const livechatConversationId = localStorage.getItem(decaLCConversationIdKey) ?? '';
    return {
      action: 'forceDisconnectLivechat',
      events: [],
      userId: wsUserId,
      conversationId: livechatConversationId,
    };
  }, [wsUserId, decaLCConversationIdKey]);

  const voteCollectionStorageRef = useRef<VoteCollection>({});

  const [navigatorData, setNavigatorData] = useState<CssData>({
    height: 0,
    width: 0,
  });

  const [emptyStateHeaderData, setEmptyStateHeaderData] = useState<CssData>({
    height: 0,
    width: 0,
  });

  const [headerData, setHeaderData] = useState<CssData>({
    height: 0,
    width: 0,
  });

  const [inputElmData, setInputElmData] = useState<CssData>({
    height: 0,
    width: 0,
  });

  const [startConversationButtonData, setStartConversationButtonData] = useState<CssData>({
    height: 0,
    width: 0,
  });

  const [triggerFlowButtonElmData, setTriggerFlowButtonElmData] = useState<CssData>({
    height: 0,
    width: 0,
  });

  const messageNavigator = {
    isChat: () => messageScreen === MESSAGE_SCREENS.chat,
    isConversation: () => messageScreen === MESSAGE_SCREENS.conversation,
    goToConversation: () => {
      setMessageScreen(MESSAGE_SCREENS.conversation);
    },
    goToChat: () => {
      setMessageScreen(MESSAGE_SCREENS.chat);
    },
  };

  const navigatorRef = useRef<any>(null);
  const emptyStateHeaderRef = useRef<any>(null);

  const hideChatBox = () => setChatBoxUIState(false);
  const showChatBox = () => setChatBoxUIState(true);

  const updateNavigatorRef = (ref: any) => {
    navigatorRef.current = ref;
    if (!ref) {
      return;
    }
    setTimeout(() => {
      setNavigatorData({
        height: ref.clientHeight,
        width: ref.clientWidth,
      });
    });
  };

  const updateInputElmRef = (ref: any) => {
    if (!ref) {
      return;
    }
    setTimeout(() => {
      setInputElmData({
        height: ref.clientHeight,
        width: ref.clientWidth,
      });
    });
  };

  const updateHeaderRef = (ref: any) => {
    if (!ref) {
      return;
    }
    setTimeout(() => {
      setHeaderData({
        height: ref.clientHeight,
        width: ref.clientWidth,
      });
    });
  };

  const updateEmptyStateHeaderRef = (ref: any) => {
    emptyStateHeaderRef.current = ref;
    if (!ref) {
      return;
    }
    setTimeout(() => {
      setEmptyStateHeaderData({
        height: ref.clientHeight,
        width: ref.clientWidth,
      });
    });
  };

  const updateStartConversationButtonRef = (ref: any) => {
    if (!ref) {
      return;
    }
    setTimeout(() => {
      setStartConversationButtonData({
        height: ref.clientHeight,
        width: ref.clientWidth,
      });
    });
  };

  const updateTriggerFlowButtonRef = (ref: any) => {
    if (!ref) {
      return;
    }
    setTimeout(() => {
      setTriggerFlowButtonElmData({
        height: ref.clientHeight,
        width: ref.clientWidth,
      });
    });
  };

  const clickLauncherHandler = useCallback(() => {
    if (chatBoxUIState) {
      hideChatBox();
    } else {
      showChatBox();
    }
  }, [chatBoxUIState]);

  const navigateHandler = useCallback(
    (type: NavigatorType) => {
      if (type === 'home') {
        setCurrentPage('home');
      }
      if (type === 'message') {
        setCurrentPage('message');
        if (isTurnOnLiveChatIntegration === false) {
          messageNavigator.goToChat();
        }
      }
    },
    [isTurnOnLiveChatIntegration]
  );

  const disableChatBoxUI = useCallback(() => {
    setChatBoxUIAvailability(false);
    hideChatBox();
  }, []);

  const closeHandler = useCallback((e: any) => {
    e.preventDefault();
    e.stopPropagation();
    disableChatBoxUI();
  }, []);

  const handleChatbotSystemMessages = useCallback(
    (message: ChatResponse) => {
      if (message.type === 'system') {
        const { type, text } = message.data;
        setChatbotSystemState({ type, text });
      }
    },
    [messages]
  );

  const updateMessages = (message: ChatResponse) => {
    setMessages((prev) => {
      // if there is any buttons type in the prev, then remove it
      const removePrevButtons = prev.filter((message) => message.type !== 'buttons');
      // remove loader message
      const newMessages = removePrevButtons.filter((message) => message.type !== 'loader');
      return [...newMessages, message];
    });
  };

  /**
   * Normalize the event payload to ensure unique timestamps
   * @param payload - The event payload to normalize
   * @returns The normalized event payload
   */
  const normalizeEventPayload = useCallback((payload: MessageEventPayload) => {
    const baseTimestamp = Date.now();
    return {
      ...payload,
      events: payload.events.map((event, index) => ({
        ...event,
        timestamp: baseTimestamp + index, // Add small increment to ensure unique timestamps
      })),
    };
  }, []);

  const handleSendEventData = useCallback(
    (payload: MessageEventPayload) => {
      ApiService.sendEventDataToHook(boxId, normalizeEventPayload(payload));
    },
    [boxId, normalizeEventPayload]
  );

  const handleSendArticleAnalyticEvent = useCallback(
    (payload: ArticleAnalyticEventPayload) => {
      ApiService.sendArticleAnalyticEvent(boxId, payload);
    },
    [boxId]
  );

  const handleTriggerChatbotFlow = useCallback(() => {
    const loader = generateLoaderMessage(actionColor);
    updateMessages(loader);
    const payload = {
      events: [
        {
          type: 'trigger',
          message: {
            type: 'trigger',
            data: {},
          },
          source: sourceInfo,
        },
      ],
    };
    handleSendEventData(payload);
  }, [wsUserId, actionColor, userAgent, boxId]);

  const handleGoToChat = useCallback(() => {
    navigateHandler('message');
    messageNavigator.goToChat();
  }, [messageNavigator.goToChat]);

  const resetChatBoxState = useCallback(() => {
    setIsKBCardMessaging(false);
  }, [setIsKBCardMessaging]);

  const handleSendLiveChatDisplayEvent = useCallback(
    (eventType: string) => {
      const payload = {
        type: eventType,
        message: {
          type: eventType,
          data: {
            enduserId: wsUserId,
            conversationId: currentConversationId,
            messageId: lastLiveChatMessage?.id,
            orgId: organizationId,
            teamId: connectLiveChatInfor.teamId,
            assigneeId: connectLiveChatInfor.assigneeId,
            category: 'status',
          },
        },
      };

      sendLiveChatActivityEvent(payload);
    },
    [lastLiveChatMessage, currentConversationId, wsUserId, userAgent, boxId, organizationId]
  );

  const sendLiveChatActivityEvent = useCallback(
    (data: any) => {
      if (!centrifugeInstance) {
        console.error('Centrifuge instance not initialized');
        return;
      }

      try {
        centrifugeInstance.publish(LIVECHAT_USER_EVENTS_CHANNEL, data);
      } catch (error) {
        console.error('Error sending live chat activity event:', error);
      }
    },
    [centrifugeInstance]
  );

  const sendForceDisconnectLivechat = useCallback(async () => {
    await ApiService.sendEventDataToHook(boxId, forceDisconnectLivechatPayload);
    localStorage.removeItem(decaLCConversationIdKey);
  }, [boxId, forceDisconnectLivechatPayload, decaLCConversationIdKey]);

  const handleStopStreaming = useCallback(() => {
    const currentStreamingMessageId = streamingMessageId;

    const payload = {
      events: [
        {
          type: 'stream.control',
          message: {
            type: 'stop',
            data: {
              messageId: currentStreamingMessageId,
            },
          },
          source: sourceInfo,
        },
      ],
    };

    // Send custom event to notify wsContext to stop processing chunks
    sendCustomEvent(COMMON_MESSAGE_EVENTS.STOP_STREAMING, { messageId: currentStreamingMessageId });

    setIsStreaming(false);
    setStreamingMessageId('');
    handleSendEventData(payload);
  }, [streamingMessageId, sourceInfo, handleSendEventData]);

  useEffect(() => {
    if (!props.chatbox.displayComponents.includes(currentPage)) {
      setCurrentPage(getCurrentPage(props));
    }
    setIsDisplayDouble(props.chatbox.displayComponents.length === 2);
  }, [props, currentPage]);

  useEffect(() => {
    isDisplayDouble && messageNavigator.goToConversation();
    navigateHandler(isDisplayDouble ? 'home' : 'message');
  }, [isDisplayDouble]);

  useEffect(() => {
    setChatBoxUIState(props.general?.autoOpen ?? false);
  }, [props.general?.autoOpen]);

  useEffect(() => {
    if (currentConversationId) return;
    const lastMessage = messages[messages.length - 1];
    if (lastMessage) {
      handleChatbotSystemMessages(lastMessage);
    }
  }, [messages]);

  useEffect(() => {
    if (props.elementId && isMobileOnly) {
      if (props.general?.botTrigger === BOT_TRIGGER_TYPES.CLICK_A_BUTTON) {
        setChatBoxDisplayStyle('none');
      } else {
        setChatBoxDisplayStyle('block');
      }
    }
  }, [props.general?.botTrigger]);

  useEffect(() => {
    if (props.elementId && isMobileOnly) {
      const eventSendDisplayStyle = generateActionSendChatboxDisplayStyleEvent(props.elementId);
      const handleCustomEvent = (event: any) => {
        setChatBoxDisplayStyle(event.detail);
      };
      window.addEventListener(eventSendDisplayStyle, handleCustomEvent);

      const thisElement = document.getElementById(props.elementId);
      if (thisElement) {
        thisElement.style.width = chatBoxUIState ? '100%' : '0';
        thisElement.style.height = chatBoxUIState ? '100%' : '0';
        if (thisElement.parentElement) {
          // Remove parent scroll when chatbot is opened on mobile
          if (chatBoxUIState) {
            if (chatBoxDisplayStyle === 'none') {
              thisElement.parentElement.style.overflow = 'initial';
            } else {
              thisElement.parentElement.style.overflow = 'hidden';
            }
          } else {
            thisElement.parentElement.style.overflow = 'initial';
          }
        }
      }

      // Clean up the event listener
      return () => {
        window.removeEventListener(eventSendDisplayStyle, handleCustomEvent);
      };
    }
  }, [chatBoxUIState, props.elementId, chatBoxDisplayStyle]);

  useEffect(() => {
    if (!widgetsList || widgetsList.length === 0) return;
    /*Binding event listener for the call to action button widget*/
    const eventClickedList = widgetsList
      .filter((widget) => widget.type === WIDGET_TYPE.CALL_TO_ACTION)
      .map((widget) => widget.properties?.clickedEvent || '');

    if (eventClickedList.length) {
      eventClickedList.forEach((event) => {
        createCustomEventListener(event, handleGoToChat);
      });
    }

    return () => {
      if (eventClickedList.length) {
        eventClickedList.forEach((event) => {
          removeCustomEventListener(event, handleGoToChat);
        });
      }
    };
  }, [widgetsList, handleGoToChat]);

  const dom = {
    navigatorData,
    emptyStateHeaderData,
    headerData,
    inputElmData,
    startConversationButtonData,
    triggerFlowButtonElmData,
  };

  return {
    currentPage,
    chatBoxUIState,
    chatBoxUIAvailability,
    clickLauncherHandler,
    navigateHandler,
    closeHandler,
    updateNavigatorRef,
    updateEmptyStateHeaderRef,
    updateHeaderRef,
    updateInputElmRef,
    updateStartConversationButtonRef,
    dom,
    isDisplayDouble,
    messages,
    updateMessages,
    messageNavigator,
    handleSendEventData,
    normalizeEventPayload,
    setMessages,
    liveChatConnected,
    actionColor,
    connectLiveChatInfor,
    setConnectLiveChatInfor,
    setLiveChatConnected,
    currentConversationId,
    setCurrentConversationId,
    isConversationAssignedToOperator,
    setIsConversationAssignedToOperator,
    isDisabledMessageInput,
    setIsDisabledMessageInput,
    userAgent,
    urlGetLiveChatHistoryMessages,
    urlGetLiveChatConversationList,
    conversationSelected,
    setConversationSelected,
    livechatConversationList,
    setLivechatConversationList,
    isCompletedLiveChatConversation,
    isConnectNotAssignedOperatorYet,
    isConnectAssignedOperator,
    isDisconnectLiveChat,
    wsUserId,
    organizationId,
    chatbotSystemState,
    setChatbotSystemState,
    isFinishedChatbotConversation,
    updateTriggerFlowButtonRef,
    isKBCardMessaging,
    setIsKBCardMessaging,
    urlGetChatbotHistoryMessages,
    botId,
    handleTriggerChatbotFlow,
    actionButtonEvents,
    setActionButtonEvents,
    submitFormMessageEvents,
    setSubmitFormMessageEvents,
    boxId,
    resetChatBoxState,
    articleAnalyticEventPayload,
    handleSendArticleAnalyticEvent,
    voteCollectionStorageRef,
    articleAnalyticsEvents,
    setArticleAnalyticsEvents,
    isFocusInput,
    setIsFocusInput,
    lastLiveChatMessage,
    setLastLiveChatMessage,
    handleSendLiveChatDisplayEvent,
    isTurnOnLiveChatIntegration,
    selectedLiveChatTeamEvents,
    setSelectedLiveChatTeamEvents,
    sourceInfo,
    centrifugeInstance,
    setCentrifugeInstance,
    isErrorFetchLCConversations,
    setIsErrorFetchLCConversations,
    sendForceDisconnectLivechat,
    isStreaming,
    setIsStreaming,
    handleStopStreaming,
    streamingMessageId,
    setStreamingMessageId,
    forceDisconnectLivechatPayload,
    decaLCConversationIdKey,
  };
};

export type ChatBoxUIContextType = ReturnType<typeof useChatBoxUI>;

const context = createContext<ChatBoxUIContextType | null>(null);

interface ChatBoxUIContextProviderProps {
  children: any;
  setting: ChatBoxUIAppProps;
}
export const ChatBoxUIContextProvider: React.FC<ChatBoxUIContextProviderProps> = (props) => {
  const value = useChatBoxUI(props.setting);

  return <context.Provider value={value}>{props.children}</context.Provider>;
};

export const useChatBoxUIContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useChatBoxUIContext must be used inside ChatBoxUIContextProvider');
  }

  return value;
};
