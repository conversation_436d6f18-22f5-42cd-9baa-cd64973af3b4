import {
  useEffect,
  useState,
  useRef,
  useCallback,
  createContext,
  useContext,
  useMemo,
} from 'react';
import { Centrifuge } from 'centrifuge';
import { AppConfig } from '@/configs';
import { WIDGET_ENGINE_MESSAGE_SOURCES } from '@resola-ai/widget-engine';
import ApiService from '../services/api';
import { useChatBoxUIContext } from './chatBoxUIContext';
import type {
  ChatResponse,
  LiveChatFromType,
  LiveChatMessageData,
  StreamingResponse,
  SystemMessageResponse,
  QAResponse,
} from '../models';
import {
  LIVECHAT_CONVERSATION_EVENTS,
  CHATBOT_SYSTEM_TYPES,
  COMMON_MESSAGE_EVENTS,
  LIVECHAT_DISPLAY_EVENT_TYPES,
  initLiveChatInfor,
  TemplateTypes,
  LIVECHAT_USER_EVENTS_CHANNEL,
} from '../constants';
import {
  generateLoaderMessage,
  generateSubmitFormMessageEvent,
  generateActionButtonClickedEvent,
  generateSendArticleAnalyticsEvent,
  generateSelectTeamInLivechatModeEvent,
} from '../utils';
import AppService from '../services/app';
import {
  sendCustomEvent,
  createCustomEventListener,
  removeCustomEventListener,
} from '@resola-ai/utils';
import { fixEscapeCharacters } from '@/components/ChatBoxUI/utils';
import { ARTICLE_EVENT_TYPES } from '@/constants';
import { getHookDomain } from '../utils/hook';
import { replaceLineBreaks } from '@/utils';

interface WSContextProps {
  boxId: string;
  children: React.ReactNode;
}

const useWs = (boxId: string) => {
  const [wsToken, setWsToken] = useState<string>('');
  const wsUserId = AppService.getInstance(boxId).getUserId();

  const {
    messages,
    setMessages,
    handleSendEventData,
    actionColor,
    setConnectLiveChatInfor,
    setLiveChatConnected,
    setCurrentConversationId,
    setIsConversationAssignedToOperator,
    setIsDisabledMessageInput,
    isCompletedLiveChatConversation,
    isDisabledMessageInput,
    isKBCardMessaging,
    setIsKBCardMessaging,
    actionButtonEvents,
    setActionButtonEvents,
    submitFormMessageEvents,
    setSubmitFormMessageEvents,
    articleAnalyticEventPayload,
    handleSendArticleAnalyticEvent,
    articleAnalyticsEvents,
    setArticleAnalyticsEvents,
    setLastLiveChatMessage,
    isFocusInput,
    handleSendLiveChatDisplayEvent,
    selectedLiveChatTeamEvents,
    setSelectedLiveChatTeamEvents,
    sourceInfo,
    setCentrifugeInstance,
    setIsStreaming,
    isStreaming,
    setStreamingMessageId,
    streamingMessageId,
    decaLCConversationIdKey,
  } = useChatBoxUIContext();
  const location = window.location;

  const messagesRef = useRef<ChatResponse[]>([]);
  const eventHandlerRef = useRef<(...args: any) => any>();
  const routeRef = useRef<string>(location.pathname);
  const articleIdViewedListRef = useRef<string[]>([]);
  const streamMessagesRef = useRef<Record<string, ChatResponse>>({});
  const stoppedStreamingRef = useRef<Record<string, boolean>>({});

  const memoizedSelectLiveChatTeamEvents = useMemo(
    () => selectedLiveChatTeamEvents,
    [selectedLiveChatTeamEvents]
  );

  const retrieveWsToken = async () => {
    const token = await ApiService.getWsToken(wsUserId, wsToken);
    setWsToken(token);
  };

  const renewWsToken = async () => {
    const token = await ApiService.getWsToken(wsUserId, wsToken);
    return token;
  };

  const getChannelName = (channelId: string, userId: string) => {
    return `deca:${channelId}|user#${userId}`;
  };

  const handleActionEvent = useCallback(
    (event: any) => {
      const { action, messageId } = event.detail;
      const actionUrl = action?.href || action?.url || '';
      const payload = {
        events: [
          {
            type: 'message',
            message: {
              type: 'button',
              data: {
                buttonId: action.data?.buttonId,
                buttonLabel: action.data?.buttonLabel,
                buttonValue: action.data?.buttonValue ?? '',
                url: actionUrl,
              },
            },
            source: sourceInfo,
          },
        ],
      };
      if (actionUrl) {
        window.open(actionUrl, '_blank');
      }
      handleSendEventData(payload);
      const currentMessages = [...messagesRef.current];
      const currentMessage = currentMessages.find((m) => m.id === messageId);
      const newMessages = [...currentMessages];
      const index = currentMessages.findIndex((m) => m.id === messageId);
      if (!currentMessage || index === -1) return;
      if (currentMessage.type === 'buttons') {
        newMessages[index] = {
          id: messageId,
          created: currentMessage.created,
          from: 'user',
          type: 'text',
          data: {
            text: action.text,
          },
        };
      }
      if (currentMessage.type === 'card') {
        // remove the other actions of the card except the clicked action
        newMessages[index] = {
          id: messageId,
          created: currentMessage.created,
          from: 'user',
          type: 'card',
          data: {
            ...currentMessage.data,
            actions: [],
          },
        };
        // add a new one as text
        newMessages.push({
          id: messageId,
          created: currentMessage.created,
          from: 'user',
          type: 'text',
          data: {
            text: action.text,
          },
        });
      }
      const loader = generateLoaderMessage(actionColor);
      newMessages.push(loader);
      setMessages(newMessages);
      sendCustomEvent(COMMON_MESSAGE_EVENTS.UPDATE_TEXT_FROM_USER_INPUT, '');
    },
    [messages, actionColor, sourceInfo]
  );

  const handleSubmitFormData = useCallback(
    (event: any) => {
      const payload = {
        events: [
          {
            type: 'message',
            message: {
              type: 'form',
              data: event.detail.data,
            },
            source: sourceInfo,
          },
        ],
      };

      const newMessages = messages.filter((message) => message.type !== 'form');
      setMessages(newMessages);
      setIsDisabledMessageInput(false);
      handleSendEventData(payload);
    },
    [messages, handleSendEventData, setIsDisabledMessageInput, sourceInfo]
  );

  const handleArticleClickedEvent = useCallback(
    (event: any) => {
      const { eventType, articleId, voteType } = event.detail;
      if (!articleId) return;

      const newPayload = {
        ...articleAnalyticEventPayload,
        event: eventType,
        properties: {
          ...articleAnalyticEventPayload.properties,
          itemId: articleId,
        },
      };

      if (eventType === ARTICLE_EVENT_TYPES.VIEW) {
        if ('feedback' in newPayload.properties) {
          newPayload.properties.feedback = undefined;
        }
      } else {
        newPayload.properties.feedback = {
          type: voteType,
        };
      }

      // Check if the article has been viewed
      if (eventType === ARTICLE_EVENT_TYPES.VIEW) {
        if (articleIdViewedListRef.current.includes(articleId)) return;
        articleIdViewedListRef.current.push(articleId);
      }

      handleSendArticleAnalyticEvent(newPayload);
    },
    [articleIdViewedListRef, handleSendArticleAnalyticEvent, articleAnalyticEventPayload]
  );

  const handleSelectLiveChatTeamEvent = useCallback(
    (event: any) => {
      const { teamId, text } = event.detail;
      const payload = {
        events: [
          {
            type: 'message',
            message: {
              type: 'livechatTeam',
              data: {
                teamId,
                text,
              },
            },
            source: sourceInfo,
          },
        ],
      };
      const newMessages = messages.filter((message) => message.type !== 'livechatTeam');
      setMessages(newMessages);
      setIsDisabledMessageInput(false);
      handleSendEventData(payload);
    },
    [sourceInfo, handleSendEventData, messages]
  );

  const handleLiveChatEvents = useCallback(
    (lastMessage: ChatResponse) => {
      const text = (lastMessage.data as LiveChatMessageData).text;
      const type = (lastMessage.data as LiveChatMessageData).type;
      const isActivityType = type === 'activity';
      const isCreatedEvent = isActivityType && text === LIVECHAT_CONVERSATION_EVENTS.CREATED;
      const isAssignOperatorEvent =
        isActivityType && text === LIVECHAT_CONVERSATION_EVENTS.ASSIGNED;
      const isCompletedEventPattern = (text || '').includes('conversation.completed');
      const isWrapupEventPattern = (text || '').includes('conversation.inwrapup');
      const isDisconnectFromLiveChat =
        isActivityType && (isCompletedEventPattern || isWrapupEventPattern);

      if (isCreatedEvent) {
        const conversationId =
          (lastMessage as ChatResponse & { conversationId?: string }).conversationId ?? '';
        setLiveChatConnected(true);
        setIsConversationAssignedToOperator(false);
        setCurrentConversationId(conversationId);
        localStorage.setItem(decaLCConversationIdKey, conversationId);
        const newMessages = messagesRef.current.filter((message) => message.type !== 'loader');
        setMessages(newMessages);
      }

      if (isAssignOperatorEvent) {
        const messageInfo = lastMessage.data as LiveChatMessageData;
        const data = {
          name: messageInfo.metadata?.assignee?.name ?? '',
          picture: messageInfo.metadata?.assignee?.picture ?? '',
          description: messageInfo.metadata?.team?.name ?? '',
          teamId: messageInfo.metadata?.team?.id ?? '',
          assigneeId: messageInfo.metadata?.assignee?.id ?? '',
        };

        setConnectLiveChatInfor(data);
        setIsConversationAssignedToOperator(true);
      }

      /* Disconnect from LiveChat*/
      if (isDisconnectFromLiveChat) {
        setConnectLiveChatInfor(initLiveChatInfor);
        setCurrentConversationId('');
        setLiveChatConnected(false);
        setIsConversationAssignedToOperator(false);
        setLastLiveChatMessage(null);
        localStorage.removeItem(decaLCConversationIdKey);
      }
    },
    [messages]
  );

  const handleChatbotSystemMessage = useCallback(
    (message: ChatResponse) => {
      /* 
      - Handle the case when receive a chatbot system message
      - Handle return a flag to disable the message input field 
    */
      let isDisabled = true;
      const action = (message.data as SystemMessageResponse)?.action;
      if (action?.enableInput) {
        isDisabled = false;
      }

      return isDisabled;
    },
    [messages]
  );

  const isMessageInputFieldDisabled = useCallback(
    (message: ChatResponse) => {
      switch (message.type) {
        case TemplateTypes.Form:
        case TemplateTypes.LiveChatTeam:
          return true;
        case TemplateTypes.Buttons:
          /* Turn on: 'Allow user input and search intents' in buttons card => Enable the input field to search */
          return !message.data?.canSearchIntent;
        case TemplateTypes.System: {
          const isDisabled = handleChatbotSystemMessage(message);
          return isDisabled;
        }
        default:
          return false;
      }
    },
    [messages]
  );

  const checkStreamStopped = useCallback((messageId: string, isCompleted: boolean) => {
    if (stoppedStreamingRef.current[messageId]) {
      // If completed, clean up the stopped flag
      if (isCompleted) delete stoppedStreamingRef.current[messageId];

      return true; // Don't process further chunks for stopped streams
    }
    return false;
  }, []);

  const handleStreamingState = useCallback(
    (messageId: string, isCompleted: boolean) => {
      if (!isStreaming) setIsStreaming(true);

      if (!streamingMessageId) setStreamingMessageId(messageId);

      if (isCompleted) {
        delete streamMessagesRef.current[messageId];
        setIsStreaming(false);
        setStreamingMessageId('');
      }
    },
    [isStreaming, streamingMessageId]
  );

  const handleLoaderAnimationMessage = useCallback(
    (message: ChatResponse) => {
      const metadata = (message.data as SystemMessageResponse)?.metadata;
      const isKBStepType = metadata?.stepType === 'qna';
      const isKBSearchMode = metadata?.mode === 'search';
      const isKBRagMode = metadata?.mode === 'rag';
      const isActiveStatus = metadata?.status === 'active';
      const isCaptureInputStepType = metadata?.stepType === 'capture';
      const isCaptureIntentStepType = metadata?.stepType === 'choice';
      const isEntityStepType = metadata?.stepType === 'entity';
      const isExecutionSystem =
        (message.data as SystemMessageResponse)?.type === CHATBOT_SYSTEM_TYPES.EXECUTION;
      const isKBSearchModeProcess =
        isExecutionSystem && isKBSearchMode && isKBStepType && isActiveStatus;
      const isKBRagModeProcess = isExecutionSystem && isKBRagMode && isKBStepType && isActiveStatus;
      const isCaptureInputProcess = isExecutionSystem && isCaptureInputStepType && isActiveStatus;
      const isCaptureIntentProcess = isExecutionSystem && isCaptureIntentStepType && isActiveStatus;
      const isAICaptureProcess = isExecutionSystem && isEntityStepType && isActiveStatus;

      const groupSystemProcess = [
        isKBSearchModeProcess,
        isKBRagModeProcess,
        isCaptureInputProcess,
        isCaptureIntentProcess,
        isAICaptureProcess,
      ].some(Boolean);

      if (groupSystemProcess) {
        const isLoaderMessage = messages.some((message) => message.type === 'loader');
        if (isLoaderMessage) {
          setMessages((prev) => prev.filter((message) => message.type !== 'loader'));
        }
      }

      return {
        isKBSearchModeProcess,
        isKBRagModeProcess,
      };
    },
    [messages]
  );

  const handleStreamMessage = useCallback(
    (message: ChatResponse) => {
      if (message.type !== TemplateTypes.Stream) return null;

      const messageId = message.id ?? '';
      const isCompleted = message?.completed === true;

      // Check if this stream was manually stopped
      if (checkStreamStopped(messageId, isCompleted)) return null;

      // Get existing stream message or create a new one
      let streamMessage = streamMessagesRef.current[messageId];

      if (!streamMessage) {
        handleStreamingState(messageId, false);

        streamMessage = {
          ...message,
          data: {
            ...message.data,
            text: message.data?.text ?? '',
          },
        };
        streamMessagesRef.current[messageId] = streamMessage;
      } else {
        // Update existing stream message with new text chunk
        if (streamMessage?.data) {
          (streamMessage.data as StreamingResponse).text += message.data?.text ?? '';
          streamMessagesRef.current[messageId] = streamMessage;
        }
      }

      // If streaming is completed, clean up the references
      if (isCompleted) handleStreamingState(messageId, true);

      return {
        message: streamMessage,
        isCompleted,
      };
    },
    [checkStreamStopped, handleStreamingState]
  );

  const handleQAStreamMessage = useCallback(
    (message: ChatResponse) => {
      if (message.type !== TemplateTypes.QAStream) return null;

      const messageId = message.id ?? '';
      const isCompleted = message?.completed === true;

      // Check if this stream was manually stopped
      if (checkStreamStopped(messageId, isCompleted)) return null;

      // Get existing stream message or create a new one
      let streamMessage = streamMessagesRef.current[messageId];

      if (!streamMessage) {
        handleStreamingState(messageId, false);

        streamMessage = {
          ...message,
          data: {
            ...message.data,
            text: message.data?.text ?? '',
            answers: message.data?.answers ?? [],
            title: message.data?.title ?? '',
          },
        };
        streamMessagesRef.current[messageId] = streamMessage;
      } else {
        // Update existing stream message with new text chunk
        if (streamMessage?.data) {
          (streamMessage.data as QAResponse).text += message.data?.text ?? '';
          (streamMessage.data as QAResponse).answers = message.data?.answers ?? [];
          (streamMessage.data as QAResponse).title = message.data?.title ?? '';
          streamMessagesRef.current[messageId] = streamMessage;
        }
      }

      if (messageId) {
        const sendArticleAnalyticsEvent = generateSendArticleAnalyticsEvent(messageId);
        setArticleAnalyticsEvents((prev) => {
          // Check if the event already exists to prevent duplicates
          const eventExists = prev.some((event) => event === sendArticleAnalyticsEvent);
          if (eventExists) {
            return prev;
          }
          return [...prev, sendArticleAnalyticsEvent];
        });
      }

      // If streaming is completed, clean up the references
      if (isCompleted) handleStreamingState(messageId, true);

      return {
        message: streamMessage,
        isCompleted,
      };
    },
    [checkStreamStopped, handleStreamingState, setArticleAnalyticsEvents]
  );

  const processStreamMessage = useCallback(
    (newMessage: ChatResponse, streamHandler: (message: ChatResponse) => any) => {
      const streamResult = streamHandler(newMessage);
      if (streamResult) {
        const { message: streamMessage, isCompleted } = streamResult;

        setMessages((prev) => {
          // Find if the stream message already exists in the messages list
          const index = prev.findIndex((msg) => msg.id === streamMessage?.id);

          if (index >= 0) {
            // Update existing message
            const updatedMessages = [...prev];
            updatedMessages[index] = streamMessage;
            return updatedMessages;
          }
          // Add new message
          const newMessages = prev.filter((message) => message.type !== 'loader');
          return [...newMessages, streamMessage];
        });

        // Skip processing other messages when streaming is completed
        if (isCompleted) return true;
      }
      return false;
    },
    [setMessages]
  );

  const eventHandler = useCallback(
    (data: { messages: ChatResponse[] }) => {
      const latestMessages = data.messages;
      if (!latestMessages?.length) return;

      const newMessage = latestMessages[0] as ChatResponse;
      if (!newMessage) return;

      // 'sender' is from livechat events only - Handle Livechat events
      if ('sender' in newMessage) {
        handleLiveChatEvents(newMessage);

        // Enable input field after receiving livechat events in case we turn off SearchIntent
        if (isDisabledMessageInput) setIsDisabledMessageInput(false);

        // Handle messages from admin
        if ((newMessage.from as LiveChatFromType) === 'admin') {
          // save the last livechatmessage to the context
          setLastLiveChatMessage(newMessage);

          // send READ event to Livechat
          if (isFocusInput) handleSendLiveChatDisplayEvent(LIVECHAT_DISPLAY_EVENT_TYPES.READ);
        }
      }

      // Skip activity messages
      if ((newMessage?.data as LiveChatMessageData)?.type === 'activity') return;

      // Skip messages if Livechat conversation is completed
      if (isCompletedLiveChatConversation) return;

      // Handle stream messages
      if (newMessage.type === TemplateTypes.Stream) {
        const shouldReturn = processStreamMessage(newMessage, handleStreamMessage);
        if (shouldReturn) return;
        return; // Always return early for stream messages to avoid processing them twice
      }

      // Handle stream QA messages
      if (newMessage.type === TemplateTypes.QAStream) {
        const shouldReturn = processStreamMessage(newMessage, handleQAStreamMessage);
        if (shouldReturn) return;
        return;
      }

      // Process non-stream messages
      const mergeMessages = latestMessages
        .map((message) => {
          // Handle input field state
          const isDisabled = isMessageInputFieldDisabled(message);
          setIsDisabledMessageInput(isDisabled);

          // Handle loader animation
          const { isKBSearchModeProcess, isKBRagModeProcess } =
            handleLoaderAnimationMessage(message);
          setIsKBCardMessaging(isKBSearchModeProcess || isKBRagModeProcess);

          // Process message based on its type
          switch (message.type) {
            case TemplateTypes.Text: {
              return {
                ...message,
                data: {
                  text: fixEscapeCharacters(message.data.text),
                  sources: isKBCardMessaging ? WIDGET_ENGINE_MESSAGE_SOURCES.kbCard : '',
                  from: message.from,
                  metadata: message.data?.metadata,
                },
              };
            }
            case TemplateTypes.Buttons: {
              const actionEventLists: string[] = [];
              message.data.actions.forEach((action) => {
                const actionEvent = generateActionButtonClickedEvent(action.id);
                actionEventLists.push(actionEvent);
              });
              setActionButtonEvents([...actionEventLists]);

              return {
                ...message,
                from: 'user',
                data: {
                  ...message.data,
                  actions: message.data.actions.map((action) => ({
                    ...action,
                    text: replaceLineBreaks(action.text),
                  })),
                },
              };
            }
            case TemplateTypes.Card: {
              return {
                ...message,
                from: 'user',
                data: {
                  ...message.data,
                  title: replaceLineBreaks(message.data.title),
                  subtitle: replaceLineBreaks(message.data.description),
                  actions: message.data.actions.map((action) => ({
                    ...action,
                    text: replaceLineBreaks(action.text),
                  })),
                },
              };
            }
            case TemplateTypes.Form: {
              const submitFormEvent = generateSubmitFormMessageEvent(message.id ?? '');
              setSubmitFormMessageEvents((prev) => [...prev, submitFormEvent]);
              break;
            }
            case TemplateTypes.QA:
            case TemplateTypes.HTML: {
              const sendArticleAnalyticsEvent = generateSendArticleAnalyticsEvent(message.id ?? '');
              setArticleAnalyticsEvents((prev) => [...prev, sendArticleAnalyticsEvent]);
              break;
            }
            case TemplateTypes.LiveChatTeam: {
              const selectLiveChatTeamEvents = message.data.teams.map((team) =>
                generateSelectTeamInLivechatModeEvent(team.id)
              );
              setSelectedLiveChatTeamEvents((prev) => [...prev, ...selectLiveChatTeamEvents]);
              break;
            }
            case TemplateTypes.QAStream:
            case TemplateTypes.Stream: {
              // Skip stream messages as they've already been handled above
              return null;
            }
          }

          return message;
        })
        .filter(Boolean) as ChatResponse[];

      if (!mergeMessages.length) return;

      setMessages((prev) => {
        const newMessages = prev.filter((message) => message.type !== 'loader');
        return [...newMessages, ...mergeMessages];
      });
    },
    [messages, isStreaming, streamingMessageId]
  );

  // Helper function for managing event listeners
  const createEventListenerEffect = useCallback(
    (events: string[], handler: (...args: any) => any, cleanup?: () => void) => {
      if (!events.length) return;

      events.forEach((event) => {
        createCustomEventListener(event, handler);
      });

      return () => {
        events.forEach((event) => {
          removeCustomEventListener(event, handler);
        });
        cleanup?.();
      };
    },
    []
  );

  useEffect(() => {
    return createEventListenerEffect(submitFormMessageEvents, handleSubmitFormData);
  }, [submitFormMessageEvents, handleSubmitFormData, createEventListenerEffect]);

  useEffect(() => {
    return createEventListenerEffect(actionButtonEvents, handleActionEvent);
  }, [actionButtonEvents, handleActionEvent, createEventListenerEffect]);

  useEffect(() => {
    return createEventListenerEffect(articleAnalyticsEvents, handleArticleClickedEvent, () => {
      articleIdViewedListRef.current = [];
    });
  }, [articleAnalyticsEvents, handleArticleClickedEvent, createEventListenerEffect]);

  useEffect(() => {
    return createEventListenerEffect(
      memoizedSelectLiveChatTeamEvents,
      handleSelectLiveChatTeamEvent
    );
  }, [memoizedSelectLiveChatTeamEvents, handleSelectLiveChatTeamEvent, createEventListenerEffect]);

  // Listen for stop streaming events
  useEffect(() => {
    const handleStopStreamingEvent = (event: any) => {
      const { messageId } = event.detail;
      if (messageId) {
        // Mark this stream as stopped
        stoppedStreamingRef.current[messageId] = true;
      }
    };

    createCustomEventListener(COMMON_MESSAGE_EVENTS.STOP_STREAMING, handleStopStreamingEvent);
    return () => {
      removeCustomEventListener(COMMON_MESSAGE_EVENTS.STOP_STREAMING, handleStopStreamingEvent);
    };
  }, []);

  useEffect(() => {
    retrieveWsToken();
  }, []);

  useEffect(() => {
    if (!wsToken) {
      return;
    }
    const centrifuge = new Centrifuge(AppConfig.WEBSOCKET_URL, {
      debug: true,
      token: wsToken,
      getToken: renewWsToken,
    });

    setCentrifugeInstance(centrifuge);

    // update decaHookDomain
    try {
      const domain = getHookDomain();
      AppService.getInstance(boxId).updateDecaHookDomain(domain);
    } catch (error) {
      console.log('Error getting domain from websocket url', error);
    }

    const channel = getChannelName(AppService.getInstance(boxId).decaIntegrationId, wsUserId);

    const subscription = centrifuge.newSubscription(channel);
    console.log('channel:', subscription.channel);

    subscription.on('publication', (ctx) => {
      if (eventHandlerRef.current) {
        eventHandlerRef.current(ctx.data);
      }
      console.log('centrifugo answer', ctx.channel, ctx.data);
    });

    subscription.subscribe();

    const livechatSubscription = centrifuge.newSubscription(LIVECHAT_USER_EVENTS_CHANNEL);

    livechatSubscription.on('publication', (ctx) => {
      if (eventHandlerRef.current) {
        eventHandlerRef.current(ctx.data);
      }
    });

    livechatSubscription.subscribe();

    centrifuge.connect();

    centrifuge.on('connected', (ctx) => {
      console.log('centrifugo connected');
    });

    centrifuge.on('error', (err) => {
      console.error('centrifugo error', err);
      // ref: https://github.com/centrifugal/centrifuge/blob/master/errors.go
      const errorCode = err?.error?.code;
      if ([101, 109, 110].includes(errorCode)) {
        retrieveWsToken();
      }
      // Chore: Disabled sentry for all apps: https://github.com/resola-ai/deca-apps/pull/2001/files
      // try {
      //   (window as any).__deca_sentry__.setExtra('error', err);
      //   (window as any).__deca_sentry__.setExtra('channel', channel);
      //   (window as any).__deca_sentry__.captureMessage(
      //     'Failed to connect with centrifugo (websocket)'
      //   );
      // } catch (error) {
      //   console.error('Error sending error to Sentry', error);
      // }
    });

    return () => {
      subscription.unsubscribe();
      livechatSubscription.unsubscribe();
      centrifuge.disconnect();
      // Clean up all streaming references on unmount
      streamMessagesRef.current = {};
      stoppedStreamingRef.current = {};
      console.log('unsubscribe & disconnect from centrifugo server');
    };
  }, [location, wsToken]);

  useEffect(() => {
    eventHandlerRef.current = eventHandler;
  }, [eventHandler]);

  useEffect(() => {
    routeRef.current = location.pathname;
  }, [location]);

  useEffect(() => {
    messagesRef.current = messages;
  }, [messages]);

  return {};
};

export type wsContextType = ReturnType<typeof useWs>;

const context = createContext<wsContextType | null>(null);

export const WSContextProvider: React.FC<WSContextProps> = ({ children, boxId }) => {
  const value = useWs(boxId);

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWSContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWSContext must be used inside WSContextProvider');
  }

  return value;
};
