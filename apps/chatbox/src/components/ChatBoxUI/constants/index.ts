import type { ChatBoxUIAppProps, LiveChatHeaderInfor, MessageType } from '../models';

export * from './events';
export * from './sample';
export * from './media';

export const chatBoxMainClassName = 'chatbox-main';

export const chatBoxUiInitialState: ChatBoxUIAppProps = {
  launcher: {
    sideSpacing: 20,
    bottomSpacing: 20,
    position: 'right',
    launcherIconUrl: 'svg/default-launcher.svg',
    launcherWidth: 60,
    launcherHeight: 60,
    closeButton: {
      show: false,
      width: 20,
      height: 20,
    },
    hideLauncherWhenOpenChatbox: false,
    shape: 'bubble',
    size: 60,
  },
  chatbox: {
    width: 360,
    height: 640,
    showChatBoxAsDefault: false,
    defaultPage: 'home',
    borderRadius: 12,
    displayComponents: ['home', 'message'],
    contentDisplay: 'top',
    themeColor: '',
  },
  home: {
    welcomeMessage: 'Hi there! \n What can we do to help you ?',
    background: {
      backgroundType: 'color',
      backgroundValue: '#FD7E14',
      fadeBackgroundToWhite: true,
    },
    styling: {
      paddingTop: '12px',
      paddingInline: '15px',
      bgColor: '#000',
    },
    titleStyling: {
      marginTop: '120px',
    },
    widgets: [
      {
        type: 'callToActionButton',
        data: {
          text: 'Book appointment',
          href: '',
          target: '',
        },
      },
    ],
    navigator: {
      text: 'Home',
    },
  },
  message: {
    styling: {
      bgColor: '#000',
    },
    background: {
      backgroundType: 'color',
      backgroundValue: '#FD7E14',
      fadeBackgroundToWhite: true,
    },
    emptyState: {
      icon: {
        width: 33.33,
        height: 33.33,
      },
      header: {
        text: 'Message',
      },
      content: {
        text: 'No Messages',
        description: 'Your message with the team will show here',
      },
      action: {
        text: 'Send us a message',
      },
    },
    navigator: {
      text: 'Message',
    },
  },
  brand: {
    actionColor: '#FD7E14',
    backgroundColor: '#87878D',
    useActionColorAsHyperlink: false,
  },
  general: {
    availability: true,
  },
  colorSettings: {
    buttonBackground: '#FD7E14',
    buttonText: '#E9E9E9',
    welcomeMessageColor: '#000000',
  },
  boxId: '',
};

export const WS_USER_ID_KEY = 'decaUserId';
export const DECA_LC_CONVERSATION_ID_KEY = 'decaCW_{boxId}_livechatConversationId';

export enum TemplateTypes {
  CallToActionButton = 'callToActionButton',
  SendingMessage = 'sendingMessage',
  QuickAnswer = 'quickAnswer',
  LargeImageLink = 'largeImageLink',
  SmallImageLink = 'smallImageLink',
  Image = 'image',
  Video = 'video',
  Document = 'document',
  Text = 'text',
  Card = 'card',
  Buttons = 'buttons',
  Loader = 'loader',
  Form = 'form',
  System = 'system',
  HTML = 'html',
  QA = 'qa',
  GroupMessagesByDate = 'groupMessagesByDate',
  LiveChatTeam = 'livechatTeam',
  Stream = 'stream',
  QAStream = 'qaStream',
}

export const ALLOWED_TYPES: MessageType[] = [
  TemplateTypes.Text,
  TemplateTypes.Image,
  TemplateTypes.Video,
  TemplateTypes.Document,
  TemplateTypes.Card,
  TemplateTypes.CallToActionButton,
  TemplateTypes.SmallImageLink,
  TemplateTypes.LargeImageLink,
  TemplateTypes.SendingMessage,
  TemplateTypes.QuickAnswer,
  TemplateTypes.Buttons,
  TemplateTypes.Loader,
  TemplateTypes.Form,
  TemplateTypes.System,
  TemplateTypes.HTML,
  TemplateTypes.QA,
  TemplateTypes.LiveChatTeam,
  TemplateTypes.Stream,
  TemplateTypes.QAStream,
];

export const MESSAGE_TYPES_INCLUDE_BUTTON = ['card', 'buttons'];

export const initLiveChatInfor: LiveChatHeaderInfor = {
  name: '', // display name of the operator
  picture: '', // picture of the operator
  description: '', // team name of the operator
  teamId: '',
  assigneeId: '',
};

export const BOX_MESSAGES_KEY = 'decaClientMessages_{userId}{boxId}{botId}';

export const DEMO_PREFIX = 'demo-';

export const OFFICIAL_PREFIX = 'DC';

export const LIVECHAT_CONVERSATION_LIMIT = 10;
export const LIVECHAT_MESSAGE_HISTORY_LIMIT = 30;
export const CHATBOT_MESSAGE_HISTORY_LIMIT = 30;
export const LIVECHAT_CONVERSATION_STATUS = {
  NEW: 'new',
  INPROGRESS: 'inProgress',
  COMPLETED: 'completed',
  INWRAPUP: 'inWrapUp',
};
export const DEFAULT_HEIGHT_OF_HEADER_BOTTOM_ELEMENT = 140;
export const DEFAULT_COLOR_NAVIGATOR = '#87878D';
export const WIDTH_FIT_CONTENT_FOR_MESSAGE_TYPES = ['text', 'html'];
export const CHATBOT_SYSTEM_TYPES = {
  FINISHED: 'chatbot.finished',
  EXECUTION: 'chatbot.execution',
};
export const LIVECHAT_TYPING_TIMEOUT = 3000;

export const LIVECHAT_USER_EVENTS_CHANNEL = 'livechat:user_activity_event';
