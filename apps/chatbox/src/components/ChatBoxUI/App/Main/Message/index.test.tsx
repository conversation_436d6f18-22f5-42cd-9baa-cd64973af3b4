import React from 'react';
import { screen, act, waitFor, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import Message from './index';
import { useChatBoxUIContext } from '@/components/ChatBoxUI/context/chatBoxUIContext';
import { useMedia } from '@/contexts/MediaContext';
import ApiService from '@/components/ChatBoxUI/services/api';
import { renderWithAllProviders } from '@/utils/unitTest';
import { LIVECHAT_DISPLAY_EVENT_TYPES } from '@/components/ChatBoxUI/constants';
import type { NavigatorType } from '@/components/ChatBoxUI/models';
import type { MediaContextType } from '@/contexts/MediaContext';

// Mock dependencies
vi.mock('@/components/ChatBoxUI/context/chatBoxUIContext');
vi.mock('@/contexts/MediaContext');
vi.mock('@/components/ChatBoxUI/services/api');
vi.mock('react-device-detect', () => ({
  isMobileOnly: false,
}));

// Mock child components
vi.mock('./ConversationHeader', () => ({
  default: () => <div data-testid='conversation-header'>Conversation Header</div>,
}));

vi.mock('./ChatHeader', () => ({
  default: () => <div data-testid='chat-header'>Chat Header</div>,
}));

vi.mock('./ChatState', () => ({
  default: () => <div data-testid='chat-state'>Chat State</div>,
}));

vi.mock('./EmptyState', () => ({
  default: () => <div data-testid='empty-state'>Empty State</div>,
}));

vi.mock('./ConversationState', () => ({
  default: () => <div data-testid='conversation-state'>Conversation State</div>,
}));

vi.mock('../MessageInput', () => ({
  default: () => <input data-testid='message_input_textbox' role='textbox' />,
}));

vi.mock('./TriggerFlowButton', () => ({
  default: () => <div data-testid='trigger-flow-button'>Trigger Flow Button</div>,
}));

// Add DragEvent and DataTransfer polyfills
global.DragEvent = class DragEvent extends Event {
  dataTransfer: DataTransfer | null;

  constructor(type: string, eventInitDict?: DragEventInit) {
    super(type, eventInitDict);
    this.dataTransfer = eventInitDict?.dataTransfer || null;
  }
} as any;

global.DataTransfer = class DataTransfer {
  dropEffect = 'none';
  effectAllowed = 'all';
  files: FileList = [] as any;
  items: DataTransferItemList = [] as any;
  types: string[] = [];

  clearData(format?: string): void {}
  getData(format: string): string {
    return '';
  }
  setData(format: string, data: string): void {}
  setDragImage(image: Element, x: number, y: number): void {}
} as any;

// Add this before the describe block
let mockDropHandler: ((files: File[]) => void) | null = null;

// Mock MessageFileDropzoneProvider at module level
vi.mock('@resola-ai/ui', () => ({
  MessageFileDropzoneProvider: ({
    children,
    onFilesDrop,
    rejectMessage,
    rejectSubMessage,
  }: any) => {
    // Store the handler for later use
    mockDropHandler = onFilesDrop;
    // Store reject messages for testing
    (global as any).mockRejectMessage = rejectMessage;
    (global as any).mockRejectSubMessage = rejectSubMessage;
    return children;
  },
}));

// Mock Tolgee with specific Japanese translations
vi.mock('@/tolgee', () => ({
  tolgee: {
    t: vi.fn((key: string) => {
      const translations: Record<string, string> = {
        'common:errors.fileValidationError':
          'ファイルサイズが大きすぎる（最大10MB）、サポートされていない形式、または5ファイル以上アップロードしています。もう一度お試しください。',
        'common:errors.checkFileLimits':
          'ファイルの種類、サイズ（最大10MB）、数（最大5ファイル）を確認してください',
        'common:errors.fetchConversationsFailed': '会話の取得に失敗しました',
        'common:errors.title': 'エラー',
      };
      return translations[key] || key;
    }),
    getLanguage: vi.fn(() => 'ja'),
    changeLanguage: vi.fn(),
    isLoaded: vi.fn(() => true),
    isLoading: vi.fn(() => false),
  },
}));

// Update the services-shared mock
vi.mock('@resola-ai/services-shared', () => ({
  logger: {
    error: vi.fn(),
  },
  createBaseConfig: vi.fn().mockReturnValue({
    apiUrl: 'test-api-url',
    apiKey: 'test-api-key',
    env: 'test',
    API_SERVER_URL: 'test-api-url',
    BASE_PATH: '/',
  }),
  axiosService: {
    init: vi.fn(),
    instance: {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    },
  },
  shareAppConfigService: {
    init: vi.fn(),
    getConfig: vi.fn(),
  },
  datadogService: {
    init: vi.fn(),
  },
  NotificationService: {
    sendNotification: vi.fn(),
  },
}));

describe('Message Component', () => {
  const mockProps = {
    launcher: {
      sideSpacing: 20,
      bottomSpacing: 20,
      position: 'right' as const,
      launcherIconUrl: 'test-icon.png',
      launcherWidth: 60,
      launcherHeight: 60,
      closeButton: {
        show: false,
        width: 20,
        height: 20,
      },
      hideLauncherWhenOpenChatbox: false,
      shape: 'bubble',
      size: 60,
    },
    chatbox: {
      width: 300,
      height: 500,
      borderRadius: 4,
      showChatBoxAsDefault: false,
      defaultPage: 'message' as NavigatorType,
      displayComponents: ['message'] as NavigatorType[],
      contentDisplay: 'top' as const,
    },
    home: {
      welcomeMessage: 'Welcome',
      background: {
        backgroundType: 'color' as const,
        backgroundValue: '#fff',
        fadeBackgroundToWhite: true,
      },
      styling: {},
      titleStyling: {},
      widgets: [],
      navigator: { text: 'Home' },
    },
    message: {
      styling: {},
      background: {
        backgroundType: 'color' as const,
        backgroundValue: '#fff',
        fadeBackgroundToWhite: true,
      },
      emptyState: {
        icon: { width: 30, height: 30 },
        header: { text: 'Empty' },
        content: { text: 'No messages', description: 'No messages yet' },
        action: { text: 'Send message' },
      },
      navigator: { text: 'Message' },
    },
    brand: {
      actionColor: '#000',
      backgroundColor: '#fff',
      useActionColorAsHyperlink: false,
    },
    boxId: 'test-box',
    colorSettings: {
      buttonBackground: '#000',
      buttonText: '#fff',
      welcomeMessageColor: '#000',
    },
    customChatboxSize: {
      customHeight: 400,
      customMaxHeight: 600,
    },
  };

  const mockContext = {
    currentPage: 'message' as NavigatorType,
    chatBoxUIState: true,
    chatBoxUIAvailability: true,
    clickLauncherHandler: vi.fn(),
    navigateHandler: vi.fn(),
    closeHandler: vi.fn(),
    updateNavigatorRef: vi.fn(),
    updateEmptyStateHeaderRef: vi.fn(),
    updateHeaderRef: vi.fn(),
    updateInputElmRef: vi.fn(),
    updateStartConversationButtonRef: vi.fn(),
    updateTriggerFlowButtonRef: vi.fn(),
    normalizeEventPayload: vi.fn((payload) => payload),
    dom: {
      navigatorData: { height: 0, width: 0 },
      emptyStateHeaderData: { height: 0, width: 0 },
      headerData: { height: 0, width: 0 },
      inputElmData: { height: 0, width: 0 },
      startConversationButtonData: { height: 0, width: 0 },
      triggerFlowButtonElmData: { height: 0, width: 0 },
    },
    isDisplayDouble: true,
    messageNavigator: {
      isChat: () => true,
      isConversation: () => false,
      goToConversation: vi.fn(),
      goToChat: vi.fn(),
    },
    urlGetLiveChatConversationList: 'test-url',
    actionColor: '#000',
    liveChatConnected: false,
    isFinishedChatbotConversation: false,
    boxId: 'test-box',
    isTurnOnLiveChatIntegration: true,
    handleSendLiveChatDisplayEvent: vi.fn(),
    setIsFocusInput: vi.fn(),
    lastLiveChatMessage: null,
    messages: [],
    setMessages: vi.fn(),
    messageScreen: 'chat',
    setMessageScreen: vi.fn(),
    connectLiveChatInfor: { name: '', picture: '', description: '', teamId: '', assigneeId: '' },
    setConnectLiveChatInfor: vi.fn(),
    currentConversationId: '',
    setCurrentConversationId: vi.fn(),
    isConversationAssignedToOperator: false,
    setIsConversationAssignedToOperator: vi.fn(),
    isDisabledMessageInput: false,
    setIsDisabledMessageInput: vi.fn(),
    conversationSelected: null,
    setConversationSelected: vi.fn(),
    livechatConversationList: [],
    setLivechatConversationList: vi.fn(),
    chatbotSystemState: { type: '', text: '' },
    setChatbotSystemState: vi.fn(),
    isKBCardMessaging: false,
    setIsKBCardMessaging: vi.fn(),
    actionButtonEvents: [],
    setActionButtonEvents: vi.fn(),
    submitFormMessageEvents: [],
    setSubmitFormMessageEvents: vi.fn(),
    chatBoxDisplayStyle: '',
    setChatBoxDisplayStyle: vi.fn(),
    articleAnalyticsEvents: [],
    setArticleAnalyticsEvents: vi.fn(),
    setCentrifugeInstance: vi.fn(),
    updateChatBoxDisplayStyle: vi.fn(),
    updateChatBoxUIState: vi.fn(),
    updateCurrentPage: vi.fn(),
    updateMessageScreen: vi.fn(),
    updateMessages: vi.fn(),
    updateLiveChatConnected: vi.fn(),
    updateConnectLiveChatInfor: vi.fn(),
    updateCurrentConversationId: vi.fn(),
    updateIsConversationAssignedToOperator: vi.fn(),
    updateIsDisabledMessageInput: vi.fn(),
    updateConversationSelected: vi.fn(),
    updateLivechatConversationList: vi.fn(),
    updateChatbotSystemState: vi.fn(),
    updateIsKBCardMessaging: vi.fn(),
    updateActionButtonEvents: vi.fn(),
    updateSubmitFormMessageEvents: vi.fn(),
    updateArticleAnalyticsEvents: vi.fn(),
    updateCentrifugeInstance: vi.fn(),
    updateIsFocusInput: vi.fn(),
    updateLastLiveChatMessage: vi.fn(),
    updateIsFinishedChatbotConversation: vi.fn(),
    updateIsTurnOnLiveChatIntegration: vi.fn(),
    updateBoxId: vi.fn(),
    updateActionColor: vi.fn(),
    updateUrlGetLiveChatConversationList: vi.fn(),
    updateMessageNavigator: vi.fn(),
    updateChatBoxUIAvailability: vi.fn(),
    wsUserId: 'test-user',
    userAgent: 'test-agent',
    botId: 'test-bot',
    organizationId: 'test-org',
    widgetsList: [],
    urlGetLiveChatHistoryMessages: 'test-url',
    urlGetChatbotHistoryMessages: 'test-url',
    isCompletedLiveChatConversation: false,
    isConnectNotAssignedOperatorYet: false,
    isConnectAssignedOperator: false,
    isDisconnectLiveChat: true,
    sourceInfo: {
      type: 'user',
      userId: 'test-user',
      meta: { userAgent: 'test-agent' },
    },
    articleAnalyticEventPayload: {
      event: '',
      requestId: 'test-id',
      organizationId: 'test-org',
      anonymousId: 'test-user',
      context: {
        userAgent: 'test-agent',
        source: 'chatbot',
      },
      properties: {
        itemId: '',
      },
      sentAt: expect.any(String),
    },
    voteCollectionStorageRef: { current: {} },
    navigatorRef: { current: null },
    emptyStateHeaderRef: { current: null },
    hideChatBox: vi.fn(),
    showChatBox: vi.fn(),
    disableChatBoxUI: vi.fn(),
    handleChatbotSystemMessages: vi.fn(),
    handleSendEventData: vi.fn(),
    handleSendArticleAnalyticEvent: vi.fn(),
    handleTriggerChatbotFlow: vi.fn(),
    handleGoToChat: vi.fn(),
    resetChatBoxState: vi.fn(),
    selectedLiveChatTeamEvents: [],
    setSelectedLiveChatTeamEvents: vi.fn(),
    centrifugeInstance: null,
    setLiveChatConnected: vi.fn(),
    isFocusInput: false,
    setLastLiveChatMessage: vi.fn(),
  };

  const mockMediaContext: MediaContextType = {
    isUploading: false,
    handleFileUploads: vi.fn(),
    createMessageWithMedia: vi.fn(),
    uploadedFiles: [],
    clearUploadedFiles: vi.fn(),
    clearProcessingFiles: vi.fn(),
    canAddMoreFiles: vi.fn(() => true),
  };

  const mockConversations = [
    {
      id: '1',
      name: 'Conversation 1',
      status: 'new',
      teamId: 'team1',
      enduser: {
        id: 'user1',
        platform: 'web',
        name: 'User 1',
      },
      enduserId: 'user1',
      lastMessage: {
        id: 'msg1',
        data: {
          id: 'msg1',
          text: 'Hello',
          type: 'text',
          metadata: {
            sender: {
              name: 'User 1',
              type: 'user',
              iconUrl: 'test.png',
            },
          },
        },
        sender: {
          name: 'User 1',
          id: 'user1',
          type: 'user',
          picture: 'test.png',
        },
        created: '2023-01-01',
      },
      lastEnduserMessage: 'Hello from user',
      lastOperatorMessage: 'Hi from operator',
      ocsChannel: 'channel1',
      integrationId: 'int1',
      unread: false,
      app: null,
      orgId: 'org1',
      created: '2023-01-01',
      updated: '2023-01-01',
      platform: {
        name: 'web',
        channelName: 'web',
      },
      fromChatbot: false,
      isNewConversation: false,
      isBookmark: false,
    },
  ];

  beforeEach(() => {
    vi.mocked(useChatBoxUIContext).mockReturnValue({
      ...mockContext,
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    });

    vi.mocked(useMedia).mockReturnValue(mockMediaContext);
    vi.mocked(ApiService.getLiveChatConversationList).mockResolvedValueOnce({
      data: {
        conversations: [
          {
            ...mockConversations[0],
            lastEnduserMessage: 'Hello from user',
            lastOperatorMessage: 'Hi from operator',
          },
        ],
      },
      status: 'success',
      pagination: {
        next: '',
      },
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders null when currentPage is not message', async () => {
    const testContext = {
      ...mockContext,
      currentPage: 'not-message' as NavigatorType,
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    };

    vi.mocked(useChatBoxUIContext).mockReturnValue(testContext);

    await act(async () => {
      const { container } = renderWithAllProviders(<Message {...mockProps} />);
      expect(container.firstChild).toBeNull();
    });
  });

  it('renders chat screen when live chat integration is disabled', async () => {
    const testContext = {
      ...mockContext,
      isTurnOnLiveChatIntegration: false,
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    };

    vi.mocked(useChatBoxUIContext).mockReturnValue(testContext);

    await act(async () => {
      renderWithAllProviders(<Message {...mockProps} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('message_input_textbox')).toBeInTheDocument();
      expect(screen.getByTestId('chat-header')).toBeInTheDocument();
      expect(screen.getByTestId('chat-state')).toBeInTheDocument();
    });
  });

  it('renders conversation screen when live chat integration is enabled', async () => {
    const testContext = {
      ...mockContext,
      messageNavigator: {
        isChat: () => false,
        isConversation: () => true,
        goToConversation: vi.fn(),
        goToChat: vi.fn(),
      },
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    };

    vi.mocked(useChatBoxUIContext).mockReturnValue(testContext);

    renderWithAllProviders(<Message {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByTestId('conversation-state')).toBeInTheDocument();
    });
  });

  it('fetches conversations when live chat integration is enabled', async () => {
    const mockConversations = [
      {
        id: '1',
        name: 'Conversation 1',
        status: 'new',
        teamId: 'team1',
        enduser: {
          id: 'user1',
          platform: 'web',
          name: 'User 1',
        },
        enduserId: 'user1',
        lastMessage: {
          id: 'msg1',
          data: {
            id: 'msg1',
            text: 'Hello',
            type: 'text',
            metadata: {
              sender: {
                name: 'User 1',
                type: 'user',
                iconUrl: 'test.png',
              },
            },
          },
          sender: {
            name: 'User 1',
            id: 'user1',
            type: 'user',
            picture: 'test.png',
          },
          created: '2023-01-01',
        },
        ocsChannel: 'channel1',
        integrationId: 'int1',
        unread: false,
        app: null,
        orgId: 'org1',
        created: '2023-01-01',
        updated: '2023-01-01',
        platform: {
          name: 'web',
          channelName: 'web',
        },
        fromChatbot: false,
        isNewConversation: false,
        isBookmark: false,
      },
    ];

    vi.mocked(useChatBoxUIContext).mockReturnValue({
      ...mockContext,
      messageNavigator: {
        isChat: () => false,
        isConversation: () => true,
        goToConversation: vi.fn(),
        goToChat: vi.fn(),
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    });

    vi.mocked(ApiService.getLiveChatConversationList).mockResolvedValue({
      data: {
        conversations: [
          {
            ...mockConversations[0],
            lastEnduserMessage: 'Hello from user',
            lastOperatorMessage: 'Hi from operator',
          },
        ],
      },
      status: 'success',
      pagination: {
        next: '',
      },
    });

    await act(async () => {
      renderWithAllProviders(<Message {...mockProps} />);
    });

    expect(ApiService.getLiveChatConversationList).toHaveBeenCalledWith('test-box', 'test-url');
    expect(screen.getByTestId('conversation-state')).toBeInTheDocument();
  });

  it('handles conversation fetch error', async () => {
    const mockError = new Error('Failed to fetch conversations');

    vi.mocked(useChatBoxUIContext).mockReturnValue({
      ...mockContext,
      messageNavigator: {
        isChat: () => false,
        isConversation: () => true,
        goToConversation: vi.fn(),
        goToChat: vi.fn(),
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    });

    vi.mocked(ApiService.getLiveChatConversationList).mockRejectedValueOnce(mockError);

    renderWithAllProviders(<Message {...mockProps} />);

    await waitFor(() => {
      expect(screen.getByTestId('empty-state')).toBeInTheDocument();
    });
  });

  it('shows trigger flow button when chat is finished', async () => {
    const testContext = {
      ...mockContext,
      isFinishedChatbotConversation: true,
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    };

    vi.mocked(useChatBoxUIContext).mockReturnValue(testContext);

    await act(async () => {
      renderWithAllProviders(<Message {...mockProps} />);
    });

    await waitFor(() => {
      expect(screen.getByTestId('trigger-flow-button')).toBeInTheDocument();
    });
  });

  it('handles focus and blur events', async () => {
    const testContext = {
      ...mockContext,
      liveChatConnected: true,
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    };

    vi.mocked(useChatBoxUIContext).mockReturnValue(testContext);

    await act(async () => {
      renderWithAllProviders(<Message {...mockProps} />);
    });

    const textbox = screen.getByTestId('message_input_textbox');

    await act(async () => {
      fireEvent.focus(textbox);
    });

    expect(testContext.setIsFocusInput).toHaveBeenCalledWith(true);
    expect(testContext.handleSendLiveChatDisplayEvent).toHaveBeenCalledWith(
      LIVECHAT_DISPLAY_EVENT_TYPES.FOCUS
    );

    await act(async () => {
      fireEvent.blur(textbox);
    });

    expect(testContext.setIsFocusInput).toHaveBeenCalledWith(false);
    expect(testContext.handleSendLiveChatDisplayEvent).toHaveBeenCalledWith(
      LIVECHAT_DISPLAY_EVENT_TYPES.UNFOCUS
    );
  });

  it('handles file drop events', async () => {
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });
    const testFiles = [file];

    renderWithAllProviders(<Message {...mockProps} />);

    // Ensure mockDropHandler is defined before using it
    expect(mockDropHandler).toBeDefined();
    if (!mockDropHandler) {
      throw new Error('mockDropHandler is not defined');
    }

    await act(async () => {
      if (mockDropHandler) {
        await mockDropHandler(testFiles);
      }
    });

    await waitFor(() => {
      expect(mockMediaContext.handleFileUploads).toHaveBeenCalledWith(testFiles, 'test-org', '');
    });
  });

  it('passes translated reject messages to MessageFileDropzoneProvider', async () => {
    renderWithAllProviders(<Message {...mockProps} />);

    await waitFor(() => {
      // Verify that the reject messages are passed to the provider
      // rejectMessage uses fileValidationError and rejectSubMessage uses checkFileLimits
      expect((global as any).mockRejectMessage).toBe(
        'ファイルサイズが大きすぎる（最大10MB）、サポートされていない形式、または5ファイル以上アップロードしています。もう一度お試しください。'
      );
      expect((global as any).mockRejectSubMessage).toBe(
        'ファイルの種類、サイズ（最大10MB）、数（最大5ファイル）を確認してください'
      );
    });
  });

  it('handles custom reject message configuration in chat screen', async () => {
    const testContext = {
      ...mockContext,
      isTurnOnLiveChatIntegration: false,
      closeHandler: vi.fn(),
      updateNavigatorRef: vi.fn(),
      updateEmptyStateHeaderRef: vi.fn(),
      updateHeaderRef: vi.fn(),
      updateInputElmRef: vi.fn(),
      updateStartConversationButtonRef: vi.fn(),
      updateTriggerFlowButtonRef: vi.fn(),
      dom: {
        navigatorData: { height: 0, width: 0 },
        emptyStateHeaderData: { height: 0, width: 0 },
        headerData: { height: 0, width: 0 },
        inputElmData: { height: 0, width: 0 },
        startConversationButtonData: { height: 0, width: 0 },
        triggerFlowButtonElmData: { height: 0, width: 0 },
      },
      isErrorFetchLCConversations: false,
      setIsErrorFetchLCConversations: vi.fn(),
      sendForceDisconnectLivechat: vi.fn(),
      isStreaming: false,
      setIsStreaming: vi.fn(),
      handleStopStreaming: vi.fn(),
      streamingMessageId: '',
      setStreamingMessageId: vi.fn(),
      forceDisconnectLivechatPayload: {
        action: 'forceDisconnectLivechat',
        events: [],
        userId: 'test-user',
        conversationId: '',
      },
      decaLCConversationIdKey: 'decaCW_test-box_livechatConversationId',
    };

    vi.mocked(useChatBoxUIContext).mockReturnValue(testContext);

    await act(async () => {
      renderWithAllProviders(<Message {...mockProps} />);
    });

    await waitFor(() => {
      // Verify the chat screen renders with custom reject messages
      expect(screen.getByTestId('message_input_textbox')).toBeInTheDocument();
      expect((global as any).mockRejectMessage).toBe(
        'ファイルサイズが大きすぎる（最大10MB）、サポートされていない形式、または5ファイル以上アップロードしています。もう一度お試しください。'
      );
      expect((global as any).mockRejectSubMessage).toBe(
        'ファイルの種類、サイズ（最大10MB）、数（最大5ファイル）を確認してください'
      );
    });
  });
});
