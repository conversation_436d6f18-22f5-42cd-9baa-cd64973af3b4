import React, { memo, useEffect } from 'react';
import { createStyles } from '@mantine/emotion';
import type { ChatBoxUIAppProps } from '@/components/ChatBoxUI/models';
import { useChatBoxUIContext } from '@/components/ChatBoxUI/context/chatBoxUIContext';
import { Button } from '@mantine/core';
import { IconSend } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { LIVECHAT_CONVERSATION_STATUS } from '@/components/ChatBoxUI/constants';

type ActionButtonProps = ChatBoxUIAppProps & {
  navigatorHeight: number;
};

const useStyles = createStyles((_, props: ActionButtonProps) => ({
  container: {
    position: 'absolute',
    width: '100%',
    display: 'flex',
    bottom: props.navigatorHeight,
  },
  customButton: {
    width: '100%',
    margin: '15px',
    borderRadius: '8px',
    paddingBlock: '10px',
    paddingInline: '20px',
    fontSize: '16px',
    lineHeight: '24.8px',
    fontWeight: 500,
    cursor: 'pointer',
    border: 'none',
    height: '36px',
    color: props.colorSettings.buttonText,
    backgroundColor: props.colorSettings.buttonBackground,
    '.mantine-Button-section': {
      marginRight: '10px',
    },
    '&:hover': {
      opacity: 0.9,
      backgroundColor: props.colorSettings.buttonBackground,
    },
  },
}));

const ActionButton: React.FC<ChatBoxUIAppProps> = (props) => {
  const { t } = useTranslate('messageClient');
  const {
    dom,
    messageNavigator,
    isDisplayDouble,
    updateStartConversationButtonRef,
    livechatConversationList,
    setLiveChatConnected,
    setIsConversationAssignedToOperator,
    setConnectLiveChatInfor,
    setCurrentConversationId,
    currentPage,
    isErrorFetchLCConversations,
    forceDisconnectLivechatPayload,
    handleSendEventData,
    decaLCConversationIdKey,
  } = useChatBoxUIContext();
  const containerRef = React.useRef<HTMLDivElement>(null);
  const navigatorHeight = dom.navigatorData?.height || 0;

  const customProps = {
    ...props,
    navigatorHeight: isDisplayDouble ? navigatorHeight : 0,
  };

  const { classes } = useStyles(customProps);

  useEffect(() => {
    updateStartConversationButtonRef(containerRef.current);
  }, [currentPage]);

  const handleGoToMessage = () => {
    const excludedStatuses = [
      LIVECHAT_CONVERSATION_STATUS.COMPLETED,
      LIVECHAT_CONVERSATION_STATUS.INWRAPUP,
    ];

    // Find the first conversation that is not in `excludedStatuses`
    const conversation = livechatConversationList.find(
      (item) => !excludedStatuses.includes(item.status)
    );

    if (!conversation) {
      handleNavigateToChatScreen(); // No valid conversation, just navigate to chat
      return;
    }

    setLiveChatConnected(true);
    setCurrentConversationId(conversation.id);

    const { status, assignee, team } = conversation;
    const assigneeData = {
      name: assignee?.displayName ?? '',
      picture: assignee?.picture ?? '',
      description: team?.name ?? '',
      teamId: team?.id ?? '',
      assigneeId: assignee?.id ?? '',
    };

    if (status === LIVECHAT_CONVERSATION_STATUS.NEW) {
      const isNoAssignee = !conversation.assigneeId || conversation.assigneeId.includes('UNKNOWN');
      setIsConversationAssignedToOperator(!isNoAssignee);
      if (!isNoAssignee) {
        setConnectLiveChatInfor(assigneeData);
      }
    } else if (status === LIVECHAT_CONVERSATION_STATUS.INPROGRESS) {
      setConnectLiveChatInfor(assigneeData);
      setIsConversationAssignedToOperator(true);
    }

    handleNavigateToChatScreen();
  };

  const handleNavigateToChatScreen = () => {
    if (isErrorFetchLCConversations) {
      handleSendEventData(forceDisconnectLivechatPayload);
      localStorage.removeItem(decaLCConversationIdKey);
    }

    messageNavigator.goToChat();
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      handleGoToMessage();
    }
  };

  return (
    <div
      className={classes.container}
      ref={containerRef}
      onClick={handleGoToMessage}
      onKeyDown={handleKeyDown}
      aria-label='Start action'
      data-testid='start_action_button'
    >
      <Button className={classes.customButton} leftSection={<IconSend size={'20px'} />}>
        {t('startActionText')}
      </Button>
    </div>
  );
};

export default memo(ActionButton);
