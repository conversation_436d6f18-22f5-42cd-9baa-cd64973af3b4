import { renderHook, act } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import useWindowSize from './useWindowSize';

describe('useWindowSize', () => {
  let mockAddEventListener: ReturnType<typeof vi.fn>;
  let mockRemoveEventListener: ReturnType<typeof vi.fn>;
  const originalInnerHeight = window.innerHeight;
  const originalAddEventListener = window.addEventListener;
  const originalRemoveEventListener = window.removeEventListener;

  beforeEach(() => {
    mockAddEventListener = vi.fn();
    mockRemoveEventListener = vi.fn();

    // Mock window methods and properties
    Object.defineProperty(window, 'innerHeight', {
      value: 768,
      writable: true,
      configurable: true,
    });

    window.addEventListener = mockAddEventListener;
    window.removeEventListener = mockRemoveEventListener;
  });

  afterEach(() => {
    // Restore original values
    Object.defineProperty(window, 'innerHeight', {
      value: originalInnerHeight,
      writable: true,
      configurable: true,
    });
    window.addEventListener = originalAddEventListener;
    window.removeEventListener = originalRemoveEventListener;
    vi.restoreAllMocks();
  });

  it('should return initial window inner height', () => {
    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(768);
  });

  it('should add resize event listener on mount', () => {
    renderHook(() => useWindowSize());

    expect(mockAddEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
    expect(mockAddEventListener).toHaveBeenCalledTimes(1);
  });

  it('should remove resize event listener on unmount', () => {
    const { unmount } = renderHook(() => useWindowSize());

    unmount();

    expect(mockRemoveEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
    expect(mockRemoveEventListener).toHaveBeenCalledTimes(1);
  });

  it('should update innerHeight when window is resized', () => {
    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(768);

    // Simulate window resize
    act(() => {
      window.innerHeight = 1024;
      // Get the resize handler that was registered
      const resizeHandler = mockAddEventListener.mock.calls[0][1];
      resizeHandler();
    });

    expect(result.current.innerHeight).toBe(1024);
  });

  it('should handle multiple resize events', () => {
    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(768);

    // First resize
    act(() => {
      window.innerHeight = 900;
      const resizeHandler = mockAddEventListener.mock.calls[0][1];
      resizeHandler();
    });

    expect(result.current.innerHeight).toBe(900);

    // Second resize
    act(() => {
      window.innerHeight = 600;
      const resizeHandler = mockAddEventListener.mock.calls[0][1];
      resizeHandler();
    });

    expect(result.current.innerHeight).toBe(600);
  });

  it('should handle zero height', () => {
    window.innerHeight = 0;

    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(0);
  });

  it('should handle very large height values', () => {
    window.innerHeight = 999999;

    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(999999);
  });

  it('should maintain the same event listener reference', () => {
    const { rerender } = renderHook(() => useWindowSize());

    const firstCall = mockAddEventListener.mock.calls[0];

    rerender();

    // Should not add more event listeners on re-render
    expect(mockAddEventListener).toHaveBeenCalledTimes(1);
    expect(mockRemoveEventListener).toHaveBeenCalledTimes(0);
  });

  it('should handle rapid resize events', () => {
    const { result } = renderHook(() => useWindowSize());

    const resizeHandler = mockAddEventListener.mock.calls[0][1];

    // Simulate rapid resize events
    act(() => {
      window.innerHeight = 800;
      resizeHandler();
      window.innerHeight = 900;
      resizeHandler();
      window.innerHeight = 1000;
      resizeHandler();
    });

    expect(result.current.innerHeight).toBe(1000);
  });

  it('should use correct event listener arguments', () => {
    renderHook(() => useWindowSize());

    expect(mockAddEventListener).toHaveBeenCalledWith('resize', expect.any(Function));

    const addCall = mockAddEventListener.mock.calls[0];
    expect(addCall[0]).toBe('resize');
    expect(typeof addCall[1]).toBe('function');
  });

  it('should use the same function reference for add and remove event listener', () => {
    const { unmount } = renderHook(() => useWindowSize());

    const addedHandler = mockAddEventListener.mock.calls[0][1];

    unmount();

    const removedHandler = mockRemoveEventListener.mock.calls[0][1];

    expect(addedHandler).toBe(removedHandler);
  });

  it('should handle decimal values for innerHeight', () => {
    window.innerHeight = 768.5;

    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(768.5);
  });

  it('should handle negative values gracefully', () => {
    window.innerHeight = -100;

    const { result } = renderHook(() => useWindowSize());

    expect(result.current.innerHeight).toBe(-100);
  });

  it('should properly clean up event listeners', () => {
    const { unmount } = renderHook(() => useWindowSize());

    // Verify that addEventListener was called
    expect(mockAddEventListener).toHaveBeenCalledWith('resize', expect.any(Function));

    unmount();

    // Verify that removeEventListener was called with the same handler
    expect(mockRemoveEventListener).toHaveBeenCalledWith('resize', expect.any(Function));
    expect(mockRemoveEventListener).toHaveBeenCalledTimes(1);
  });
});
