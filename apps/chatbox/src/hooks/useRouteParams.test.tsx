import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { useParams } from 'react-router-dom';
import useRouteParams from './useRouteParams';
import { useSettingsContext } from '@/contexts/SettingsContext';

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

// Mock SettingsContext
vi.mock('@/contexts/SettingsContext', () => ({
  useSettingsContext: vi.fn(),
}));

describe('useRouteParams', () => {
  const mockSetCurrentChatboxId = vi.fn();
  const mockUseParams = vi.mocked(useParams);
  const mockUseSettingsContext = vi.mocked(useSettingsContext);

  beforeEach(() => {
    vi.clearAllMocks();
    mockUseSettingsContext.mockReturnValue({
      setCurrentChatboxId: mockSetCurrentChatboxId,
    } as any);
  });

  it('should call setCurrentChatboxId with chatboxId from params', () => {
    const chatboxId = 'test-chatbox-123';
    mockUseParams.mockReturnValue({ chatboxId });

    renderHook(() => useRouteParams());

    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith(chatboxId);
  });

  it('should call setCurrentChatboxId with undefined when no chatboxId in params', () => {
    mockUseParams.mockReturnValue({});

    renderHook(() => useRouteParams());

    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith(undefined);
  });

  it('should call setCurrentChatboxId when chatboxId changes', () => {
    const initialChatboxId = 'initial-chatbox';
    const newChatboxId = 'new-chatbox';

    mockUseParams.mockReturnValue({ chatboxId: initialChatboxId });
    const { rerender } = renderHook(() => useRouteParams());

    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith(initialChatboxId);

    // Change the params
    mockUseParams.mockReturnValue({ chatboxId: newChatboxId });
    rerender();

    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith(newChatboxId);
    expect(mockSetCurrentChatboxId).toHaveBeenCalledTimes(2);
  });

  it('should handle empty string chatboxId', () => {
    mockUseParams.mockReturnValue({ chatboxId: '' });

    renderHook(() => useRouteParams());

    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith('');
  });

  it('should handle null chatboxId', () => {
    mockUseParams.mockReturnValue({ chatboxId: null as any });

    renderHook(() => useRouteParams());

    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith(null);
  });

  it('should re-run effect when setCurrentChatboxId reference changes', () => {
    const chatboxId = 'test-chatbox';
    const newSetCurrentChatboxId = vi.fn();

    mockUseParams.mockReturnValue({ chatboxId });

    const { rerender } = renderHook(() => useRouteParams());
    expect(mockSetCurrentChatboxId).toHaveBeenCalledWith(chatboxId);

    // Change the setCurrentChatboxId function reference
    mockUseSettingsContext.mockReturnValue({
      setCurrentChatboxId: newSetCurrentChatboxId,
    } as any);

    rerender();

    expect(newSetCurrentChatboxId).toHaveBeenCalledWith(chatboxId);
  });

  it('should not call setCurrentChatboxId if dependencies have not changed', () => {
    const chatboxId = 'test-chatbox';
    mockUseParams.mockReturnValue({ chatboxId });

    const { rerender } = renderHook(() => useRouteParams());

    expect(mockSetCurrentChatboxId).toHaveBeenCalledTimes(1);

    // Rerender without changing dependencies
    rerender();

    // Should not be called again since dependencies haven't changed
    expect(mockSetCurrentChatboxId).toHaveBeenCalledTimes(1);
  });
});
