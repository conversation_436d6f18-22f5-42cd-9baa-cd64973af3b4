import { renderHook, act } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import useObjectUrls from './useObjectUrls';

// Mock URL methods
const mockCreateObjectURL = vi.fn();
const mockRevokeObjectURL = vi.fn();

describe('useObjectUrls', () => {
  beforeEach(() => {
    // Mock URL.createObjectURL and URL.revokeObjectURL
    global.URL.createObjectURL = mockCreateObjectURL;
    global.URL.revokeObjectURL = mockRevokeObjectURL;

    // Reset mocks
    mockCreateObjectURL.mockClear();
    mockRevokeObjectURL.mockClear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should create and return object URL for a file', () => {
    const mockUrl = 'blob:http://localhost:3000/test-url';
    mockCreateObjectURL.mockReturnValue(mockUrl);

    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const { result } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;
    const url = getObjectUrl(file);

    expect(mockCreateObjectURL).toHaveBeenCalledWith(file);
    expect(url).toBe(mockUrl);
  });

  it('should return the same URL for the same file', () => {
    const mockUrl = 'blob:http://localhost:3000/test-url';
    mockCreateObjectURL.mockReturnValue(mockUrl);

    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const { result } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;
    const url1 = getObjectUrl(file);
    const url2 = getObjectUrl(file);

    expect(mockCreateObjectURL).toHaveBeenCalledTimes(1);
    expect(url1).toBe(url2);
    expect(url1).toBe(mockUrl);
  });

  it('should create different URLs for different files', () => {
    const mockUrl1 = 'blob:http://localhost:3000/test-url-1';
    const mockUrl2 = 'blob:http://localhost:3000/test-url-2';
    mockCreateObjectURL.mockReturnValueOnce(mockUrl1).mockReturnValueOnce(mockUrl2);

    const file1 = new File(['content 1'], 'test1.txt', { type: 'text/plain' });
    const file2 = new File(['content 2'], 'test2.txt', { type: 'text/plain' });
    const { result } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;
    const url1 = getObjectUrl(file1);
    const url2 = getObjectUrl(file2);

    expect(mockCreateObjectURL).toHaveBeenCalledTimes(2);
    expect(mockCreateObjectURL).toHaveBeenNthCalledWith(1, file1);
    expect(mockCreateObjectURL).toHaveBeenNthCalledWith(2, file2);
    expect(url1).toBe(mockUrl1);
    expect(url2).toBe(mockUrl2);
  });

  it('should throw error when trying to get URL after unmounting', () => {
    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const { result, unmount } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;
    unmount();

    expect(() => getObjectUrl(file)).toThrow('Cannot getObjectUrl while unmounted');
  });

  it('should revoke all object URLs on cleanup', () => {
    const mockUrl1 = 'blob:http://localhost:3000/test-url-1';
    const mockUrl2 = 'blob:http://localhost:3000/test-url-2';
    mockCreateObjectURL.mockReturnValueOnce(mockUrl1).mockReturnValueOnce(mockUrl2);

    const file1 = new File(['content 1'], 'test1.txt', { type: 'text/plain' });
    const file2 = new File(['content 2'], 'test2.txt', { type: 'text/plain' });
    const { result, unmount } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;
    getObjectUrl(file1);
    getObjectUrl(file2);

    unmount();

    expect(mockRevokeObjectURL).toHaveBeenCalledTimes(2);
    expect(mockRevokeObjectURL).toHaveBeenCalledWith(mockUrl1);
    expect(mockRevokeObjectURL).toHaveBeenCalledWith(mockUrl2);
  });

  it('should handle empty map state correctly', () => {
    const mockUrl = 'blob:http://localhost:3000/test-url';
    mockCreateObjectURL.mockReturnValue(mockUrl);

    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });
    const { result } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;

    // First call should create and return URL
    const url = getObjectUrl(file);
    expect(url).toBe(mockUrl);
    expect(mockCreateObjectURL).toHaveBeenCalledWith(file);
  });

  it('should handle file object with different properties', () => {
    const mockUrl = 'blob:http://localhost:3000/image-url';
    mockCreateObjectURL.mockReturnValue(mockUrl);

    const imageFile = new File(['image data'], 'image.jpg', {
      type: 'image/jpeg',
      lastModified: Date.now(),
    });
    const { result } = renderHook(() => useObjectUrls());

    const getObjectUrl = result.current;
    const url = getObjectUrl(imageFile);

    expect(mockCreateObjectURL).toHaveBeenCalledWith(imageFile);
    expect(url).toBe(mockUrl);
  });

  it('should maintain separate maps across multiple hook instances', () => {
    const mockUrl1 = 'blob:http://localhost:3000/instance1-url';
    const mockUrl2 = 'blob:http://localhost:3000/instance2-url';
    mockCreateObjectURL.mockReturnValueOnce(mockUrl1).mockReturnValueOnce(mockUrl2);

    const file = new File(['test content'], 'test.txt', { type: 'text/plain' });

    const { result: result1 } = renderHook(() => useObjectUrls());
    const { result: result2 } = renderHook(() => useObjectUrls());

    const getObjectUrl1 = result1.current;
    const getObjectUrl2 = result2.current;

    const url1 = getObjectUrl1(file);
    const url2 = getObjectUrl2(file);

    expect(mockCreateObjectURL).toHaveBeenCalledTimes(2);
    expect(url1).toBe(mockUrl1);
    expect(url2).toBe(mockUrl2);
  });
});
