import { renderHook } from '@testing-library/react';
import { describe, expect, it, vi, beforeEach, afterEach } from 'vitest';
import useSafariConfig from './useSafariConfig';

describe('useSafariConfig', () => {
  const originalUserAgent = navigator.userAgent;

  beforeEach(() => {
    // Mock navigator.userAgent
    Object.defineProperty(window.navigator, 'userAgent', {
      value: '',
      writable: true,
      configurable: true,
    });
  });

  afterEach(() => {
    // Restore original userAgent
    Object.defineProperty(window.navigator, 'userAgent', {
      value: originalUserAgent,
      writable: true,
      configurable: true,
    });
    vi.restoreAllMocks();
  });

  it('should return default config for non-Safari browsers', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current).toEqual({
      isSafari: false,
      isMobile: false,
      scrollThreshold: 0,
      tolerance: 0,
      delay: 100,
      autoScrollDelay: 0,
    });
  });

  it('should detect Safari on macOS', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current).toEqual({
      isSafari: true,
      isMobile: false,
      scrollThreshold: 20,
      tolerance: 5,
      delay: 150,
      autoScrollDelay: 0,
    });
  });

  it('should detect Safari on iPhone', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current).toEqual({
      isSafari: true,
      isMobile: true,
      scrollThreshold: 50,
      tolerance: 10,
      delay: 200,
      autoScrollDelay: 50,
    });
  });

  it('should detect Safari on iPad', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current).toEqual({
      isSafari: true,
      isMobile: true,
      scrollThreshold: 50,
      tolerance: 10,
      delay: 200,
      autoScrollDelay: 50,
    });
  });

  it('should detect Safari on iPod', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (iPod touch; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current).toEqual({
      isSafari: true,
      isMobile: true,
      scrollThreshold: 50,
      tolerance: 10,
      delay: 200,
      autoScrollDelay: 50,
    });
  });

  it('should not detect Chrome as Safari', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.isSafari).toBe(false);
    expect(result.current.isMobile).toBe(false);
  });

  it('should not detect Android Chrome as Safari', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.isSafari).toBe(false);
    expect(result.current.isMobile).toBe(false);
  });

  it('should handle edge case user agents', () => {
    // Empty user agent
    (window.navigator as any).userAgent = '';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.isSafari).toBe(false);
    expect(result.current.isMobile).toBe(false);
  });

  it('should handle malformed user agents', () => {
    (window.navigator as any).userAgent = 'Invalid User Agent String';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.isSafari).toBe(false);
    expect(result.current.isMobile).toBe(false);
  });

  it('should correctly identify Safari with additional text in user agent', () => {
    (window.navigator as any).userAgent =
      'Custom/1.0 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15 CustomApp/2.0';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.isSafari).toBe(true);
    expect(result.current.isMobile).toBe(false);
  });

  it('should provide correct configuration values for desktop Safari', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.scrollThreshold).toBe(20);
    expect(result.current.tolerance).toBe(5);
    expect(result.current.delay).toBe(150);
    expect(result.current.autoScrollDelay).toBe(0);
  });

  it('should provide correct configuration values for mobile Safari', () => {
    (window.navigator as any).userAgent =
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';

    const { result } = renderHook(() => useSafariConfig());

    expect(result.current.scrollThreshold).toBe(50);
    expect(result.current.tolerance).toBe(10);
    expect(result.current.delay).toBe(200);
    expect(result.current.autoScrollDelay).toBe(50);
  });
});
