import type { StatusColor } from '@resola-ai/ui';
import { FieldTypes } from '@resola-ai/ui/components';
import { themeConfigurations } from '@resola-ai/ui/constants';
import {
  IconArrowRightCircle,
  IconAt,
  IconBrandHipchat,
  IconCalendarDue,
  IconCalendarTime,
  IconCircleChevronDown,
  IconCoin,
  IconFile,
  IconFileStack,
  IconFloatLeft,
  IconHash,
  IconLetterCase,
  IconLink,
  IconListCheck,
  IconNotes,
  IconNumbers,
  IconPercentage,
  IconPhone,
  IconPhoto,
  IconSquareCheck,
  IconStar,
  IconUsers,
} from '@tabler/icons-react';

export const IconList = [
  { value: 'users', Icon: IconUsers },
  { value: 'file', Icon: IconFile },
  { value: 'fileStack', Icon: IconFileStack },
  { value: 'note', Icon: IconNotes },
  { value: 'star', Icon: IconStar },
];

export const OptionsName = 'options';

export const Colors: StatusColor[] = [
  'purple',
  'red',
  'blue',
  'teal',
  'yellow',
  'green',
  'violet',
  'pink',
  'navy',
  'grey',
];

export const ColorCodes = {
  violet: {
    color: themeConfigurations.colors?.decaViolet?.[9],
    backgroundColor: themeConfigurations.colors?.decaViolet?.[0],
  },
  blue: {
    color: themeConfigurations.colors?.decaBlue?.[9],
    backgroundColor: themeConfigurations.colors?.decaBlue?.[0],
  },
  green: {
    color: themeConfigurations.colors?.decaGreen?.[9],
    backgroundColor: themeConfigurations.colors?.decaGreen?.[0],
  },
  yellow: {
    color: themeConfigurations.colors?.decaYellow?.[9],
    backgroundColor: themeConfigurations.colors?.decaYellow?.[0],
  },
  purple: {
    color: themeConfigurations.colors?.decaPurple?.[9],
    backgroundColor: themeConfigurations.colors?.decaPurple?.[1],
  },
  red: {
    color: themeConfigurations.colors?.decaRed?.[9],
    backgroundColor: themeConfigurations.colors?.decaRed?.[1],
  },
  grey: {
    color: themeConfigurations.colors?.decaGrey?.[7],
    backgroundColor: themeConfigurations.colors?.decaLight?.[1],
  },
  teal: {
    color: themeConfigurations.colors?.decaTeal?.[9],
    backgroundColor: themeConfigurations.colors?.decaTeal?.[1],
  },
  navy: {
    color: themeConfigurations.colors?.decaNavy?.[4],
    backgroundColor: themeConfigurations.colors?.decaNavy?.[0],
  },
  pink: {
    color: themeConfigurations.colors?.decaPink?.[9],
    backgroundColor: themeConfigurations.colors?.decaPink?.[1],
  },
};

export const OptionsOrder = {
  MANUAL: 'manual',
  ASC: 'asc',
  DESC: 'desc',
};

export const FormatDate = {
  ja: 'YYYY/MM/DD',
  en: 'MM/DD/YYYY',
  eu: 'DD/MM/YYYY',
  long: 'MMMM D, YYYY',
  longJa: 'YYYY年MM月DD日',
};

export const FormatTime = {
  '12h': 'hh:mm A',
  '24h': 'HH:mm',
};

export const ColumnWidth = {
  [FieldTypes.SINGLE_LINE_TEXT]: 280,
  [FieldTypes.LONG_TEXT]: 280,
  [FieldTypes.PHONE_NUMBER]: 280,
  [FieldTypes.EMAIL]: 280,
  [FieldTypes.URL]: 280,
  [FieldTypes.MODIFIED_TIME]: 360,
  [FieldTypes.CREATED_TIME]: 360,
  [FieldTypes.CREATED_BY]: 280,
  [FieldTypes.MODIFIED_BY]: 280,
  [FieldTypes.DATETIME]: 360,
  [FieldTypes.MULTI_SELECT]: 360,
  [FieldTypes.SINGLE_SELECT]: 280,
  [FieldTypes.CHECKBOX]: 280,
  [FieldTypes.NUMBER]: 280,
  [FieldTypes.AUTONUMBER]: 280,
  [FieldTypes.CURRENCY]: 150,
  [FieldTypes.PERCENT]: 150,
  [FieldTypes.IMAGE]: 280,
  [FieldTypes.RELATIONSHIP]: 150,
  [FieldTypes.LINE]: 280,
};

export const ColumnIcon = {
  [FieldTypes.SINGLE_LINE_TEXT]: IconLetterCase,
  [FieldTypes.LONG_TEXT]: IconFloatLeft,
  [FieldTypes.PHONE_NUMBER]: IconPhone,
  [FieldTypes.EMAIL]: IconAt,
  [FieldTypes.URL]: IconLink,
  [FieldTypes.CREATED_TIME]: IconCalendarDue,
  [FieldTypes.MODIFIED_TIME]: IconCalendarDue,
  [FieldTypes.CREATED_BY]: IconUsers,
  [FieldTypes.MODIFIED_BY]: IconUsers,
  [FieldTypes.DATETIME]: IconCalendarTime,
  [FieldTypes.MULTI_SELECT]: IconListCheck,
  [FieldTypes.SINGLE_SELECT]: IconCircleChevronDown,
  [FieldTypes.CHECKBOX]: IconSquareCheck,
  [FieldTypes.NUMBER]: IconHash,
  [FieldTypes.AUTONUMBER]: IconNumbers,
  [FieldTypes.CURRENCY]: IconCoin,
  [FieldTypes.PERCENT]: IconPercentage,
  [FieldTypes.IMAGE]: IconPhoto,
  [FieldTypes.RELATIONSHIP]: IconArrowRightCircle,
  [FieldTypes.LINE]: IconBrandHipchat,
};

export const Currency = {
  usd: 'USD',
  yen: 'JPY',
};

export const CurrencySymbol = {
  [Currency.usd]: '$',
  [Currency.yen]: '¥',
};

export const CurrencyFormatTypes = {
  commaOnly: 'commaOnly',
  commaPeriod: 'commaPeriod',
  periodComma: 'periodComma',
  spaceComma: 'spaceComma',
  spacePeriod: 'spacePeriod',
  periodOnly: 'periodOnly',
};

export const CurrencyFormat = {
  [CurrencyFormatTypes.commaOnly]: { thousand: ',', decimal: '' },
  [CurrencyFormatTypes.commaPeriod]: { thousand: ',', decimal: '.' },
  [CurrencyFormatTypes.periodComma]: { thousand: '.', decimal: ',' },
  [CurrencyFormatTypes.spaceComma]: { thousand: ' ', decimal: ',' },
  [CurrencyFormatTypes.spacePeriod]: { thousand: ' ', decimal: '.' },
  [CurrencyFormatTypes.periodOnly]: { thousand: '', decimal: '.' },
};

export const FormatPhone = {
  jp: 'xxx-xxxx-xxxx',
  en: 'xx-xxx-xxx-xxx',
  northUS: '+x-xxx-xxx-xxxx',
  general: '+x-(xxx)-xxx-xxxx',
};

export const PREFERENCES = {
  activities: 'activities',
  identities: 'identities',
  files: 'files',
  history: 'history',
  longText: 'longText',
  customObject: 'customObject',
};

export const IgnoreActivityActorType = ['mail', 'sms', 'line'];

export const UN_ADDROW_FIELDS = [
  FieldTypes.CREATED_TIME,
  FieldTypes.MODIFIED_TIME,
  FieldTypes.CREATED_BY,
  FieldTypes.MODIFIED_BY,
];

export type MessageType = 'email' | 'sms' | 'line';
export const CUSTOM_ACTION_COLORS = ['red', 'violet', 'green', 'yellow', 'purple', 'grey'];
export const UNGROUPED_ID = 'default';
export const UNSUPPORTED_MENTION_TYPES = [
  FieldTypes.RELATIONSHIP,
  FieldTypes.LINE,
  FieldTypes.IMAGE,
  FieldTypes.CHECKBOX,
  FieldTypes.SINGLE_SELECT,
  FieldTypes.MULTI_SELECT,
  FieldTypes.MODIFIED_TIME,
  FieldTypes.CREATED_TIME,
  FieldTypes.CREATED_BY,
  FieldTypes.MODIFIED_BY,
  FieldTypes.LONG_TEXT,
];

export const DEFAULT_PROFILE_SETTINGS = [
  {
    type: 'activities',
    enabled: false,
  },
  {
    type: 'identities',
    enabled: false,
  },
  {
    type: 'attachments',
    enabled: false,
  },
  {
    type: 'tasks',
    enabled: false,
  },
  {
    type: 'pinLongText',
    enabled: false,
  },
  {
    type: 'pinCustomObject',
    enabled: false,
  },
];

export const NOT_ALLOW_IMPORT_FIELDS = [
  FieldTypes.CREATED_TIME,
  FieldTypes.MODIFIED_TIME,
  FieldTypes.CREATED_BY,
  FieldTypes.MODIFIED_BY,
  FieldTypes.AUTONUMBER,
  FieldTypes.RELATIONSHIP,
  FieldTypes.SINGLE_SELECT,
  FieldTypes.MULTI_SELECT,
];
