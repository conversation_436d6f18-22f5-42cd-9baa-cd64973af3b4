import { axiosService, logger } from '../../../../../packages/services';
import type { IImportCSV } from '../../models';

export const TasksAPI = {
  exportCSV: async (wsId: string, objId: string, viewId: string) => {
    try {
      const response = await axiosService.instance.post(`data/${wsId}/${objId}/export`, {
        viewId,
      });
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  importCSV: async (wsId: string, importPayload: IImportCSV) => {
    try {
      const response = await axiosService.instance.post(
        `data/${wsId}/initiateImport`,
        importPayload
      );
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },

  confirmImport: async (
    wsId: string,
    taskId: string,
    objectId: string,
    fields?: { [key: string]: string }
  ) => {
    try {
      const response = await axiosService.instance.post(`data/${wsId}/confirmImport`, {
        taskId,
        objectId,
        fields,
      });
      return response.data.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
