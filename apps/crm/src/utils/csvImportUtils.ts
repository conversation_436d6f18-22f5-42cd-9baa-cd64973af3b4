import { NOT_ALLOW_IMPORT_FIELDS } from '@/constants/workspace';
import { FieldTypes } from '@resola-ai/ui/components';

export interface FieldMapping {
  csvField: string;
  systemFieldType: string | null;
  systemFieldId: string | null;
  systemFieldName: string | null;
  skipped: boolean;
  isExistingField: boolean;
}

/**
 * Auto-match CSV field names to appropriate field types based on common patterns
 */
export const getAutoMatchedFieldType = (fieldName: string): string => {
  const lowerFieldName = fieldName.toLowerCase().trim();

  // Email patterns
  const emailPatterns = ['email', 'mail'];
  if (emailPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.EMAIL;
  }

  // Phone patterns
  const phonePatterns = ['phone', 'tel', 'mobile', 'cellular'];
  if (phonePatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.PHONE_NUMBER;
  }

  // URL patterns
  const urlPatterns = ['url', 'website', 'link', 'site'];
  if (urlPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.URL;
  }

  // Percent patterns
  const percentPatterns = ['percent', 'percentage', 'rate', 'ratio', '%'];
  if (percentPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.PERCENT;
  }

  // Currency patterns
  const currencyPatterns = [
    'currency',
    'money',
    'dollar',
    'yen',
    'euro',
    'salary',
    'wage',
    'fee',
    'payment',
    'revenue',
    'income',
  ];
  if (currencyPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.CURRENCY;
  }

  // Number patterns
  const numberPatterns = [
    'number',
    'num',
    'count',
    'quantity',
    'amount',
    'price',
    'cost',
    'total',
    'sum',
    'value',
  ];
  if (numberPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.NUMBER;
  }

  // Date patterns
  const datePatterns = [
    'date',
    'time',
    'birthday',
    'birth',
    'anniversary',
    'deadline',
    'due',
    'start',
    'end',
    'schedule',
  ];
  if (datePatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.DATETIME;
  }

  // Line patterns
  const linePatterns = ['line', 'lineid', 'chat', 'messaging'];
  if (linePatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.LINE;
  }

  // Multi select patterns (check before single select to avoid conflicts)
  const multiSelectPatterns = [
    'tags',
    'categories',
    'selections',
    'options',
    'choices',
    'multiple',
    'multi',
    'list',
  ];
  if (multiSelectPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.MULTI_SELECT;
  }

  // Single select patterns
  const singleSelectPatterns = [
    'status',
    'category',
    'type',
    'selection',
    'option',
    'choice',
    'pick',
    'dropdown',
    'select',
  ];
  if (singleSelectPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.SINGLE_SELECT;
  }

  // Address patterns
  const addressPatterns = [
    'address',
    'street',
    'city',
    'state',
    'zip',
    'postal',
    'country',
    'location',
  ];
  if (addressPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.SINGLE_LINE_TEXT;
  }

  // Name patterns
  const namePatterns = ['name', 'title', 'label', 'subject', 'company', 'organization'];
  if (namePatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.SINGLE_LINE_TEXT;
  }

  // Long text patterns
  const longTextPatterns = [
    'description',
    'note',
    'comment',
    'remarks',
    'details',
    'bio',
    'summary',
    'content',
  ];
  if (longTextPatterns.some((pattern) => lowerFieldName.includes(pattern))) {
    return FieldTypes.LONG_TEXT;
  }

  // Checkbox patterns
  const checkboxPatterns = [
    'active',
    'enabled',
    'published',
    'verified',
    'confirmed',
    'approved',
    'completed',
    'done',
    'finished',
    'closed',
    'checkbox',
  ];
  const checkboxPrefixes = ['is', 'has', 'can', 'should'];

  if (
    checkboxPatterns.some((pattern) => lowerFieldName.includes(pattern)) ||
    checkboxPrefixes.some((prefix) => lowerFieldName.startsWith(prefix))
  ) {
    return FieldTypes.CHECKBOX;
  }

  // Default to single line text for other fields
  return FieldTypes.SINGLE_LINE_TEXT;
};

/**
 * Generate CSV field names from CSV data
 */
export const getCsvFields = (csvData: string[][]): string[] => {
  if (!Array.isArray(csvData) || csvData.length === 0) return [];
  return Array.isArray(csvData[0]) ? csvData[0] : [];
};

/**
 * Match CSV field names to existing object fields first, then fall back to auto-matching
 */
export const matchCsvFieldsToObjectFields = (
  csvFields: string[],
  objectFields: Array<{ id: string; name?: string; type: string }> = []
): FieldMapping[] => {
  // Ensure both csvFields and objectFields are arrays
  const safeCsvFields = Array.isArray(csvFields) ? csvFields : [];
  const safeObjectFields = Array.isArray(objectFields) ? objectFields : [];

  return safeCsvFields.map((csvField) => {
    const lowerCsvField = csvField.toLowerCase().trim();

    // First, try to match with existing object fields by name
    const matchedField = safeObjectFields.find((field) => {
      const fieldName = field.name?.toLowerCase().trim() ?? '';
      return (
        fieldName === lowerCsvField ||
        fieldName.includes(lowerCsvField) ||
        lowerCsvField.includes(fieldName)
      );
    });

    if (matchedField) {
      return {
        csvField,
        systemFieldType: matchedField.type,
        systemFieldId: matchedField.id,
        systemFieldName: matchedField.name ?? null,
        skipped: false,
        isExistingField: true,
      };
    }

    // Fall back to auto-matching by field type based on field name
    const autoMatchedType = getAutoMatchedFieldType(csvField);

    return {
      csvField,
      systemFieldType: autoMatchedType,
      systemFieldId: null,
      systemFieldName: null,
      skipped: false,
      isExistingField: false,
    };
  });
};

/**
 * Create initial field mappings with auto-matching against existing object fields
 */
export const createInitialFieldMappings = (
  csvFields: string[],
  objectFields: Array<{ id: string; name?: string; type: string }> = []
): FieldMapping[] => {
  return matchCsvFieldsToObjectFields(csvFields, objectFields);
};

/**
 * Calculate total number of records to import
 */
export const calculateTotalRecords = (csvData: string[][]): number => {
  return csvData.length > 0 ? csvData.length : 0;
};

/**
 * Filter out automatically created fields from field type options
 */
export const filterAutoCreatedFields = (fieldTypeOptions: any[]): any[] => {
  return fieldTypeOptions.filter((option) => !NOT_ALLOW_IMPORT_FIELDS.includes(option.value));
};

/**
 * Update field mapping for a specific CSV field
 */
export const updateFieldMapping = (
  fieldMappings: FieldMapping[],
  csvField: string,
  updates: Partial<FieldMapping>
): FieldMapping[] => {
  return fieldMappings.map((mapping) => {
    if (mapping.csvField === csvField) {
      // If changing to an existing field, update the related properties
      if (updates.systemFieldId && updates.systemFieldType) {
        return {
          ...mapping,
          ...updates,
          isExistingField: true,
          systemFieldName: updates.systemFieldName ?? mapping.systemFieldName,
        };
      }
      // If changing to a new field type, reset existing field properties
      if (updates.systemFieldType && !updates.systemFieldId) {
        return {
          ...mapping,
          ...updates,
          systemFieldId: null,
          systemFieldName: null,
          isExistingField: false,
        };
      }
      return { ...mapping, ...updates };
    }
    return mapping;
  });
};

/**
 * Get field mapping by CSV field name
 */
export const getFieldMappingByField = (
  fieldMappings: FieldMapping[],
  csvField: string
): FieldMapping | undefined => {
  return fieldMappings.find((mapping) => mapping.csvField === csvField);
};

/**
 * Get active (non-skipped) field mappings
 */
export const getActiveFieldMappings = (fieldMappings: FieldMapping[]): FieldMapping[] => {
  return fieldMappings.filter((mapping) => !mapping.skipped);
};

/**
 * Get skipped field mappings
 */
export const getSkippedFieldMappings = (fieldMappings: FieldMapping[]): FieldMapping[] => {
  return fieldMappings.filter((mapping) => mapping.skipped);
};

/**
 * Validate field mappings to ensure required fields are mapped
 */
export const validateFieldMappings = (
  fieldMappings: FieldMapping[]
): {
  isValid: boolean;
  errors: string[];
} => {
  const errors: string[] = [];
  const activeFieldMappings = getActiveFieldMappings(fieldMappings);

  // Check if at least one field is mapped
  if (activeFieldMappings.length === 0) {
    errors.push('At least one field must be mapped for import');
  }

  // Add validation for unique fields if needed
  // For now, we allow multiple fields to map to the same type

  return {
    isValid: errors.length === 0,
    errors,
  };
};

/**
 * Create field creation payload from active field mappings
 */
export const createFieldPayload = (fieldMappings: FieldMapping[]) => {
  const activeFieldMappings = getActiveFieldMappings(fieldMappings);
  return activeFieldMappings
    .filter((mapping) => !mapping.systemFieldId)
    .map((mapping) => ({
      name: mapping.systemFieldName ?? mapping.csvField,
      type: mapping.systemFieldType ?? 'text',
    }));
};

/**
 * Map fields to CSV columns for both system fields and new fields
 */
export const createFieldsToConfirmMap = (
  activeFieldMappings: FieldMapping[],
  responseFields: Array<{ id: string; name: string }>
): { [key: string]: string } => {
  return activeFieldMappings.reduce((acc: { [key: string]: string }, mapping) => {
    // For system fields, use the existing field ID
    if (mapping.systemFieldId) {
      acc[mapping.systemFieldId] = mapping.csvField;
    } else {
      // For new fields, find the corresponding field ID from the response
      const newField = responseFields.find(
        (field) => field.name === (mapping.systemFieldName || mapping.csvField)
      );
      if (newField?.id) {
        acc[newField.id] = mapping.csvField;
      }
    }
    return acc;
  }, {});
};

const getFieldOptions = (field: { name: string; type: string }) => {
  switch (field.type) {
    case FieldTypes.DATETIME:
      return {
        date: {
          format: 'YYYY/MM/DD',
        },
        time: {
          format: 'hh:mm A',
        },
        timezone: {
          format: 'Asia/Tokyo',
        },
      };

    case FieldTypes.SINGLE_SELECT:
    case FieldTypes.MULTI_SELECT:
      return {
        choices: [],
      };
    case FieldTypes.LONG_TEXT:
      return {
        format: 'YYYY/MM/DD',
      };
    case FieldTypes.NUMBER:
      return {
        numberFormat: 'decimal',
      };
    case FieldTypes.CURRENCY:
      return {
        currency: 'USD',
        decimalPlaces: 1,
        separator: {
          format: 'commaPeriod',
        },
      };

    case FieldTypes.PERCENT:
      return {
        separator: {
          format: 'commaPeriod',
        },
      };

    case FieldTypes.LINE:
      return {
        channelId: '',
      };

    case FieldTypes.PHONE_NUMBER:
      return {
        customFormat: {
          enabled: true,
          format: 'jp',
        },
      };

    default:
      return {};
  }
};

export const normalizeFieldMappings = (fields: { name: string; type: string }[]) => {
  return fields.map((field) => ({
    name: field.name.trim(),
    type: field.type.trim(),
    options: getFieldOptions(field),
  }));
};
