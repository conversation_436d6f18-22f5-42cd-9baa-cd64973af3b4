import { Currency, CurrencyFormat, CurrencySymbol, FormatDate } from '@/constants/workspace';
import type { WSObject } from '@/models';
import type { Active, Over } from '@dnd-kit/core';
import { arrayMove } from '@dnd-kit/sortable';
import { type MantineTheme, rem } from '@mantine/core';
import { FieldTypes, type View, type ViewGroup, ViewType } from '@resola-ai/ui/components';
import { TableCustomFieldsChangeTypes } from '@resola-ai/ui/components/DecaTable/components/Toolbar';
import { RowActions, UNORDERED_COLUMN_IDS } from '@resola-ai/ui/components/DecaTable/constants';
import * as dayjs from 'dayjs';
import advanced from 'dayjs/plugin/advancedFormat';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import { v4 as uuidV4 } from 'uuid';

// Sort options by label manually )
export const sortManual = (options) => {
  return options.sort((a, b) => a.time - b.time);
};

// Sort options by label in ascending order (A -> Z)
export const sortOptionsAsc = (options) => {
  return options.sort((a, b) => a.label.localeCompare(b.label));
};

// Sort options by label in descending order (Z -> A)
export const sortOptionsDesc = (options) => {
  return options.sort((a, b) => b.label.localeCompare(a.label));
};

export const handleDragOption = (active: Active, over: Over, data) => {
  const activeIndex = data.findIndex(({ id }) => id === active.id);
  const overIndex = data.findIndex(({ id }) => id === over.id);
  return arrayMove(data, activeIndex, overIndex);
};

export const getDateTime = (
  dateFormat,
  timeType,
  tz = 'Asia/Tokyo',
  displayTz = false,
  date?: string,
  lang?: 'ja' | 'en'
) => {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  dayjs.extend(advanced);

  const dateTimeInUtc = dayjs.utc(date ? new Date(date) : new Date());
  const dateTime = dateTimeInUtc.tz(tz);
  const tzAbbr = displayTz
    ? dateTime
        .format('zzz')
        .split(' ')
        .map((word) => word[0])
        .join('')
    : '';

  const format = `${
    lang === 'ja' && dateFormat === FormatDate.long ? FormatDate.longJa : dateFormat
  } ${timeType ?? ''}`;

  return `${dateTimeInUtc.tz(tz).format(format)} ${tzAbbr}`;
};

export const isValidEmail = (email: string): boolean => {
  return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(email);
};
export const isValidUrl = (url: string): boolean => {
  return /^(https?):\/\/[^\s/$.?#].[^\s]*$|^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(url);
};
export const isValidPhone = (phone: string): boolean => {
  return /^[0-9]{8,15}$/.test(phone);
};
export const isValidLine = (line: string): boolean => {
  return /^[a-zA-Z0-9._-]{4,50}$/.test(line);
};

const addThousandSeparators = (numStr: string, separator: string): string => {
  return numStr
    .split('')
    .reverse()
    .reduce<string[]>((acc, digit, i) => {
      if (i > 0 && i % 3 === 0) acc.push(separator);
      acc.push(digit);
      return acc;
    }, [])
    .reverse()
    .join('');
};

export const getCurrencyFormat = (
  value = 0,
  symbol = '$',
  separator = 'local',
  precision = 1,
  isReverse = false
) => {
  let formattedValue;
  if (separator === 'local') {
    const userLocale = navigator.language || 'en-US';
    formattedValue = new Intl.NumberFormat(userLocale, {
      style: 'decimal',
      minimumFractionDigits: precision,
      maximumFractionDigits: precision,
    }).format(value);
  } else {
    const format = CurrencyFormat[separator] || CurrencyFormat.periodOnly;
    const parts = (+value).toFixed(precision).split('.');
    parts[0] = addThousandSeparators(parts[0], format.thousand);
    formattedValue = format.decimal !== undefined ? parts.join(format.decimal) : parts[0];
  }

  return `${!isReverse ? symbol : ''}${formattedValue.trim()}${isReverse ? symbol : ''}`;
};

export const getFieldFormattedValue = (item: {
  type: any;
  value: string;
  options: any;
}): string => {
  let formattedValue = '';

  switch (item.type) {
    case FieldTypes.CURRENCY: {
      const symbol = CurrencySymbol[item.options?.currency ?? Currency.yen];
      const separatorFormat = item.options?.separator?.enabled
        ? item.options?.separator?.format
        : 'local';
      formattedValue = getCurrencyFormat(
        +item.value,
        symbol,
        separatorFormat,
        item.options?.decimalPlaces
      );
      break;
    }
    case FieldTypes.DATETIME:
    case FieldTypes.CREATED_TIME:
    case FieldTypes.MODIFIED_TIME: {
      try {
        formattedValue = getDateTime(
          item.options?.date?.format,
          item.options?.time?.enabled ? item.options?.time?.format : '',
          item.options?.timezone?.format,
          item.options?.displayTimezone,
          item.value,
          'ja'
        );
      } catch (e) {
        formattedValue = String(item.value);
      }
      break;
    }
    case FieldTypes.PERCENT: {
      const precision = item.options?.decimalPlaces ?? 1;
      const separatorFormat = item.options?.separator?.enabled
        ? item.options?.separator?.format
        : 'commaPeriod';
      formattedValue = getCurrencyFormat(Number(item.value), '%', separatorFormat, precision, true);

      break;
    }
    default:
      formattedValue = String(item.value);
  }

  return formattedValue;
};

export const downloadImage = (url: string) => {
  fetch(url)
    .then((response) => response.blob())
    .then((blob) => {
      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob);
      // Create a temporary anchor element
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = 'image.jpg'; // Set the file name
      // Append the anchor to the document body
      document.body.appendChild(a);
      a.click();
      // Clean up by revoking the blob URL and removing the anchor element
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    })
    .catch(() => alert('Failed to download image'));
};

const decimalRegex = /^\d+(\.\d*)?$/;
const integerRegex = /^\d*$/;

const commonValidations = {
  decimal: (value: string) => decimalRegex.test(value),
  integer: (value: string) => integerRegex.test(value),
  maxLength: (value: string, max: number) => value.length <= max,
};

const validationRules: Record<
  string,
  {
    validate: (value: string, format?: string) => boolean;
    message: string;
  }[]
> = {
  [FieldTypes.NUMBER]: [
    {
      validate: (value: string, format?: string) =>
        format === 'decimal' ? commonValidations.decimal(value) : commonValidations.integer(value),
      message: 'numberValidateErr',
    },
    {
      validate: (value: string) => commonValidations.maxLength(value, 50),
      message: 'maxLengthErr',
    },
  ],
  [FieldTypes.EMAIL]: [{ validate: isValidEmail, message: 'emailInValid' }],
  [FieldTypes.URL]: [{ validate: isValidUrl, message: 'urlInValid' }],
  [FieldTypes.PHONE_NUMBER]: [{ validate: isValidPhone, message: 'phoneInvalid' }],
  [FieldTypes.CURRENCY]: [
    { validate: commonValidations.decimal, message: 'numberValidateErr' },
    {
      validate: (value: string) => commonValidations.maxLength(value, 50),
      message: 'maxLengthErr',
    },
  ],
  [FieldTypes.PERCENT]: [
    { validate: commonValidations.decimal, message: 'numberValidateErr' },
    {
      validate: (value: string) => commonValidations.maxLength(value, 50),
      message: 'maxLengthErr',
    },
  ],
  [FieldTypes.SINGLE_LINE_TEXT]: [
    {
      validate: (value: string) => commonValidations.maxLength(value, 50),
      message: 'maxLengthErr',
    },
  ],
  [FieldTypes.LINE]: [{ validate: isValidLine, message: 'lineInvalid' }],
};

export const validateInput = (
  value: string,
  type,
  t: (key: string) => string,
  format?: string
): string => {
  const rules = validationRules[type];
  if (!rules) return '';

  for (const rule of rules) {
    if (!rule.validate(value, format)) {
      return t(rule.message);
    }
  }

  return '';
};

export const handleActionRow = async (
  action: string,
  rowIds: string[],
  deleteRow: (rowIds: string[]) => Promise<void>,
  handleInsertRow: (pos: 'above' | 'below', rowIds: string[]) => Promise<void>
) => {
  switch (action) {
    case RowActions.DELETE_ROW:
      await deleteRow(rowIds);
      break;
    case RowActions.INSERT_ABOVE:
      await handleInsertRow('above', rowIds);
      break;
    case RowActions.INSERT_BELOW:
    case RowActions.DUPLICATE_ROW:
      await handleInsertRow('below', rowIds);
      break;
    default:
      break;
  }
};

export const customNotificationStyles = (
  theme: MantineTheme,
  bgColor?: string,
  textColor?: string
) => ({
  root: {
    borderRadius: '16px',
    padding: '5px 18px',
    left: '50%',
    top: rem(75),
    width: 'fit-content',
    float: 'unset !important',
    backgroundColor: `${bgColor ?? theme.colors.decaGreen[0]} !important`,

    '.mantine-Notification-icon': {
      backgroundColor: 'unset',
    },
    '.mantine-Notification-description': {
      color: textColor ?? theme.colors.decaGreen[9],
      marginRight: rem(10),
    },
    svg: {
      stroke: textColor ?? theme.colors.decaGreen[9],
    },
  },
});

export const getViewFields = (object: WSObject | undefined) => {
  if (!object) return [];
  const activeView = object?.views?.[0];
  const fieldOrder = activeView?.fieldOrder ?? [];

  return (
    object?.fields
      ?.filter((f) => f.id)
      .sort((a, b) => fieldOrder.indexOf(a.id) - fieldOrder.indexOf(b.id)) || []
  );
};

export const getSelectedRowDetails = <T extends { id: string }>(
  rowSelection: Record<string, boolean>,
  data: T[]
): (T & { recordIndex: number })[] => {
  if (!rowSelection || !data?.length) return [];

  const dataMap = new Map(
    data.map((record, index) => [record.id, { ...record, recordIndex: index }])
  );

  return Object.entries(rowSelection).reduce<(T & { recordIndex: number })[]>(
    (result, [id, isSelected]) => {
      if (isSelected && dataMap.has(id)) {
        result.push(dataMap.get(id)!);
      }
      return result;
    },
    []
  );
};
export const createNewRow = (columns: any[]) => {
  const newRow = { id: uuidV4() };

  columns.forEach((col) => {
    if (col.type === FieldTypes.CHECKBOX) {
      newRow[col.id] = false;
    }
    if (col.type === FieldTypes.SINGLE_SELECT && col.options?.defaultValue) {
      newRow[col.id] =
        col.options?.defaultValue === 'notSelected' ? undefined : col.options?.defaultValue;
    }
    if (col.type === FieldTypes.MULTI_SELECT && col.options?.defaultValue?.length) {
      newRow[col.id] = col.options?.defaultValue;
    }
    if (col.type === FieldTypes.DATETIME && col.options?.useCurrentDate) {
      newRow[col.id] = new Date();
    }
  });

  return newRow;
};

export const handleColumnOrderChange = (
  newOrders: string[],
  activeView: View,
  selectedView: View,
  handleViewChange: (id: string, view: View, type?: string) => void
) => {
  if (!selectedView?.fieldOrder?.length) return;

  // Check if views match by ID
  if (activeView.id !== selectedView.id) return;

  const fieldOrder = newOrders?.filter((item) => !UNORDERED_COLUMN_IDS.includes(item));

  // Skip if we end up with invalid field orders
  if (fieldOrder.length === 0) return;

  const isSameOrder = fieldOrder.every(
    (value, index) => value === selectedView?.fieldOrder?.[index]
  );

  if (!isSameOrder) {
    handleViewChange(activeView.id, {
      ...activeView,
      fieldOrder,
    });
  }
};

export const handleColumnSizingChange = (
  size: Record<string, number>,
  activeView: any,
  setState: (state: any) => void,
  handleViewChange: (id: string, view: any, type: string) => void
) => {
  const currentCol = Object.keys(size)[0];
  setState(size);
  if (!activeView) return;

  // If activeView doesn't have fields property, create a new view with updated columnWidth
  if (!activeView.fields) {
    const updatedView = {
      ...activeView,
      columnWidth: { ...(activeView.columnWidth ?? {}), ...size },
    };
    handleViewChange(activeView.id, updatedView, TableCustomFieldsChangeTypes.ORDER_COLUMN);
    return;
  }

  const fields = [...activeView.fields].map((field) => {
    if (field.fieldMetaId === currentCol) {
      field.size = size[currentCol];
    }
    return field;
  });

  handleViewChange(
    activeView.id,
    { ...activeView, fields },
    TableCustomFieldsChangeTypes.ORDER_COLUMN
  );
};

export const mapColumnsToView = (
  tableColumns: any[],
  activeView: any,
  objectName: string | undefined,
  columnWidth: Record<string, number>
) => {
  const cols = tableColumns.map((col) => {
    const size = columnWidth[col.type as string];
    const field = activeView?.fields?.find((f) => col.id === f.fieldMetaId);
    return field ? { ...field, ...col, size: field.size ?? size } : { ...col, size };
  });

  return {
    ...activeView,
    fields: cols,
    objectName,
    length: cols.length,
    fieldOrder: activeView?.fieldOrder ?? [],
  };
};

export const normalizeViewGroups = (viewGroups: ViewGroup[], views?: View[]) => {
  const flatViews = viewGroups.flatMap((vg) => {
    if (vg.type === ViewType.GROUP) {
      return vg.views;
    }
    return [vg.view];
  });
  const updatedViews = views
    ?.filter((v) => {
      const flatView = flatViews.find((fv) => fv?.id === v.id);
      return flatView?.name !== v.name;
    })
    .map((v) => {
      return { ...v, name: flatViews?.find((fv) => fv?.id === v.id)?.name ?? v.name };
    });

  const updatedViewGroups = viewGroups.map((vg) => {
    if (vg.type === ViewType.GROUP) {
      return { id: vg.id, name: vg.name, viewIds: vg.viewIds, type: vg.type };
    }
    return { id: vg.id, type: vg.type };
  });
  return { views: updatedViews, viewGroups: updatedViewGroups };
};

// Helper to find a view by ID within a list of views
export const findViewInList = (views: View[], id: string | undefined): View | undefined => {
  if (!id) return undefined;
  return views.find((v) => v.id === id);
};

// Helper to sort pinned records to the top
export const sortPinnedRecords = (records: any[], pinnedRecords: any[]): any[] => {
  if (!pinnedRecords.length) return records;

  const pinnedMap = new Map(pinnedRecords.map((r) => [r.id, r]));
  const pinnedSet = new Set(pinnedMap.keys());

  const unpinned: any[] = [];

  for (const record of records) {
    if (!pinnedSet.has(record.id)) {
      unpinned.push(record);
    }
  }

  return [...pinnedRecords, ...unpinned];
};

// Helper to check if a field is involved in current view's filters or sorts
export const isFieldInFiltersOrSorts = (fieldId: string, activeView?: any): boolean => {
  if (!activeView) return false;

  // Check if field is in filters
  if (activeView.filters) {
    try {
      // Convert filters to string and search for fieldId
      const filtersString = JSON.stringify(activeView.filters);
      if (filtersString.includes(fieldId)) {
        return true;
      }
    } catch (error) {
      // Check if filters is an object with field keys
      if (typeof activeView.filters === 'object' && !Array.isArray(activeView.filters)) {
        // Check if the field exists in the filters object, regardless of its value
        return Object.prototype.hasOwnProperty.call(activeView.filters, fieldId);
      }
      // Check if filters is an array of filter objects
      if (Array.isArray(activeView.filters)) {
        return activeView.filters.some((filter: any) => filter.fieldId === fieldId);
      }
    }
  }

  // Check if field is in sorts
  if (activeView.sort && Array.isArray(activeView.sort)) {
    return activeView.sort.some((sort: any) => sort.fieldId === fieldId);
  }

  return false;
};
