import { AppContextProvider } from '@/contexts/AppContext';
import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import type React from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import NavigationControls from './index';

// Mock Tolgee
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) =>
      ({
        objects: 'Objects',
        createNew: 'Create New',
        objectSetting: 'Object Setting',
      })[key] || key,
  }),
}));
vi.mock('@/tolgee', () => ({
  tolgee: {
    changeLanguage: vi.fn(),
  },
}));

// Mock WorkspaceContext
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    mutateRecord: vi.fn(),
    mutateView: vi.fn(),
  }),
}));

// Mock AppContext
const mockMutateObjects = vi.fn();
vi.mock('@/contexts/AppContext', () => ({
  AppContextProvider: ({ children }: any) => <div data-testid='mock-app-context'>{children}</div>,
  useAppContext: () => ({
    showSidebar: true,
    sidebarWidth: 240,
    onToggleSidebar: vi.fn(),
    objects: [
      { id: '1', name: { singular: 'Object 1' }, icon: 'file' },
      { id: '2', name: { singular: 'Object 2' }, icon: 'file' },
      { id: '3', name: { singular: 'Object 3' }, icon: 'file' },
    ],
    mutateObjects: mockMutateObjects,
    setSidebarWidth: vi.fn(),
    setShowSidebar: vi.fn(),
    setFormOpened: vi.fn(),
    formOpened: false,
  }),
}));

// Mock DndKit
vi.mock('@dnd-kit/core', async () => {
  const actual = await vi.importActual('@dnd-kit/core');
  return {
    ...actual,
    DndContext: ({ children, onDragEnd }: any) => {
      return (
        <div
          data-testid='dnd-context-test'
          onClick={() =>
            onDragEnd({
              active: { id: '1' },
              over: { id: '3' },
            })
          }
        >
          {children}
        </div>
      );
    },
    useSensor: () => ({}),
    useSensors: () => [],
    PointerSensor: vi.fn(),
  };
});

vi.mock('@dnd-kit/sortable', () => ({
  SortableContext: ({ children }: any) => <div data-testid='sortable-context'>{children}</div>,
  useSortable: () => ({
    attributes: {},
    listeners: {},
    setNodeRef: () => {},
    transform: null,
    transition: null,
    isDragging: false,
  }),
}));

// Mock the router hooks
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: () => ({ id: 'test-id', wsId: 'test-workspace-id' }),
  };
});

// Mock ObjectAPI
vi.mock('@/services/api/object', () => ({
  ObjectAPI: {
    reorder: vi.fn().mockResolvedValue({}),
  },
}));

// Mock scroll into view
Element.prototype.scrollIntoView = vi.fn();

const renderWithProviders = (component: React.ReactNode) => {
  return renderWithMantine(
    <BrowserRouter>
      <AppContextProvider>{component}</AppContextProvider>
    </BrowserRouter>
  );
};

describe('NavigationControls', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders navigation container', () => {
    renderWithProviders(<NavigationControls />);
    expect(screen.getByTestId('navigation-container')).toBeInTheDocument();
  });

  it('displays objects section when sidebar is shown', () => {
    renderWithProviders(<NavigationControls />);
    expect(screen.getByText('Objects')).toBeInTheDocument();
  });

  it('toggles sidebar when chevron button is clicked', () => {
    renderWithProviders(<NavigationControls />);
    const toggleButton = screen.getByTestId('toggle-sidebar-button');
    fireEvent.click(toggleButton);
    expect(screen.getByTestId('navigation-container')).toBeInTheDocument();
  });

  it('opens create new form when plus button is clicked', async () => {
    renderWithProviders(<NavigationControls />);
    const createButton = screen.getByTestId('create-new-button');
    fireEvent.click(createButton);

    await waitFor(() => {
      expect(screen.getByTestId('navigation-container')).toBeInTheDocument();
    });
  });

  // test drag and drop
  it('handles drag and drop reordering of workspace items', async () => {
    renderWithProviders(<NavigationControls />);

    // Verify initial order
    expect(screen.getByTestId('workspace-item-1')).toBeInTheDocument();
    expect(screen.getByTestId('workspace-item-2')).toBeInTheDocument();
    expect(screen.getByTestId('workspace-item-3')).toBeInTheDocument();

    // Trigger drag end through mocked DndContext
    const dndContext = screen.getByTestId('dnd-context-test');
    fireEvent.click(dndContext);

    // Wait for async operations
    await waitFor(() => {
      // Verify mutateObjects was called (the actual implementation calls it without parameters)
      expect(mockMutateObjects).toHaveBeenCalled();
    });
  });
});
