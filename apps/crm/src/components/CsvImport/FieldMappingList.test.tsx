import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { IconBuilding, IconMail, IconPhone, IconUser } from '@tabler/icons-react';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import type { FieldMapping } from '../../utils';
import FieldMappingList from './FieldMappingList';

// Mock the useImportStyles hook
vi.mock('./useImportStyle', () => ({
  useImportStyles: () => ({
    classes: {
      mappingContainer: 'mapping-container',
      mappingHeader: 'mapping-header',
      headerColumn: 'header-column',
      mappingRow: 'mapping-row',
      csvFieldColumn: 'csv-field-column',
      csvFieldName: 'csv-field-name',
      arrow: 'arrow',
      crmFieldColumn: 'crm-field-column',
      selectRow: 'select-row',
      fieldTypeSelect: 'field-type-select',
      fieldTypeSelectSecondary: 'field-type-select-secondary',
      fieldTypeSelectFull: 'field-type-select-full',
    },
  }),
}));

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        csvField: 'CSV Field',
        crmFieldType: 'CRM Field Type',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock data
const mockFieldMappings: FieldMapping[] = [
  {
    csvField: 'Full Name',
    systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
    systemFieldId: 'name',
    systemFieldName: 'Name',
    skipped: false,
    isExistingField: true,
  },
  {
    csvField: 'Email Address',
    systemFieldType: FieldTypes.EMAIL,
    systemFieldId: 'email',
    systemFieldName: 'Email',
    skipped: false,
    isExistingField: true,
  },
  {
    csvField: 'Phone Number',
    systemFieldType: FieldTypes.PHONE_NUMBER,
    systemFieldId: null,
    systemFieldName: null,
    skipped: false,
    isExistingField: false,
  },
  {
    csvField: 'Company',
    systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
    systemFieldId: 'company',
    systemFieldName: 'Company',
    skipped: false,
    isExistingField: true,
  },
];

const mockMainFieldOptions = [
  { value: 'name', label: 'Name', icon: <IconUser size={16} /> },
  { value: 'email', label: 'Email', icon: <IconMail size={16} /> },
  { value: 'phone', label: 'Phone', icon: <IconPhone size={16} /> },
  { value: 'company', label: 'Company', icon: <IconBuilding size={16} /> },
  { value: 'new_field', label: 'Create New Field', icon: <IconUser size={16} /> },
];

const mockFieldTypeOptions = [
  { value: FieldTypes.SINGLE_LINE_TEXT, label: 'Single Line Text', icon: <IconUser size={16} /> },
  { value: FieldTypes.EMAIL, label: 'Email', icon: <IconMail size={16} /> },
  { value: FieldTypes.PHONE_NUMBER, label: 'Phone Number', icon: <IconPhone size={16} /> },
  { value: FieldTypes.LONG_TEXT, label: 'Long Text', icon: <IconUser size={16} /> },
];

const mockProps = {
  fieldMappings: mockFieldMappings,
  mainFieldOptions: mockMainFieldOptions,
  fieldTypeOptions: mockFieldTypeOptions,
  shouldShowNewFieldType: vi.fn((mapping: FieldMapping) => !mapping.isExistingField),
  getMainSelectedValue: vi.fn((mapping: FieldMapping) => {
    if (mapping.isExistingField && mapping.systemFieldId) {
      return mapping.systemFieldId;
    }
    return 'new_field';
  }),
  handleMainFieldSelectionChange: vi.fn(),
  handleNewFieldTypeChange: vi.fn(),
  renderOption: vi.fn(({ option }) => (
    <div data-testid={`option-${option.value}`}>
      {option.icon}
      {option.label}
    </div>
  )),
};

const renderComponent = (props = {}) => {
  return renderWithMantine(<FieldMappingList {...mockProps} {...props} />);
};

describe('FieldMappingList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the component with headers', () => {
      renderComponent();

      expect(screen.getByText('CSV Field')).toBeInTheDocument();
      expect(screen.getByText('CRM Field Type')).toBeInTheDocument();
    });

    it('renders all field mappings', () => {
      renderComponent();

      // Check by counting CSV field elements
      const csvFieldElements = document.querySelectorAll('.csv-field-name');
      expect(csvFieldElements).toHaveLength(mockFieldMappings.length);
    });

    it('renders mapping rows with CSV field names', () => {
      renderComponent();

      // Check that we have the expected number of mapping rows
      const mappingRows = document.querySelectorAll('.mapping-row');
      expect(mappingRows).toHaveLength(mockFieldMappings.length);
    });

    it('renders arrow indicators between CSV and CRM fields', () => {
      renderComponent();

      const arrows = screen.getAllByText('→');
      expect(arrows).toHaveLength(mockFieldMappings.length);
    });
  });

  describe('Field Selection', () => {
    it('renders select dropdowns for each field mapping', () => {
      renderComponent();

      const selects = document.querySelectorAll('[aria-haspopup="menu"]');
      expect(selects.length).toBeGreaterThan(0);
    });

    it('shows appropriate number of selects based on field type', () => {
      renderComponent();

      // Count total number of selects should equal:
      // - 1 for each existing field
      // - 2 for each new field
      const existingFieldCount = mockFieldMappings.filter((m) => m.isExistingField).length;
      const newFieldCount = mockFieldMappings.filter((m) => !m.isExistingField).length;
      const expectedSelectCount = existingFieldCount + newFieldCount * 2;

      const selects = document.querySelectorAll('[aria-haspopup="menu"]');
      expect(selects).toHaveLength(expectedSelectCount);
    });

    it('renders different layouts for existing vs new fields', () => {
      renderComponent();

      // Check that we have the expected number of full-width selects (existing fields)
      const fullWidthSelects = document.querySelectorAll('.field-type-select-full');
      const existingFieldCount = mockFieldMappings.filter((m) => m.isExistingField).length;
      expect(fullWidthSelects).toHaveLength(existingFieldCount);

      // Check that we have secondary selects for new fields
      const secondarySelects = document.querySelectorAll('.field-type-select-secondary');
      const newFieldCount = mockFieldMappings.filter((m) => !m.isExistingField).length;
      expect(secondarySelects).toHaveLength(newFieldCount);
    });

    it('calls the callback functions when provided', () => {
      const handleMainFieldSelectionChange = vi.fn();
      const handleNewFieldTypeChange = vi.fn();
      renderComponent({ handleMainFieldSelectionChange, handleNewFieldTypeChange });

      // Just verify that the callback functions are defined and could be called
      expect(handleMainFieldSelectionChange).toBeDefined();
      expect(handleNewFieldTypeChange).toBeDefined();
    });
  });

  describe('Conditional Rendering', () => {
    it('uses shouldShowNewFieldType to determine select layout', () => {
      const shouldShowNewFieldType = vi.fn(
        (mapping: FieldMapping) => mapping.csvField === 'Phone Number'
      );
      renderComponent({ shouldShowNewFieldType });

      expect(shouldShowNewFieldType).toHaveBeenCalledTimes(mockFieldMappings.length);

      // Check that it was called with each mapping
      mockFieldMappings.forEach((mapping) => {
        expect(shouldShowNewFieldType).toHaveBeenCalledWith(mapping);
      });
    });

    it('uses getMainSelectedValue to get selected values', () => {
      const getMainSelectedValue = vi.fn(
        (mapping: FieldMapping) => mapping.systemFieldId || 'new_field'
      );
      renderComponent({ getMainSelectedValue });

      expect(getMainSelectedValue).toHaveBeenCalledTimes(mockFieldMappings.length);

      // Check that it was called with each mapping
      mockFieldMappings.forEach((mapping) => {
        expect(getMainSelectedValue).toHaveBeenCalledWith(mapping);
      });
    });
  });

  describe('Custom Field Options', () => {
    it('uses mapping-specific options when getMainFieldOptionsForMapping is provided', () => {
      const customOptions = [
        { value: 'custom1', label: 'Custom Field 1', icon: <IconUser size={16} /> },
        { value: 'custom2', label: 'Custom Field 2', icon: <IconMail size={16} /> },
      ];

      const getMainFieldOptionsForMapping = vi.fn(() => customOptions);
      renderComponent({ getMainFieldOptionsForMapping });

      expect(getMainFieldOptionsForMapping).toHaveBeenCalledTimes(mockFieldMappings.length);

      // Check that it was called with each mapping
      mockFieldMappings.forEach((mapping) => {
        expect(getMainFieldOptionsForMapping).toHaveBeenCalledWith(mapping);
      });
    });

    it('falls back to general options when getMainFieldOptionsForMapping is not provided', () => {
      renderComponent();

      // Should render the component without errors when no custom options are provided
      expect(screen.getByText('CSV Field')).toBeInTheDocument();
      expect(screen.getByText('CRM Field Type')).toBeInTheDocument();

      // Should render the select components
      const selects = document.querySelectorAll('[aria-haspopup="menu"]');
      expect(selects.length).toBeGreaterThan(0);
    });
  });

  describe('Scroll Area', () => {
    it('renders scroll area with proper height calculation', () => {
      renderComponent();

      const scrollArea = document.querySelector('.mantine-ScrollArea-viewport');
      expect(scrollArea).toBeInTheDocument();
    });
  });

  describe('Select Props', () => {
    it('renders selects with correct props', () => {
      renderComponent();

      const selects = document.querySelectorAll('[aria-haspopup="menu"]');

      selects.forEach((select) => {
        // Check that selects have the correct attributes
        expect(select).toHaveAttribute('aria-haspopup', 'menu');
        expect(select).toHaveAttribute('aria-expanded', 'false');
      });
    });

    it('renders custom option components', () => {
      const { renderOption } = mockProps;
      renderComponent();

      // The render function should be called when options are rendered
      expect(renderOption).toBeDefined();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty field mappings', () => {
      renderComponent({ fieldMappings: [] });

      expect(screen.getByText('CSV Field')).toBeInTheDocument();
      expect(screen.getByText('CRM Field Type')).toBeInTheDocument();

      // Should not have any mapping rows
      const mappingRows = document.querySelectorAll('.mapping-row');
      expect(mappingRows).toHaveLength(0);
    });

    it('handles field mappings with null values', () => {
      const fieldMappingsWithNulls: FieldMapping[] = [
        {
          csvField: 'Test Field',
          systemFieldType: null,
          systemFieldId: null,
          systemFieldName: null,
          skipped: false,
          isExistingField: false,
        },
      ];

      renderComponent({ fieldMappings: fieldMappingsWithNulls });

      expect(screen.getByText('Test Field')).toBeInTheDocument();
    });

    it('handles unique keys for mapping rows', () => {
      const duplicateFieldMappings: FieldMapping[] = [
        {
          csvField: 'Duplicate Field',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: 'field1',
          systemFieldName: 'Field 1',
          skipped: false,
          isExistingField: true,
        },
        {
          csvField: 'Duplicate Field',
          systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
          systemFieldId: 'field2',
          systemFieldName: 'Field 2',
          skipped: false,
          isExistingField: true,
        },
      ];

      renderComponent({ fieldMappings: duplicateFieldMappings });

      // Check that duplicate CSV field names are rendered
      const csvFieldElements = document.querySelectorAll('.csv-field-name');
      expect(csvFieldElements).toHaveLength(2);

      // Check that both have the same text content
      expect(csvFieldElements[0]).toHaveTextContent('Duplicate Field');
      expect(csvFieldElements[1]).toHaveTextContent('Duplicate Field');
    });
  });
});
