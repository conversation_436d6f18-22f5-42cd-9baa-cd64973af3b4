import { renderWithMantine } from '@/tests/utils/testUtils';
import type { FileWithPath } from '@mantine/dropzone';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CsvImport from './index';

// Mock the required modules
vi.mock('@/services/api', () => ({
  TasksAPI: {
    importCSV: vi.fn(),
    confirmImport: vi.fn(),
  },
}));

vi.mock('react-router-dom', () => ({
  useParams: () => ({ wsId: 'test-workspace' }),
}));

vi.mock('@/contexts/AppContext', () => ({
  useAppContext: () => ({
    mutateObjects: vi.fn(),
  }),
}));

// Mock WorkspaceContext
vi.mock('@/contexts/WorkspaceContext', () => ({
  useWorkspaceContext: () => ({
    mutateRecord: vi.fn(),
    mutateView: vi.fn(),
  }),
}));

// Mock useNavigateObject
vi.mock('../NavBar/NavigationControls/useNavigateObject', () => ({
  useNavigateObject: () => ({
    handleNavigateObject: vi.fn(),
  }),
}));

// Mock Mantine components
vi.mock('@mantine/core', () => {
  const actual = vi.importActual('@mantine/core');
  return {
    ...actual,
    LoadingOverlay: ({ visible }: { visible: boolean }) => (visible ? <div>Loading...</div> : null),
    Modal: ({ children, opened }: { children: React.ReactNode; opened: boolean }) =>
      opened ? <div data-testid='modal'>{children}</div> : null,
    Radio: Object.assign(
      ({ value, label, checked }: any) => (
        <input type='radio' value={value} checked={checked} aria-label={label} />
      ),
      {
        Group: ({ children, value }: any) => (
          <div data-testid='radio-group' data-value={value}>
            {children}
          </div>
        ),
      }
    ),
    Select: ({ value, onChange, placeholder, data }: any) => (
      <select
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        aria-label={placeholder}
      >
        <option value=''>Select...</option>
        {data?.map((item: any) => (
          <option key={item.value} value={item.value}>
            {item.label}
          </option>
        ))}
      </select>
    ),
    TextInput: ({ value, onChange, placeholder }: any) => (
      <input
        type='text'
        value={value || ''}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
      />
    ),
  };
});

// Mock translations
const translations: Record<string, string> = {
  next: 'Next',
  back: 'Back',
  cancel: 'Cancel',
  import: 'Import',
  uploadFile: 'Upload file',
  selectObject: 'Select object',
  matchingFields: 'Matching fields',
  preview: 'Preview',
  dragAndDropText: 'Drag and drop your CSV file here',
  sampleFile: 'Sample file',
  uploadCsvFile: 'Upload CSV File',
  importToExistingObject: 'Import to existing object',
  createNewObject: 'Create new object',
  enterObjectName: 'Enter object name',
  useFirstRowAsHeaders: 'Use first row as headers',
  pleaseChooseObjectToImport: 'Please choose object to import',
  dropFileHere: 'Drop file here',
  maxFileSize: 'Max file size: 5MB',
  resetUpload: 'Reset Upload',
  isUploading: 'is uploading',
};

vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, defaultValue?: string) => translations[key] || defaultValue || key,
  }),
  Tolgee: () => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
  T: ({ keyName, defaultValue }: { keyName: string; defaultValue?: string }) =>
    defaultValue || keyName,
  useTolgee: () => ({}),
  BackendProvider: ({ children }: { children: React.ReactNode }) => children,
  FormatSimple: () => ({}),
}));

// Mock hooks
vi.mock('../../hooks', () => ({
  useCsvParser: () => ({
    csvData: [
      ['Name', 'Email'],
      ['John', '<EMAIL>'],
    ],
    isLoadingCsv: false,
    isUploading: false,
    uploadProgress: 0,
  }),
  useObject: () => ({
    object: {
      id: 'test-object',
      name: { singular: 'Contact', plural: 'Contacts' },
      fields: [
        { id: 'name', type: 'TEXT', name: 'Name' },
        { id: 'email', type: 'EMAIL', name: 'Email' },
      ],
    },
  }),
  useObjects: () => ({
    objects: [
      {
        id: 'contact',
        name: { singular: 'Contact', plural: 'Contacts' },
        fields: [
          { id: 'name', type: 'TEXT', name: 'Name' },
          { id: 'email', type: 'EMAIL', name: 'Email' },
        ],
      },
    ],
  }),
}));

describe('CsvImport', () => {
  const mockOnClose = vi.fn();
  const mockFile: FileWithPath = new File(['test,data\n1,2'], 'test.csv', {
    type: 'text/csv',
  }) as FileWithPath;

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console.error for error test
    console.error = vi.fn();
  });

  it('renders initial state correctly', () => {
    renderWithMantine(<CsvImport opened={true} onClose={mockOnClose} />);

    expect(screen.getByRole('button', { name: 'Upload file' })).toBeInTheDocument();
    expect(screen.getByText('Select object')).toBeInTheDocument();
    expect(screen.getByText('Matching fields')).toBeInTheDocument();
    expect(screen.getByText('Preview')).toBeInTheDocument();
  });

  it('handles file upload', async () => {
    renderWithMantine(<CsvImport opened={true} onClose={mockOnClose} />);

    // Simulate file drop
    const dropzone = screen.getByTestId('csv-dropzone');
    const dropEvent = {
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
      type: 'drop',
      dataTransfer: {
        files: [mockFile],
        items: [
          {
            kind: 'file',
            type: 'text/csv',
            getAsFile: () => mockFile,
          },
        ],
        types: ['Files'],
      },
    };

    fireEvent.drop(dropzone, dropEvent);

    await waitFor(() => {
      expect(screen.getByText(mockFile.name)).toBeInTheDocument();
    });
  });

  it('handles cancel action', () => {
    renderWithMantine(<CsvImport opened={true} onClose={mockOnClose} />);

    const cancelButton = screen.getByRole('button', { name: 'Cancel' });
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalled();
  });
});
