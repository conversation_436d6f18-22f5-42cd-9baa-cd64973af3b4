import { renderWithMantine } from '@/tests/utils/testUtils';
import { cleanup, fireEvent, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CsvFileInfo from './CsvFileInfo';

// Mock the @resola-ai/ui components
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, 'data-testid': testId, ...props }: any) => (
    <button type='button' onClick={onClick} data-testid={testId} {...props}>
      {children}
    </button>
  ),
  CustomImage: ({ url, 'data-testid': testId, ...props }: any) => {
    return <img {...props} src={url} data-testid={testId} alt='CSV import illustration' />;
  },
}));

// Mock the @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, defaultValue?: string) => {
      const translations: Record<string, string> = {
        resetUpload: 'Reset Upload',
      };
      return translations[key] || defaultValue || key;
    },
  }),
}));

// Mock the child components
vi.mock('./CsvPreview', () => ({
  default: ({ csvData, isLoading, 'data-testid': testId }: any) => (
    <div data-testid={testId}>
      <div data-testid='csv-preview-mock'>
        {isLoading ? 'Loading...' : `CSV Preview with ${csvData.length} rows`}
      </div>
    </div>
  ),
}));

vi.mock('./CsvUploadProgress', () => ({
  default: ({ fileName, progress }: any) => (
    <div data-testid='csv-upload-progress-mock'>
      <div data-testid='progress-filename'>{fileName}</div>
      <div data-testid='progress-value'>{progress}%</div>
    </div>
  ),
}));

describe('CsvFileInfo', () => {
  const mockOnReset = vi.fn();
  const mockFile = new File(['test content'], 'test.csv', { type: 'text/csv' });
  const mockCsvData = [
    ['Name', 'Email', 'Phone'],
    ['John Doe', '<EMAIL>', '************'],
    ['Jane Smith', '<EMAIL>', '************'],
  ];

  const defaultProps = {
    uploadedFile: mockFile,
    onReset: mockOnReset,
    csvData: mockCsvData,
    isLoading: false,
    isUploading: false,
    uploadProgress: 0,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('when not uploading', () => {
    it('renders the component correctly', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      expect(screen.getByTestId('csv-file-info-container')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-content')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-header')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-image')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-filename')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-preview')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-actions')).toBeInTheDocument();
      expect(screen.getByTestId('csv-file-info-reset-button')).toBeInTheDocument();
    });

    it('displays the file name correctly', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      expect(screen.getByTestId('csv-file-info-filename')).toHaveTextContent('test.csv');
    });

    it('displays the CSV import image with correct source', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      const image = screen.getByTestId('csv-file-info-image');
      expect(image).toHaveAttribute('src', 'images/csv_import.png');
    });

    it('renders the CSV preview component with correct props', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      expect(screen.getByTestId('csv-preview-mock')).toBeInTheDocument();
      expect(screen.getByTestId('csv-preview-mock')).toHaveTextContent('CSV Preview with 3 rows');
    });

    it('shows loading state in CSV preview when isLoading is true', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} isLoading={true} />);

      expect(screen.getByTestId('csv-preview-mock')).toHaveTextContent('Loading...');
    });

    it('displays the reset button with correct text', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');
      expect(resetButton).toHaveTextContent('Reset Upload');
    });

    it('calls onReset when reset button is clicked', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');
      fireEvent.click(resetButton);

      expect(mockOnReset).toHaveBeenCalledTimes(1);
    });

    it('does not render upload progress when not uploading', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      expect(screen.queryByTestId('csv-upload-progress-mock')).not.toBeInTheDocument();
    });
  });

  describe('when uploading', () => {
    const uploadingProps = {
      ...defaultProps,
      isUploading: true,
      uploadProgress: 45,
    };

    it('renders the uploading state correctly', () => {
      renderWithMantine(<CsvFileInfo {...uploadingProps} />);

      expect(screen.getByTestId('csv-file-info-uploading')).toBeInTheDocument();
      expect(screen.getByTestId('csv-upload-progress-mock')).toBeInTheDocument();
    });

    it('displays the upload progress component with correct props', () => {
      renderWithMantine(<CsvFileInfo {...uploadingProps} />);

      expect(screen.getByTestId('progress-filename')).toHaveTextContent('test.csv');
      expect(screen.getByTestId('progress-value')).toHaveTextContent('45%');
    });

    it('does not render the main content when uploading', () => {
      renderWithMantine(<CsvFileInfo {...uploadingProps} />);

      expect(screen.queryByTestId('csv-file-info-container')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-content')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-header')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-image')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-filename')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-preview')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-actions')).not.toBeInTheDocument();
      expect(screen.queryByTestId('csv-file-info-reset-button')).not.toBeInTheDocument();
    });

    it('updates progress correctly when progress changes', () => {
      // Test with initial progress
      renderWithMantine(<CsvFileInfo {...uploadingProps} />);
      expect(screen.getByTestId('progress-value')).toHaveTextContent('45%');

      // Clear previous render and test with different progress value
      cleanup();

      const higherProgressProps = {
        ...uploadingProps,
        uploadProgress: 75,
      };

      renderWithMantine(<CsvFileInfo {...higherProgressProps} />);
      expect(screen.getByTestId('progress-value')).toHaveTextContent('75%');
    });
  });

  describe('with different file types', () => {
    it('handles different file names correctly', () => {
      const xlsxFile = new File(['content'], 'data.xlsx', {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const propsWithXlsxFile = {
        ...defaultProps,
        uploadedFile: xlsxFile,
      };

      renderWithMantine(<CsvFileInfo {...propsWithXlsxFile} />);

      expect(screen.getByTestId('csv-file-info-filename')).toHaveTextContent('data.xlsx');
    });

    it('handles files with long names correctly', () => {
      const longNameFile = new File(
        ['content'],
        'very_long_filename_that_might_cause_display_issues.csv',
        { type: 'text/csv' }
      );
      const propsWithLongNameFile = {
        ...defaultProps,
        uploadedFile: longNameFile,
      };

      renderWithMantine(<CsvFileInfo {...propsWithLongNameFile} />);

      expect(screen.getByTestId('csv-file-info-filename')).toHaveTextContent(
        'very_long_filename_that_might_cause_display_issues.csv'
      );
    });
  });

  describe('with different CSV data', () => {
    it('handles empty CSV data correctly', () => {
      const propsWithEmptyData = {
        ...defaultProps,
        csvData: [],
      };

      renderWithMantine(<CsvFileInfo {...propsWithEmptyData} />);

      expect(screen.getByTestId('csv-preview-mock')).toHaveTextContent('CSV Preview with 0 rows');
    });

    it('handles large CSV data correctly', () => {
      const largeCsvData = Array(1000).fill(['col1', 'col2', 'col3']);
      const propsWithLargeData = {
        ...defaultProps,
        csvData: largeCsvData,
      };

      renderWithMantine(<CsvFileInfo {...propsWithLargeData} />);

      expect(screen.getByTestId('csv-preview-mock')).toHaveTextContent(
        'CSV Preview with 1000 rows'
      );
    });
  });

  describe('accessibility', () => {
    it('has proper button accessibility', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      const resetButton = screen.getByTestId('csv-file-info-reset-button');
      expect(resetButton).toHaveAttribute('type', 'button');
    });

    it('has proper image accessibility', () => {
      renderWithMantine(<CsvFileInfo {...defaultProps} />);

      const image = screen.getByTestId('csv-file-info-image');
      expect(image).toHaveAttribute('alt', 'CSV import illustration');
    });
  });
});
