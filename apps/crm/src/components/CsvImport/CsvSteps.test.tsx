import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import CsvSteps from './CsvSteps';

const mockSteps = [
  { number: 1, label: 'Upload File' },
  { number: 2, label: 'Select Object' },
  { number: 3, label: 'Map Fields' },
  { number: 4, label: 'Preview' },
  { number: 5, label: 'Import' },
];

describe('CsvSteps', () => {
  it('renders all steps correctly', () => {
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={1} />);

    // Check that all step labels are rendered
    mockSteps.forEach((step) => {
      expect(screen.getByText(step.label)).toBeInTheDocument();
    });
  });

  it('renders with correct number of steps', () => {
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={1} />);

    // Check that the stepper has the correct number of steps
    const stepElements = screen.getAllByRole('button');
    expect(stepElements).toHaveLength(mockSteps.length);
  });

  it('handles currentStep prop correctly', () => {
    const { unmount } = renderWithMantine(<CsvSteps steps={mockSteps} currentStep={1} />);

    // Check that the first step is active
    expect(screen.getByText('Upload File')).toBeInTheDocument();

    // Clean up first render
    unmount();

    // Test with different current step value
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={3} />);

    // All step labels should still be present
    mockSteps.forEach((step) => {
      expect(screen.getByText(step.label)).toBeInTheDocument();
    });
  });

  it('renders with empty steps array', () => {
    renderWithMantine(<CsvSteps steps={[]} currentStep={0} />);

    // Should not have any step buttons
    const stepElements = screen.queryAllByRole('button');
    expect(stepElements).toHaveLength(0);
  });

  it('renders with single step', () => {
    const singleStep = [{ number: 1, label: 'Single Step' }];

    renderWithMantine(<CsvSteps steps={singleStep} currentStep={1} />);

    expect(screen.getByText('Single Step')).toBeInTheDocument();
    const stepElements = screen.getAllByRole('button');
    expect(stepElements).toHaveLength(1);
  });

  it('handles edge case with currentStep 0', () => {
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={0} />);

    mockSteps.forEach((step) => {
      expect(screen.getByText(step.label)).toBeInTheDocument();
    });
  });

  it('handles edge case with currentStep greater than steps length', () => {
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={10} />);

    mockSteps.forEach((step) => {
      expect(screen.getByText(step.label)).toBeInTheDocument();
    });
  });

  it('renders stepper with correct size prop', () => {
    const { container } = renderWithMantine(<CsvSteps steps={mockSteps} currentStep={1} />);

    // Check that the stepper container exists with correct size
    const stepperContainer = container.querySelector('.mantine-Stepper-root');
    expect(stepperContainer).toBeInTheDocument();
    expect(stepperContainer).toHaveAttribute('data-size', 'sm');
  });

  it('applies custom styling classes', () => {
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={1} />);

    // Check that steps have the custom class applied
    const stepElements = screen.getAllByRole('button');
    expect(stepElements).toHaveLength(mockSteps.length);

    // Check that the custom styles are applied (the class name will be hashed)
    stepElements.forEach((element) => {
      expect(element).toHaveClass('mantine-Stepper-step');
    });
  });

  it('renders check icon for completed steps', () => {
    renderWithMantine(<CsvSteps steps={mockSteps} currentStep={3} />);

    // The component should render without errors
    // Check icon rendering is handled by Mantine internally
    expect(screen.getByText('Upload File')).toBeInTheDocument();
    expect(screen.getByText('Select Object')).toBeInTheDocument();
  });
});
