import { Box, Flex, Group, rem, Text } from '@mantine/core';
import type { FileWithPath } from '@mantine/dropzone';
import { CustomImage, DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import CsvPreview from './CsvPreview';
import CsvUploadProgress from './CsvUploadProgress';

interface CsvFileInfoProps {
  uploadedFile: FileWithPath;
  onReset: () => void;
  csvData: string[][];
  isLoading: boolean;
  isUploading: boolean;
  uploadProgress: number;
}

const CsvFileInfo: React.FC<CsvFileInfoProps> = ({
  uploadedFile,
  onReset,
  csvData,
  isLoading,
  isUploading,
  uploadProgress,
}) => {
  const { t } = useTranslate();

  if (isUploading) {
    return (
      <Box data-testid='csv-file-info-uploading'>
        <CsvUploadProgress fileName={uploadedFile.name} progress={uploadProgress} />
      </Box>
    );
  }

  return (
    <Box w={'100%'} data-testid='csv-file-info-container'>
      <Flex data-testid='csv-file-info-content'>
        <Group w={rem(500)} justify='center' align='center' data-testid='csv-file-info-header'>
          <CustomImage url={'images/csv_import.png'} data-testid='csv-file-info-image' />
          <Text data-testid='csv-file-info-filename'>{uploadedFile.name}</Text>
        </Group>
        <CsvPreview csvData={csvData} isLoading={isLoading} data-testid='csv-file-info-preview' />
      </Flex>

      <Box
        w={'100%'}
        ta='center'
        mt={rem(50)}
        pos='absolute'
        bottom={rem(90)}
        data-testid='csv-file-info-actions'
      >
        <DecaButton
          variant='negative_text'
          size='sm'
          onClick={onReset}
          data-testid='csv-file-info-reset-button'
        >
          {t('resetUpload')}
        </DecaButton>
      </Box>
    </Box>
  );
};

export default CsvFileInfo;
