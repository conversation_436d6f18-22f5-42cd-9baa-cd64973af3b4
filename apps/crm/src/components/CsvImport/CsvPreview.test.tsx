import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import CsvPreview from './CsvPreview';

describe('CsvPreview', () => {
  const mockCsvData = [
    ['Name', 'Email', 'Age', 'City', 'Country'],
    ['<PERSON>', '<EMAIL>', '25', 'New York', 'USA'],
    ['<PERSON> Smith', '<EMAIL>', '30', 'London', 'UK'],
    ['<PERSON>', '<EMAIL>', '35', 'Toronto', 'Canada'],
  ];

  const defaultProps = {
    csvData: mockCsvData,
    isLoading: false,
  };

  it('renders loading state when isLoading is true', () => {
    renderWithMantine(<CsvPreview {...defaultProps} isLoading={true} />);

    expect(screen.getByText('Loading preview...')).toBeInTheDocument();
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
  });

  it('renders empty state when csvData is empty', () => {
    renderWithMantine(<CsvPreview {...defaultProps} csvData={[]} />);

    expect(screen.getByText('No preview available')).toBeInTheDocument();
    expect(screen.queryByRole('table')).not.toBeInTheDocument();
  });

  it('renders table with correct data structure', () => {
    renderWithMantine(<CsvPreview {...defaultProps} />);

    const table = screen.getByRole('table');
    expect(table).toBeInTheDocument();

    // Check Excel-like column headers (A, B, C, D, E)
    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.getByText('B')).toBeInTheDocument();
    expect(screen.getByText('C')).toBeInTheDocument();
    expect(screen.getByText('D')).toBeInTheDocument();
    expect(screen.getByText('E')).toBeInTheDocument();

    // Check row numbers
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();

    // Check actual data content
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('displays correct row count in footer', () => {
    renderWithMantine(<CsvPreview {...defaultProps} />);

    expect(screen.getByText('Showing first 3 rows')).toBeInTheDocument();
  });

  it('handles data with more than 6 columns', () => {
    const wideData = [
      ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9'],
      ['Data1', 'Data2', 'Data3', 'Data4', 'Data5', 'Data6', 'Data7', 'Data8', 'Data9'],
    ];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={wideData} />);

    // Should show first 6 columns (A-F)
    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.getByText('B')).toBeInTheDocument();
    expect(screen.getByText('C')).toBeInTheDocument();
    expect(screen.getByText('D')).toBeInTheDocument();
    expect(screen.getByText('E')).toBeInTheDocument();
    expect(screen.getByText('F')).toBeInTheDocument();

    // Should show additional columns indicator
    expect(screen.getByText('+3')).toBeInTheDocument();
    expect(screen.getByText('...')).toBeInTheDocument();

    // Should show total column count in footer
    expect(screen.getByText('Showing first 1 rows • 9 columns total')).toBeInTheDocument();
  });

  it('generates correct Excel column names', () => {
    const manyColumnsData = [
      Array.from({ length: 30 }, (_, i) => `Col${i + 1}`),
      Array.from({ length: 30 }, (_, i) => `Data${i + 1}`),
    ];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={manyColumnsData} />);

    // Should show first 6 columns with correct Excel naming
    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.getByText('B')).toBeInTheDocument();
    expect(screen.getByText('C')).toBeInTheDocument();
    expect(screen.getByText('D')).toBeInTheDocument();
    expect(screen.getByText('E')).toBeInTheDocument();
    expect(screen.getByText('F')).toBeInTheDocument();

    // Should show additional columns indicator
    expect(screen.getByText('+24')).toBeInTheDocument();
  });

  it('handles empty cells correctly', () => {
    const dataWithEmptyCells = [
      ['Name', 'Email', 'Age'],
      ['John Doe', '', '25'],
      ['', '<EMAIL>', ''],
    ];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={dataWithEmptyCells} />);

    // Should still render the table structure
    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('25')).toBeInTheDocument();
  });

  it('handles single column data', () => {
    const singleColumnData = [['Name'], ['John Doe'], ['Jane Smith']];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={singleColumnData} />);

    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.queryByText('B')).not.toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('handles data with only headers', () => {
    const headersOnlyData = [['Name', 'Email', 'Age']];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={headersOnlyData} />);

    expect(screen.getByRole('table')).toBeInTheDocument();
    expect(screen.getByText('A')).toBeInTheDocument();
    expect(screen.getByText('B')).toBeInTheDocument();
    expect(screen.getByText('C')).toBeInTheDocument();
    expect(screen.getByText('Showing first 0 rows')).toBeInTheDocument();
  });

  it('truncates long text content with ellipsis', () => {
    const longTextData = [
      ['Name', 'Description'],
      ['John', 'This is a very long description that should be truncated with ellipsis'],
    ];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={longTextData} />);

    const cells = screen.getAllByRole('cell');
    // Find the cell with long text (should have title attribute for tooltip)
    const longTextCell = cells.find(
      (cell) =>
        cell.getAttribute('title') ===
        'This is a very long description that should be truncated with ellipsis'
    );
    expect(longTextCell).toBeInTheDocument();
  });

  it('displays correct footer text for large datasets', () => {
    const largeData = [
      ['Name', 'Email'],
      ...Array.from({ length: 15 }, (_, i) => [`Name${i + 1}`, `email${i + 1}@example.com`]),
    ];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={largeData} />);

    // Should show "first 10 rows" even though there are more rows
    expect(screen.getByText('Showing first 10 rows')).toBeInTheDocument();
  });

  it('handles special characters in data', () => {
    const specialCharData = [
      ['Name', 'Symbols'],
      ['John & Jane', '<script>alert("xss")</script>'],
      ['$100', '50% off'],
    ];

    renderWithMantine(<CsvPreview {...defaultProps} csvData={specialCharData} />);

    expect(screen.getByText('John & Jane')).toBeInTheDocument();
    expect(screen.getByText('<script>alert("xss")</script>')).toBeInTheDocument();
    expect(screen.getByText('$100')).toBeInTheDocument();
    expect(screen.getByText('50% off')).toBeInTheDocument();
  });
});
