import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CsvObjectSelection from './CsvObjectSelection';

// Mock the hooks and dependencies
vi.mock('@resola-ai/ui', () => ({
  DecaSwitch: ({ checked, onChange, label }: any) => (
    <div data-testid='deca-switch'>
      <input type='checkbox' checked={checked} onChange={onChange} data-testid='switch-input' />
      <label>{label}</label>
    </div>
  ),
}));

vi.mock('./useImportStyle', () => ({
  useImportStyles: () => ({
    classes: {
      borderedContainer: 'bordered-container',
      title: 'title',
      radioGroup: 'radio-group',
      radioStack: 'radio-stack',
      radioItem: 'radio-item',
      radioContent: 'radio-content',
      radioControl: 'radio-control',
      columnsPreview: 'columns-preview',
      columnTag: 'column-tag',
    },
  }),
}));

vi.mock('../../hooks/useObjects', () => ({
  useObjects: vi.fn(() => ({
    objects: [
      { id: 'obj1', name: { singular: 'Contact' } },
      { id: 'obj2', name: { singular: 'Organization' } },
      { id: 'obj3', name: { singular: 'Deal' } },
    ],
  })),
}));

vi.mock('react-router-dom', async (importOriginal) => {
  const actual = await importOriginal<typeof import('react-router-dom')>();
  return {
    ...actual,
    useParams: vi.fn(() => ({ wsId: 'test-workspace' })),
  };
});

const mockProps = {
  csvData: [
    ['Name', 'Email', 'Phone', 'Company'],
    ['John Doe', '<EMAIL>', '************', 'Acme Corp'],
    ['Jane Smith', '<EMAIL>', '************', 'Tech Inc'],
  ],
  selectedObjectType: 'obj1',
  importMode: 'existing' as const,
  newObjectName: '',
  useHeaders: true,
  onObjectTypeChange: vi.fn(),
  onImportModeChange: vi.fn(),
  onUseHeadersChange: vi.fn(),
  onNewObjectNameChange: vi.fn(),
};

const renderComponent = (props = {}) => {
  return renderWithMantine(
    <MemoryRouter>
      <CsvObjectSelection {...mockProps} {...props} />
    </MemoryRouter>
  );
};

describe('CsvObjectSelection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders component with default props', () => {
    renderComponent();

    expect(screen.getByText('pleaseChooseObjectToImport')).toBeInTheDocument();
    expect(screen.getByText('importToExistingObject')).toBeInTheDocument();
    expect(screen.getByText('createNewObject')).toBeInTheDocument();
    expect(screen.getByText('useFirstRowAsHeaders')).toBeInTheDocument();
  });

  it('displays existing object mode by default', () => {
    renderComponent();

    const existingRadio = screen.getByDisplayValue('existing');
    expect(existingRadio).toBeChecked();

    const selectObject = screen.getByPlaceholderText('selectObject');
    expect(selectObject).toBeEnabled();
  });

  it('displays object options from useObjects hook', () => {
    renderComponent();

    const select = screen.getByPlaceholderText('selectObject');
    fireEvent.click(select);

    waitFor(() => {
      expect(screen.getByText('Contact')).toBeInTheDocument();
      expect(screen.getByText('Organization')).toBeInTheDocument();
      expect(screen.getByText('Deal')).toBeInTheDocument();
    });
  });

  it('displays selected object type in select input', () => {
    const onObjectTypeChange = vi.fn();
    renderComponent({ onObjectTypeChange, selectedObjectType: 'obj1' });

    const select = screen.getByPlaceholderText('selectObject');
    expect(select).toHaveValue('Contact');
  });

  it('switches to new object mode when radio is selected', () => {
    const onImportModeChange = vi.fn();
    renderComponent({ onImportModeChange });

    const newRadio = screen.getByDisplayValue('new');
    fireEvent.click(newRadio);

    expect(onImportModeChange).toHaveBeenCalledWith('new');
  });

  it('enables text input when in new object mode', () => {
    renderComponent({ importMode: 'new' });

    const textInput = screen.getByPlaceholderText('enterObjectName');
    expect(textInput).toBeEnabled();

    // Select element should not exist in new mode
    const select = screen.queryByPlaceholderText('selectObject');
    expect(select).not.toBeInTheDocument();
  });

  it('calls onNewObjectNameChange when typing in text input', () => {
    const onNewObjectNameChange = vi.fn();
    renderComponent({
      importMode: 'new',
      onNewObjectNameChange,
    });

    const textInput = screen.getByPlaceholderText('enterObjectName');
    fireEvent.change(textInput, { target: { value: 'New Object' } });

    expect(onNewObjectNameChange).toHaveBeenCalledWith('New Object');
  });

  it('toggles headers switch and calls callback', () => {
    const onUseHeadersChange = vi.fn();
    renderComponent({ onUseHeadersChange });

    const switchInput = screen.getByTestId('switch-input');
    fireEvent.click(switchInput);

    expect(onUseHeadersChange).toHaveBeenCalledWith(false);
  });

  it('displays column headers when useHeaders is true', () => {
    renderComponent({ useHeaders: true });

    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByText('Phone')).toBeInTheDocument();
    expect(screen.getByText('Company')).toBeInTheDocument();
  });

  it('does not display column headers when useHeaders is false', () => {
    renderComponent({ useHeaders: false });

    expect(screen.queryByText('Name')).not.toBeInTheDocument();
    expect(screen.queryByText('Email')).not.toBeInTheDocument();
  });

  it('shows ellipsis when there are more than 8 columns', () => {
    const csvDataWithManyColumns = [
      ['Col1', 'Col2', 'Col3', 'Col4', 'Col5', 'Col6', 'Col7', 'Col8', 'Col9', 'Col10'],
      ['Data1', 'Data2', 'Data3', 'Data4', 'Data5', 'Data6', 'Data7', 'Data8', 'Data9', 'Data10'],
    ];

    renderComponent({
      csvData: csvDataWithManyColumns,
      useHeaders: true,
    });

    expect(screen.getByText('Col1')).toBeInTheDocument();
    expect(screen.getByText('Col8')).toBeInTheDocument();
    expect(screen.queryByText('Col9')).not.toBeInTheDocument();
    expect(screen.getByText('...')).toBeInTheDocument();
  });

  it('handles empty CSV data gracefully', () => {
    renderComponent({ csvData: [] });

    expect(screen.getByText('pleaseChooseObjectToImport')).toBeInTheDocument();
    expect(screen.queryByText('Name')).not.toBeInTheDocument();
  });

  it('handles CSV data with empty headers', () => {
    renderComponent({
      csvData: [[]],
      useHeaders: true,
    });

    expect(screen.getByText('pleaseChooseObjectToImport')).toBeInTheDocument();
    expect(screen.queryByText('Name')).not.toBeInTheDocument();
  });

  it('renders select input correctly', () => {
    renderComponent();

    const select = screen.getByPlaceholderText('selectObject');
    expect(select).toBeInTheDocument();
    expect(select).toHaveAttribute('readonly');
  });

  it('renders text input correctly in new mode', () => {
    renderComponent({ importMode: 'new' });

    const textInput = screen.getByPlaceholderText('enterObjectName');
    expect(textInput).toBeInTheDocument();
    expect(textInput).toBeEnabled();
  });

  it('applies correct CSS classes', () => {
    const { container } = renderComponent();

    expect(container.querySelector('.bordered-container')).toBeInTheDocument();
    expect(container.querySelector('.title')).toBeInTheDocument();
    expect(container.querySelector('.radio-group')).toBeInTheDocument();
    expect(container.querySelector('.radio-stack')).toBeInTheDocument();
  });

  it('handles radio item clicks correctly', () => {
    const onImportModeChange = vi.fn();
    renderComponent({ onImportModeChange });

    const radioItems = screen.getAllByRole('radio');
    if (radioItems.length > 0) {
      fireEvent.click(radioItems[0]);
      expect(onImportModeChange).toHaveBeenCalled();
    }
  });

  it('sets data-selected attribute correctly', () => {
    renderComponent({ importMode: 'existing' });

    const radioItems = document.querySelectorAll('[data-selected]');
    expect(radioItems.length).toBeGreaterThan(0);

    const existingItem = document.querySelector('[data-selected="true"]');
    expect(existingItem).toBeInTheDocument();
  });

  it('initializes state with provided props', () => {
    renderComponent({
      selectedObjectType: 'obj2',
      importMode: 'new',
      newObjectName: 'Test Object',
      useHeaders: false,
    });

    const newRadio = screen.getByDisplayValue('new');
    expect(newRadio).toBeChecked();

    const textInput = screen.getByDisplayValue('Test Object');
    expect(textInput).toBeInTheDocument();

    const switchInput = screen.getByTestId('switch-input');
    expect(switchInput).not.toBeChecked();
  });
});
