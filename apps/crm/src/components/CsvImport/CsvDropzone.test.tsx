import { renderWithMantine } from '@/tests/utils/testUtils';
import { fireEvent, screen, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CsvDropzone from './CsvDropzone';

// Mock the @resola-ai/ui components
vi.mock('@resola-ai/ui', () => ({
  DecaButton: ({ children, onClick, 'data-testid': testId, ...props }: any) => (
    <button onClick={onClick} data-testid={testId} {...props}>
      {children}
    </button>
  ),
  CustomImage: ({ url, 'data-testid': testId, ...props }: any) => {
    return <img {...props} src={url} data-testid={testId} alt='CSV import illustration' />;
  },
}));

// Mock the @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, defaultValue?: string) => {
      const translations: Record<string, string> = {
        dropFileHere: 'Drop file here',
        uploadFile: 'Upload file',
        maxFileSize: 'Max file size: 5MB',
      };
      return translations[key] || defaultValue || key;
    },
  }),
}));

describe('CsvDropzone', () => {
  const mockOnFileDrop = vi.fn();
  const mockOnDragEnter = vi.fn();
  const mockOnDragLeave = vi.fn();

  const defaultProps = {
    onFileDrop: mockOnFileDrop,
    onDragEnter: mockOnDragEnter,
    onDragLeave: mockOnDragLeave,
    isDragHover: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the dropzone component correctly', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    expect(screen.getByTestId('csv-dropzone')).toBeInTheDocument();
    expect(screen.getByTestId('dropzone-content')).toBeInTheDocument();
    expect(screen.getByTestId('csv-import-image')).toBeInTheDocument();
    expect(screen.getByTestId('upload-button')).toBeInTheDocument();
    expect(screen.getByTestId('max-file-size-text')).toBeInTheDocument();
  });

  it('displays correct text content', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    expect(screen.getByText('Upload file')).toBeInTheDocument();
    expect(screen.getByText('Max file size: 5MB')).toBeInTheDocument();
  });

  it('shows drag hover state when isDragHover is true', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} isDragHover={true} />);

    expect(screen.getByTestId('drag-hover-content')).toBeInTheDocument();
    expect(screen.getByTestId('file-plus-icon')).toBeInTheDocument();
    expect(screen.getByTestId('drop-file-text')).toBeInTheDocument();
    expect(screen.getByText('Drop file here')).toBeInTheDocument();

    // Should not show regular content when hovering
    expect(screen.queryByTestId('csv-import-image')).not.toBeInTheDocument();
    expect(screen.queryByTestId('upload-button')).not.toBeInTheDocument();
  });

  it('shows regular state when isDragHover is false', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} isDragHover={false} />);

    expect(screen.getByTestId('csv-import-image')).toBeInTheDocument();
    expect(screen.getByTestId('upload-button')).toBeInTheDocument();
    expect(screen.getByTestId('max-file-size-text')).toBeInTheDocument();

    // Should not show drag hover content
    expect(screen.queryByTestId('drag-hover-content')).not.toBeInTheDocument();
    expect(screen.queryByTestId('file-plus-icon')).not.toBeInTheDocument();
    expect(screen.queryByTestId('drop-file-text')).not.toBeInTheDocument();
  });

  it('calls onFileDrop when files are dropped', async () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const dropzone = screen.getByTestId('csv-dropzone');
    const file = new File(['csv content'], 'test.csv', { type: 'text/csv' });

    // Create a proper drag event with dataTransfer
    const dropEvent = new Event('drop', { bubbles: true });
    Object.defineProperty(dropEvent, 'dataTransfer', {
      value: {
        files: [file],
        types: ['Files'],
      },
    });

    fireEvent(dropzone, dropEvent);

    await waitFor(() => {
      expect(mockOnFileDrop).toHaveBeenCalledWith([file], expect.any(Object));
    });
  });

  it('handles drag enter event without errors', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const dropzone = screen.getByTestId('csv-dropzone');

    // Test that drag enter event doesn't cause errors
    expect(() => {
      fireEvent.dragEnter(dropzone, {
        dataTransfer: {
          files: [],
          types: ['Files'],
        },
      });
    }).not.toThrow();

    // Verify the component is still rendered correctly after drag enter
    expect(screen.getByTestId('csv-dropzone')).toBeInTheDocument();
  });

  it('calls onDragLeave when drag leave occurs', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const dropzone = screen.getByTestId('csv-dropzone');

    // Create a proper drag leave event with dataTransfer
    const dragLeaveEvent = new Event('dragleave', { bubbles: true });
    Object.defineProperty(dragLeaveEvent, 'dataTransfer', {
      value: {
        files: [],
        types: ['Files'],
      },
    });

    fireEvent(dropzone, dragLeaveEvent);

    expect(mockOnDragLeave).toHaveBeenCalled();
  });

  it('handles upload button click', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const uploadButton = screen.getByTestId('upload-button');

    fireEvent.click(uploadButton);

    // The button currently has an empty onClick handler,
    // so we just verify it doesn't throw an error
    expect(uploadButton).toBeInTheDocument();
  });

  it('accepts only CSV files', async () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const dropzone = screen.getByTestId('csv-dropzone');

    // Test with CSV file (should be accepted)
    const csvFile = new File(['csv content'], 'test.csv', { type: 'text/csv' });

    // Create a proper drag event with dataTransfer
    const dropEvent = new Event('drop', { bubbles: true });
    Object.defineProperty(dropEvent, 'dataTransfer', {
      value: {
        files: [csvFile],
        types: ['Files'],
      },
    });

    fireEvent(dropzone, dropEvent);

    await waitFor(() => {
      expect(mockOnFileDrop).toHaveBeenCalledWith([csvFile], expect.any(Object));
    });
  });

  it('has correct accessibility attributes', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const dropzone = screen.getByTestId('csv-dropzone');

    // The Dropzone component should have appropriate ARIA attributes
    expect(dropzone).toBeInTheDocument();
  });

  it('displays image with correct source', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const image = screen.getByTestId('csv-import-image');
    expect(image).toHaveAttribute('src', 'images/csv_import.png');
  });

  it('renders with correct styling classes', () => {
    renderWithMantine(<CsvDropzone {...defaultProps} />);

    const dropzone = screen.getByTestId('csv-dropzone');

    // The component should have the dropzone class applied
    expect(dropzone).toHaveClass('mantine-Dropzone-root');
  });
});
