import { ColumnIcon } from '@/constants/workspace';
import { ActionIcon, Center, Flex, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { FieldTypes } from '@resola-ai/ui/components';
import { useTableStyles } from '@resola-ai/ui/components/DecaTable/styles';
import { useTranslate } from '@tolgee/react';
import { MantineReactTable, type MRT_ColumnDef, useMantineReactTable } from 'mantine-react-table';
import { useMemo } from 'react';
import type { FieldMapping } from '../../utils';

const useStyles = createStyles(() => ({
  tablePaper: {
    width: '100%',
    height: '100%',
  },
  tableContainer: {
    width: '100%',
    height: '100%',
  },
}));

interface ObjectPreviewProps {
  csvData: string[][];
  fieldMappings: FieldMapping[];
}

const ObjectPreview = ({ csvData = [], fieldMappings = [] }: ObjectPreviewProps) => {
  const { classes: tableClasses } = useTableStyles('sm');
  const { classes, cx } = useStyles();
  const { t } = useTranslate('workspace');

  const renderData = useMemo(() => {
    if (!Array.isArray(csvData) || csvData.length === 0) return [];

    const dataRows = csvData.slice(1);

    return dataRows.map((row) => {
      const rowData: Record<string, any> = {};

      fieldMappings.forEach((mapping, index) => {
        if (!mapping.skipped && row[index] !== undefined) {
          rowData[mapping.csvField] = row[index];
        }
      });

      return rowData;
    });
  }, [csvData, fieldMappings]);

  const renderColumns = useMemo(() => {
    if (!Array.isArray(fieldMappings) || fieldMappings.length === 0) return [];

    return fieldMappings
      .filter((mapping) => !mapping.skipped)
      .map((mapping, index) => {
        const fieldType = mapping.systemFieldType || FieldTypes.SINGLE_LINE_TEXT;
        const FieldIcon = ColumnIcon[fieldType as keyof typeof ColumnIcon] as any;

        // Create unique column ID by combining index and csvField to avoid duplicate keys
        const uniqueColumnId = `${index}_${mapping.csvField}`;

        const columnDef: MRT_ColumnDef<Record<string, any>> = {
          id: uniqueColumnId,
          accessorKey: mapping.csvField,
          header: mapping.csvField,
          size: 200,
          enableEditing: false,
          Header: () => (
            <Flex h={rem(40)} gap={rem(6)} align='center' w='100%' px={rem(12)}>
              {FieldIcon && (
                <ActionIcon variant='transparent' c='decaGrey.6' size='xs'>
                  <FieldIcon />
                </ActionIcon>
              )}
              <Text size='md' fw={500} c='decaGrey.9' truncate>
                {mapping.csvField}
              </Text>
            </Flex>
          ),
          Cell: ({ cell }) => (
            <Flex mih={rem(28)} w='100%' sx={{ userSelect: 'none' }} align='center'>
              <Text size='md' truncate>
                {String(cell.getValue() || '')}
              </Text>
            </Flex>
          ),
        };

        return columnDef;
      });
  }, [fieldMappings]);

  const table = useMantineReactTable({
    columns: renderColumns,
    data: renderData,
    enableColumnActions: false,
    enableSorting: false,
    mantineTableHeadProps: {
      className: tableClasses.tableHead,
    },
    mantineTableContainerProps: {
      className: cx(tableClasses.tableParent, classes.tableContainer),
    },
    mantineTableBodyCellProps: {
      className: tableClasses.cell,
    },
    mantineTableBodyProps: {
      className: tableClasses.tableBody,
    },
    mantinePaperProps: {
      className: classes.tablePaper,
    },
    enableRowVirtualization: true,
    enableColumnVirtualization: true,
    enableTopToolbar: false,
    enableBottomToolbar: false,
    enableTableFooter: false,
    columnVirtualizerOptions: {
      overscan: 5,
      estimateSize: () => 200,
    },
    rowVirtualizerOptions: {
      overscan: 15,
      estimateSize: () => 40,
    },
  });

  if (!renderColumns || renderColumns.length === 0) {
    return (
      <Center p={rem(48)}>
        <Text>{t('csvImport.emptyTable', 'No data to preview')}</Text>
      </Center>
    );
  }

  return <MantineReactTable table={table} />;
};

export default ObjectPreview;
