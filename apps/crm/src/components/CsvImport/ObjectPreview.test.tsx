import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import type { FieldMapping } from '../../utils';
import ObjectPreview from './ObjectPreview';

// Mock the required components and hooks
vi.mock('@mantine/core', () => ({
  ...vi.importActual('@mantine/core'),
  rem: (value: number) => `${value}rem`,
  Center: ({ children }: any) => <div data-testid='center'>{children}</div>,
  Flex: ({ children }: any) => <div data-testid='flex'>{children}</div>,
  ActionIcon: ({ children }: any) => <div data-testid='action-icon'>{children}</div>,
  Text: ({ children }: any) => <div data-testid='text'>{children}</div>,
}));

// Mock translations
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (_: string, defaultValue: string) => defaultValue,
  }),
}));

// Mock MantineReactTable
vi.mock('mantine-react-table', () => ({
  MantineReactTable: ({ table }: any) => {
    const data = table?.options?.data || [];
    const columns = table?.options?.columns || [];
    return (
      <div data-testid='mantine-table'>
        {data.map((row: any, rowIndex: number) => (
          <div key={rowIndex} data-testid='table-row'>
            {columns.map((col: any, colIndex: number) => (
              <div key={colIndex} data-testid='table-cell'>
                {row[col.accessorKey]}
              </div>
            ))}
          </div>
        ))}
      </div>
    );
  },
  useMantineReactTable: ({ data, columns, ...rest }: any) => ({
    getState: vi.fn(),
    options: {
      data,
      columns,
      ...rest,
    },
    table: {
      getHeaderGroups: vi.fn().mockReturnValue([]),
      getRowModel: vi.fn().mockReturnValue({ rows: [] }),
    },
  }),
}));

describe('ObjectPreview', () => {
  const mockCsvData = [
    ['Name', 'Email', 'Phone'],
    ['John', '<EMAIL>', '1234567890'],
    ['Jane', '<EMAIL>', '0987654321'],
  ];

  const mockFieldMappings: FieldMapping[] = [
    {
      csvField: 'Name',
      systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
      systemFieldId: null,
      systemFieldName: null,
      skipped: false,
      isExistingField: false,
    },
    {
      csvField: 'Email',
      systemFieldType: FieldTypes.EMAIL,
      systemFieldId: null,
      systemFieldName: null,
      skipped: false,
      isExistingField: false,
    },
    {
      csvField: 'Phone',
      systemFieldType: FieldTypes.PHONE_NUMBER,
      systemFieldId: null,
      systemFieldName: null,
      skipped: false,
      isExistingField: false,
    },
  ];

  it('renders empty state when no data is provided', () => {
    renderWithMantine(<ObjectPreview csvData={[]} fieldMappings={[]} />);
    expect(screen.getByText('No data to preview')).toBeInTheDocument();
  });

  it('renders table with data', () => {
    renderWithMantine(<ObjectPreview csvData={mockCsvData} fieldMappings={mockFieldMappings} />);
    const cells = screen.getAllByTestId('table-cell');
    expect(cells).toHaveLength(6); // 2 rows * 3 columns
    expect(cells[0]).toHaveTextContent('John');
    expect(cells[1]).toHaveTextContent('<EMAIL>');
    expect(cells[2]).toHaveTextContent('1234567890');
  });

  it('skips columns marked as skipped', () => {
    const skippedFieldMappings: FieldMapping[] = [
      ...mockFieldMappings.slice(0, 1),
      {
        ...mockFieldMappings[1],
        skipped: true, // Skip email column
      },
      ...mockFieldMappings.slice(2),
    ];

    renderWithMantine(<ObjectPreview csvData={mockCsvData} fieldMappings={skippedFieldMappings} />);

    const cells = screen.getAllByTestId('table-cell');
    expect(cells).toHaveLength(4); // 2 rows * 2 columns (email skipped)
    expect(cells[0]).toHaveTextContent('John');
    expect(cells[1]).toHaveTextContent('1234567890');
  });

  it('handles undefined values in data', () => {
    const csvDataWithUndefined: string[][] = [
      ['Name', 'Email', 'Phone'],
      ['', '<EMAIL>', '1234567890'], // Empty string instead of undefined
    ];

    renderWithMantine(
      <ObjectPreview csvData={csvDataWithUndefined} fieldMappings={mockFieldMappings} />
    );

    const cells = screen.getAllByTestId('table-cell');
    expect(cells).toHaveLength(3); // 1 row * 3 columns
    expect(cells[0]).toHaveTextContent('');
    expect(cells[1]).toHaveTextContent('<EMAIL>');
    expect(cells[2]).toHaveTextContent('1234567890');
  });

  it('applies table virtualization settings', () => {
    const manyRows = Array(100)
      .fill(null)
      .map((_, index) => [`User ${index}`, `user${index}@example.com`, `123456789${index}`]);

    const csvDataWithManyRows = [['Name', 'Email', 'Phone'], ...manyRows];

    renderWithMantine(
      <ObjectPreview csvData={csvDataWithManyRows} fieldMappings={mockFieldMappings} />
    );

    const rows = screen.getAllByTestId('table-row');
    expect(rows.length).toBeGreaterThan(0);
    expect(screen.getByTestId('mantine-table')).toBeInTheDocument();
  });
});
