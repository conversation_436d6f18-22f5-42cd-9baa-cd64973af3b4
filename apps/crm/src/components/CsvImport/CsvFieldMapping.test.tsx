import { renderWithMantine } from '@/tests/utils/testUtils';
import { FieldTypes } from '@resola-ai/ui/components';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';
import type { ObjectColumn } from '../../models';
import type { FieldMapping } from '../../utils';
import CsvFieldMapping from './CsvFieldMapping';

// Mock the dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, defaultValue?: string) => defaultValue || key,
  }),
  Tolgee: () => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
  }),
  TolgeeProvider: ({ children }: { children: React.ReactNode }) => children,
  T: ({ keyName, defaultValue }: { keyName: string; defaultValue?: string }) =>
    defaultValue || keyName,
  useTolgee: () => ({}),
  BackendProvider: ({ children }: { children: React.ReactNode }) => children,
  FormatSimple: () => ({}),
}));

vi.mock('@tolgee/web/tools', () => ({
  InContextTools: () => ({}),
}));

vi.mock('../../hooks', () => ({
  useObject: () => ({
    object: null,
  }),
}));

vi.mock('../Workspace/FieldSettings/AddFieldForm/useOptions', () => ({
  useOptions: () => ({
    options: [
      {
        value: 'text',
        label: 'Single Line Text',
        icon: <div>TextIcon</div>,
      },
      {
        value: 'number',
        label: 'Number',
        icon: <div>NumberIcon</div>,
      },
      {
        value: 'email',
        label: 'Email',
        icon: <div>EmailIcon</div>,
      },
    ],
  }),
}));

vi.mock('./FieldMappingList', () => ({
  default: ({ fieldMappings, handleMainFieldSelectionChange, handleNewFieldTypeChange }: any) => (
    <div data-testid='field-mapping-list'>
      <div data-testid='field-mappings-count'>{fieldMappings.length}</div>
      {fieldMappings.map((mapping: FieldMapping, index: number) => (
        <div key={index} data-testid={`field-mapping-${index}`}>
          <span data-testid={`csv-field-${index}`}>{mapping.csvField}</span>
          <span data-testid={`system-field-type-${index}`}>{mapping.systemFieldType}</span>
          <span data-testid={`is-existing-${index}`}>{mapping.isExistingField.toString()}</span>
          <span data-testid={`skipped-${index}`}>{mapping.skipped.toString()}</span>
          <button
            data-testid={`change-main-field-${index}`}
            onClick={() => handleMainFieldSelectionChange(mapping.csvField, 'skip')}
          >
            Change Main Field
          </button>
          <button
            data-testid={`change-field-type-${index}`}
            onClick={() => handleNewFieldTypeChange(mapping.csvField, 'number')}
          >
            Change Field Type
          </button>
        </div>
      ))}
    </div>
  ),
}));

vi.mock('./useImportStyle', () => ({
  useImportStyles: () => ({
    classes: {
      container: 'container',
    },
  }),
}));

const mockCsvData = [
  ['Name', 'Email', 'Phone Number', 'Company'],
  ['John Doe', '<EMAIL>', '************', 'Acme Corp'],
  ['Jane Smith', '<EMAIL>', '************', 'Tech Inc'],
];

const mockObjectFields: ObjectColumn[] = [
  {
    id: 'field1',
    name: 'Name',
    type: FieldTypes.SINGLE_LINE_TEXT,
    header: 'Name',
  },
  {
    id: 'field2',
    name: 'Email',
    type: FieldTypes.EMAIL,
    header: 'Email',
  },
  {
    id: 'field3',
    name: 'Phone',
    type: FieldTypes.PHONE_NUMBER,
    header: 'Phone',
  },
];

const mockFieldMappings: FieldMapping[] = [
  {
    csvField: 'Name',
    systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
    systemFieldId: 'field1',
    systemFieldName: 'Name',
    isExistingField: true,
    skipped: false,
  },
  {
    csvField: 'Email',
    systemFieldType: FieldTypes.EMAIL,
    systemFieldId: 'field2',
    systemFieldName: 'Email',
    isExistingField: true,
    skipped: false,
  },
  {
    csvField: 'Phone Number',
    systemFieldType: FieldTypes.SINGLE_LINE_TEXT,
    systemFieldId: null,
    systemFieldName: null,
    isExistingField: false,
    skipped: false,
  },
  {
    csvField: 'Company',
    systemFieldType: null,
    systemFieldId: null,
    systemFieldName: null,
    isExistingField: false,
    skipped: true,
  },
];

const defaultProps = {
  csvData: mockCsvData,
  onFieldMappingChange: vi.fn(),
  fileName: 'test.csv',
  objectName: 'Contact',
  objectFields: mockObjectFields,
  fieldMappings: mockFieldMappings,
};

const renderComponent = (props = {}) => {
  return renderWithMantine(<CsvFieldMapping {...defaultProps} {...props} />);
};

describe('CsvFieldMapping', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders the component with InfoAlert', () => {
      renderComponent();

      expect(screen.getByText('csvFieldMappingInfo')).toBeInTheDocument();
    });

    it('renders FieldMappingList with correct props', () => {
      renderComponent();

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();
      expect(screen.getByTestId('field-mappings-count')).toHaveTextContent('4');
    });

    it('renders import summary when fileName and objectName are provided', () => {
      renderComponent();

      // Check that the import summary container is present
      // Since it uses dangerouslySetInnerHTML with translation, we'll look for any text content
      const container = screen.getByTestId('field-mapping-list').parentElement?.parentElement;
      expect(container).toBeInTheDocument();

      // Verify fileName and objectName are provided (which enables the summary)
      expect(defaultProps.fileName).toBeDefined();
      expect(defaultProps.objectName).toBeDefined();
    });

    it('does not render import summary when fileName is missing', () => {
      renderComponent({ fileName: undefined });

      expect(screen.queryByText('importSummary')).not.toBeInTheDocument();
    });

    it('does not render import summary when objectName is missing', () => {
      renderComponent({ objectName: undefined });

      expect(screen.queryByText('importSummary')).not.toBeInTheDocument();
    });
  });

  describe('Field Mapping Initialization', () => {
    it('initializes field mappings when fieldMappings prop is empty', async () => {
      const onFieldMappingChange = vi.fn();
      renderComponent({
        fieldMappings: [],
        onFieldMappingChange,
      });

      await waitFor(() => {
        expect(onFieldMappingChange).toHaveBeenCalled();
      });

      const calledWith = onFieldMappingChange.mock.calls[0][0];
      expect(calledWith).toHaveLength(4); // 4 CSV fields
      expect(calledWith[0].csvField).toBe('Name');
      expect(calledWith[1].csvField).toBe('Email');
      expect(calledWith[2].csvField).toBe('Phone Number');
      expect(calledWith[3].csvField).toBe('Company');
    });

    it('auto-matches existing fields correctly', async () => {
      const onFieldMappingChange = vi.fn();
      renderComponent({
        fieldMappings: [],
        onFieldMappingChange,
      });

      await waitFor(() => {
        expect(onFieldMappingChange).toHaveBeenCalled();
      });

      const calledWith = onFieldMappingChange.mock.calls[0][0];

      // Name field should match
      const nameMapping = calledWith.find((m: FieldMapping) => m.csvField === 'Name');
      expect(nameMapping?.isExistingField).toBe(true);
      expect(nameMapping?.systemFieldId).toBe('field1');

      // Email field should match
      const emailMapping = calledWith.find((m: FieldMapping) => m.csvField === 'Email');
      expect(emailMapping?.isExistingField).toBe(true);
      expect(emailMapping?.systemFieldId).toBe('field2');
    });

    it('handles new object creation mode', async () => {
      const onFieldMappingChange = vi.fn();
      renderComponent({
        fieldMappings: [],
        objectFields: [], // Empty for new objects
        onFieldMappingChange,
      });

      await waitFor(() => {
        expect(onFieldMappingChange).toHaveBeenCalled();
      });

      const calledWith = onFieldMappingChange.mock.calls[0][0];

      // All fields should be new (not existing)
      calledWith.forEach((mapping: FieldMapping) => {
        expect(mapping.isExistingField).toBe(false);
        expect(mapping.systemFieldId).toBe(null);
      });
    });
  });

  describe('Field Mapping Changes', () => {
    it('handles main field selection change', async () => {
      const user = userEvent.setup();
      renderComponent();

      const changeButton = screen.getByTestId('change-main-field-0');
      await user.click(changeButton);

      expect(defaultProps.onFieldMappingChange).toHaveBeenCalled();
    });

    it('handles field type change', async () => {
      const user = userEvent.setup();
      renderComponent();

      const changeButton = screen.getByTestId('change-field-type-0');
      await user.click(changeButton);

      expect(defaultProps.onFieldMappingChange).toHaveBeenCalled();
    });

    it('updates field mapping when skipping a field', async () => {
      const user = userEvent.setup();
      const onFieldMappingChange = vi.fn();
      renderComponent({ onFieldMappingChange });

      const changeButton = screen.getByTestId('change-main-field-0');
      await user.click(changeButton);

      expect(onFieldMappingChange).toHaveBeenCalled();
      const calledWith = onFieldMappingChange.mock.calls[0][0];
      const updatedMapping = calledWith.find((m: FieldMapping) => m.csvField === 'Name');
      expect(updatedMapping?.skipped).toBe(true);
    });
  });

  describe('Field Mapping Display', () => {
    it('displays correct field mapping information', () => {
      renderComponent();

      // Check that field mappings are displayed correctly
      expect(screen.getByTestId('csv-field-0')).toHaveTextContent('Name');
      expect(screen.getByTestId('system-field-type-0')).toHaveTextContent(
        FieldTypes.SINGLE_LINE_TEXT
      );
      expect(screen.getByTestId('is-existing-0')).toHaveTextContent('true');
      expect(screen.getByTestId('skipped-0')).toHaveTextContent('false');

      expect(screen.getByTestId('csv-field-1')).toHaveTextContent('Email');
      expect(screen.getByTestId('system-field-type-1')).toHaveTextContent(FieldTypes.EMAIL);
      expect(screen.getByTestId('is-existing-1')).toHaveTextContent('true');
      expect(screen.getByTestId('skipped-1')).toHaveTextContent('false');

      expect(screen.getByTestId('csv-field-2')).toHaveTextContent('Phone Number');
      expect(screen.getByTestId('system-field-type-2')).toHaveTextContent(
        FieldTypes.SINGLE_LINE_TEXT
      );
      expect(screen.getByTestId('is-existing-2')).toHaveTextContent('false');
      expect(screen.getByTestId('skipped-2')).toHaveTextContent('false');

      expect(screen.getByTestId('csv-field-3')).toHaveTextContent('Company');
      expect(screen.getByTestId('is-existing-3')).toHaveTextContent('false');
      expect(screen.getByTestId('skipped-3')).toHaveTextContent('true');
    });
  });

  describe('Edge Cases', () => {
    it('handles empty CSV data', () => {
      renderComponent({ csvData: [] });

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();
      expect(screen.getByTestId('field-mappings-count')).toHaveTextContent('4');
    });

    it('handles CSV data with only headers', () => {
      renderComponent({ csvData: [['Name', 'Email']] });

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();
    });

    it('handles undefined objectFields', () => {
      renderComponent({ objectFields: undefined });

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();
    });

    it('handles empty objectFields array', () => {
      renderComponent({ objectFields: [] });

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();
    });

    it('handles missing field type options', () => {
      // This would require mocking useOptions to return empty options
      renderComponent();

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();
    });
  });

  describe('Callback Functions', () => {
    it('calls onFieldMappingChange when field mappings are updated', async () => {
      const user = userEvent.setup();
      const onFieldMappingChange = vi.fn();
      renderComponent({ onFieldMappingChange });

      const changeButton = screen.getByTestId('change-main-field-0');
      await user.click(changeButton);

      expect(onFieldMappingChange).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            csvField: 'Name',
            skipped: true,
          }),
        ])
      );
    });

    it('maintains other field mappings when updating one', async () => {
      const user = userEvent.setup();
      const onFieldMappingChange = vi.fn();
      renderComponent({ onFieldMappingChange });

      const changeButton = screen.getByTestId('change-main-field-0');
      await user.click(changeButton);

      const calledWith = onFieldMappingChange.mock.calls[0][0];
      expect(calledWith).toHaveLength(4); // Should maintain all 4 mappings

      // Other mappings should remain unchanged
      const emailMapping = calledWith.find((m: FieldMapping) => m.csvField === 'Email');
      expect(emailMapping?.isExistingField).toBe(true);
      expect(emailMapping?.systemFieldId).toBe('field2');
    });
  });

  describe('Performance', () => {
    it('does not re-initialize mappings when fieldMappings is not empty', () => {
      const onFieldMappingChange = vi.fn();
      renderComponent({ onFieldMappingChange });

      // Should not call onFieldMappingChange since fieldMappings is already populated
      expect(onFieldMappingChange).not.toHaveBeenCalled();
    });

    it('memoizes expensive calculations', () => {
      // This test verifies that the component doesn't crash with memoization
      // and that expensive calculations are properly memoized
      renderComponent();

      expect(screen.getByTestId('field-mapping-list')).toBeInTheDocument();

      // Test that changing unrelated props doesn't trigger unnecessary re-calculations
      renderComponent({ fileName: 'different-file.csv' });
      expect(screen.getAllByTestId('field-mapping-list')).toHaveLength(2);
    });
  });
});
