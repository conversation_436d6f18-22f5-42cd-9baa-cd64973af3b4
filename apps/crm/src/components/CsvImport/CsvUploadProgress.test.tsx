import { renderWithMantine } from '@/tests/utils/testUtils';
import { screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import CsvUploadProgress from './CsvUploadProgress';

// Mock the @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, defaultValue?: string) => {
      const translations: Record<string, string> = {
        isUploading: 'is uploading',
      };
      return translations[key] || defaultValue || key;
    },
  }),
}));

describe('CsvUploadProgress', () => {
  const defaultProps = {
    fileName: 'test-file.csv',
    progress: 50,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component correctly', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    expect(screen.getByTestId('csv-upload-progress')).toBeInTheDocument();
    expect(screen.getByTestId('progress-dots-container')).toBeInTheDocument();
    expect(screen.getByTestId('progress-text')).toBeInTheDocument();
  });

  it('displays all three progress dots', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    expect(screen.getByTestId('progress-dot-1')).toBeInTheDocument();
    expect(screen.getByTestId('progress-dot-2')).toBeInTheDocument();
    expect(screen.getByTestId('progress-dot-3')).toBeInTheDocument();
  });

  it('displays the correct progress text with file name and percentage', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('test-file.csv is uploading (50%)');
  });

  it('displays correct text for different file names', () => {
    renderWithMantine(<CsvUploadProgress fileName='users.csv' progress={25} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('users.csv is uploading (25%)');
  });

  it('displays correct text for different progress values', () => {
    renderWithMantine(<CsvUploadProgress fileName='data.csv' progress={75} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('data.csv is uploading (75%)');
  });

  it('handles 0% progress correctly', () => {
    renderWithMantine(<CsvUploadProgress fileName='start.csv' progress={0} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('start.csv is uploading (0%)');
  });

  it('handles 100% progress correctly', () => {
    renderWithMantine(<CsvUploadProgress fileName='complete.csv' progress={100} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('complete.csv is uploading (100%)');
  });

  it('handles file names with special characters', () => {
    renderWithMantine(<CsvUploadProgress fileName='test-file_data (1).csv' progress={60} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('test-file_data (1).csv is uploading (60%)');
  });

  it('handles empty file name gracefully', () => {
    renderWithMantine(<CsvUploadProgress fileName='' progress={30} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('is uploading (30%)');
  });

  it('displays progress dots with correct structure', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    const dot1 = screen.getByTestId('progress-dot-1');
    const dot2 = screen.getByTestId('progress-dot-2');
    const dot3 = screen.getByTestId('progress-dot-3');

    // Check that dots are div elements (indicating they're styled elements)
    expect(dot1.tagName).toBe('DIV');
    expect(dot2.tagName).toBe('DIV');
    expect(dot3.tagName).toBe('DIV');
  });

  it('has correct container structure', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    const container = screen.getByTestId('csv-upload-progress');
    expect(container).toBeInTheDocument();
    expect(container.tagName).toBe('DIV');
  });

  it('has correct dots container structure', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    const dotsContainer = screen.getByTestId('progress-dots-container');
    expect(dotsContainer).toBeInTheDocument();
    expect(dotsContainer.tagName).toBe('DIV');
  });

  it('has correct progress text structure', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toBeInTheDocument();
    expect(progressText.tagName).toBe('P');
  });

  it('uses translation hook correctly', () => {
    renderWithMantine(<CsvUploadProgress {...defaultProps} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('is uploading');
  });

  it('handles negative progress values', () => {
    renderWithMantine(<CsvUploadProgress fileName='test.csv' progress={-10} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('test.csv is uploading (-10%)');
  });

  it('handles progress values over 100', () => {
    renderWithMantine(<CsvUploadProgress fileName='test.csv' progress={150} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('test.csv is uploading (150%)');
  });

  it('handles decimal progress values', () => {
    renderWithMantine(<CsvUploadProgress fileName='test.csv' progress={45.5} />);

    const progressText = screen.getByTestId('progress-text');
    expect(progressText).toHaveTextContent('test.csv is uploading (45.5%)');
  });
});
