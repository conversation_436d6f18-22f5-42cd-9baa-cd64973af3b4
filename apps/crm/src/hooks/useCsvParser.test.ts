import type { FileWithPath } from '@mantine/dropzone';
import { act, renderHook, waitFor } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';
import { useCsvParser } from './useCsvParser';

describe('useCsvParser', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useCsvParser(null));

    expect(result.current.csvData).toEqual([]);
    expect(result.current.isLoadingCsv).toBe(false);
    expect(result.current.isUploading).toBe(false);
    expect(result.current.uploadProgress).toBe(0);
  });

  it('should handle CSV parsing with simple data', async () => {
    const mockFile = {
      text: vi
        .fn()
        .mockResolvedValue('name,email,age\nJohn,<EMAIL>,25\nJane,<EMAIL>,30'),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    // Initially should start uploading
    expect(result.current.isUploading).toBe(true);
    expect(result.current.uploadProgress).toBe(0);

    // Wait for upload simulation and CSV parsing to complete
    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    // Check parsed CSV data
    expect(result.current.csvData).toEqual([
      ['name', 'email', 'age'],
      ['John', '<EMAIL>', '25'],
      ['Jane', '<EMAIL>', '30'],
    ]);
  });

  it('should handle CSV parsing with quoted values', async () => {
    const mockFile = {
      text: vi
        .fn()
        .mockResolvedValue(
          'name,description\n"John Doe","A person with, comma in description"\n"Jane Smith","Another person"'
        ),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(result.current.csvData).toEqual([
      ['name', 'description'],
      ['John Doe', 'A person with, comma in description'],
      ['Jane Smith', 'Another person'],
    ]);
  });

  it('should limit CSV data to 11 rows', async () => {
    const csvRows = Array.from({ length: 15 }, (_, i) => `row${i},value${i}`);
    const csvContent = `header1,header2\n${csvRows.join('\n')}`;

    const mockFile = {
      text: vi.fn().mockResolvedValue(csvContent),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    // Should only have 11 rows (header + 10 data rows)
    expect(result.current.csvData).toHaveLength(11);
    expect(result.current.csvData[0]).toEqual(['header1', 'header2']);
    expect(result.current.csvData[10]).toEqual(['row9', 'value9']);
  });

  it('should filter out empty lines', async () => {
    const mockFile = {
      text: vi
        .fn()
        .mockResolvedValue('name,email\n\nJohn,<EMAIL>\n\n\nJane,<EMAIL>\n\n'),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(result.current.csvData).toEqual([
      ['name', 'email'],
      ['John', '<EMAIL>'],
      ['Jane', '<EMAIL>'],
    ]);
  });

  it('should handle file parsing errors', async () => {
    const mockFile = {
      text: vi.fn().mockRejectedValue(new Error('File read error')),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    // The important part: hook should gracefully handle errors and return empty data
    expect(result.current.csvData).toEqual([]);
  });

  it('should reset state when uploadedFile becomes null', async () => {
    const mockFile = {
      text: vi.fn().mockResolvedValue('name,email\nJohn,<EMAIL>'),
    } as unknown as FileWithPath;

    const { result, rerender } = renderHook(
      ({ file }: { file: FileWithPath | null }) => useCsvParser(file),
      { initialProps: { file: mockFile as FileWithPath | null } }
    );

    await waitFor(
      () => {
        expect(result.current.csvData).toHaveLength(2);
      },
      { timeout: 3000 }
    );

    // Reset by setting file to null
    act(() => {
      rerender({ file: null });
    });

    expect(result.current.csvData).toEqual([]);
    expect(result.current.isUploading).toBe(false);
    expect(result.current.uploadProgress).toBe(0);
  });

  it('should handle upload progress simulation correctly', async () => {
    const mockFile = {
      text: vi.fn().mockResolvedValue('name,email\nJohn,<EMAIL>'),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    expect(result.current.isUploading).toBe(true);
    expect(result.current.uploadProgress).toBe(0);

    // Wait for upload to complete
    await waitFor(
      () => {
        expect(result.current.uploadProgress).toBe(100);
      },
      { timeout: 2000 }
    );

    // Wait for upload to finish
    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
      },
      { timeout: 3000 }
    );
  });

  it('should handle CSV with different delimiters within quotes', async () => {
    const mockFile = {
      text: vi
        .fn()
        .mockResolvedValue(
          'name,description\n"John, Jr.","Description with \\"quotes\\" and, commas"'
        ),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(result.current.csvData).toEqual([
      ['name', 'description'],
      ['John, Jr.', 'Description with \\quotes\\ and, commas'],
    ]);
  });

  it('should handle empty CSV file', async () => {
    const mockFile = {
      text: vi.fn().mockResolvedValue(''),
    } as unknown as FileWithPath;

    const { result } = renderHook(() => useCsvParser(mockFile));

    await waitFor(
      () => {
        expect(result.current.isUploading).toBe(false);
        expect(result.current.isLoadingCsv).toBe(false);
      },
      { timeout: 3000 }
    );

    expect(result.current.csvData).toEqual([]);
  });
});
