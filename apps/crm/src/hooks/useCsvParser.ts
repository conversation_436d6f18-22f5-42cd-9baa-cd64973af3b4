import type { FileWithPath } from '@mantine/dropzone';
import { useEffect, useState } from 'react';

export const useCsvParser = (uploadedFile: FileWithPath | null) => {
  const [csvData, setCsvData] = useState<string[][]>([]);
  const [isLoadingCsv, setIsLoadingCsv] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const simulateUpload = async (): Promise<void> => {
    setIsUploading(true);
    setUploadProgress(0);

    // Simulate upload progress
    const totalDuration = 1000; // 1 second
    const intervalDuration = 50; // Update every 50ms
    const increment = (intervalDuration / totalDuration) * 100;

    let currentProgress = 0;

    const interval = setInterval(() => {
      currentProgress += increment;
      if (currentProgress >= 100) {
        currentProgress = 100;
        setUploadProgress(100);
        clearInterval(interval);
        setTimeout(() => {
          setIsUploading(false);
        }, 200);
      } else {
        setUploadProgress(Math.round(currentProgress));
      }
    }, intervalDuration);
  };

  const parseCsvFile = async (file: FileWithPath) => {
    setIsLoadingCsv(true);
    try {
      const text = await file.text();
      const lines = text.split('\n').filter((line) => line.trim() !== '');
      const data = lines.slice(0, 11).map((line) => {
        // Simple CSV parsing - handles basic cases
        const result: string[] = [];
        let current = '';
        let inQuotes = false;

        for (let i = 0; i < line.length; i++) {
          const char = line[i];

          if (char === '"') {
            inQuotes = !inQuotes;
          } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
          } else {
            current += char;
          }
        }
        result.push(current.trim());
        return result;
      });
      setCsvData(data);
    } catch (error) {
      console.error('Error parsing CSV:', error);
      setCsvData([]);
    } finally {
      setIsLoadingCsv(false);
    }
  };

  useEffect(() => {
    if (uploadedFile) {
      // First simulate upload, then parse
      simulateUpload().then(() => {
        parseCsvFile(uploadedFile);
      });
    } else {
      setCsvData([]);
      setIsUploading(false);
      setUploadProgress(0);
    }
  }, [uploadedFile]);

  return {
    csvData,
    isLoadingCsv,
    isUploading,
    uploadProgress,
  };
};
