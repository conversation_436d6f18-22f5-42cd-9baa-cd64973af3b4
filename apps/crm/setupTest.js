import '@testing-library/jest-dom';
import * as matchers from '@testing-library/jest-dom/matchers';
import { cleanup } from '@testing-library/react';
import { afterEach, expect, vi } from 'vitest';
import './src/polyfills/promiseWithResolvers';

const originalError = console.error;
const originalWarn = console.warn;

// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };

  Object.defineProperty(window, 'ResizeObserver', {
    writable: true,
    value: vi.fn().mockImplementation(() => ({
      disconnect: vi.fn(),
      observe: vi.fn(),
      unobserve: vi.fn(),
    })),
  });
});

vi.mock('@tolgee/react', () => {
  const mockTolgee = vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    getInitialOptions: vi.fn(() => ({
      language: 'en',
      fallbackLanguage: 'en',
      defaultNs: 'common',
      ns: ['workspace', 'common'],
      staticData: {},
      onFormatError: vi.fn((error) => error),
    })),
    t: vi.fn((key) => key),
  }));

  const mockFormatSimple = vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
  }));

  return {
    TolgeeProvider: ({ children }) => children,
    useTranslate: () => ({
      t: vi.fn((key) => key),
    }),
    useTolgee: () => ({
      tolgee: {
        getLanguage: vi.fn(),
        changeLanguage: vi.fn(),
        t: vi.fn((key) => key),
      },
    }),
    Tolgee: mockTolgee,
    FormatSimple: mockFormatSimple,
  };
});

// Additional Tolgee mocks that might be imported elsewhere
vi.mock('@tolgee/web', () => ({
  Tolgee: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
    getInitialOptions: vi.fn(() => ({
      language: 'en',
      fallbackLanguage: 'en',
      defaultNs: 'common',
      ns: ['workspace', 'common'],
      staticData: {},
      onFormatError: vi.fn((error) => error),
    })),
  })),
}));

// Mock for @tolgee/web/tools
vi.mock('@tolgee/web/tools', () => ({
  InContextTools: vi.fn(() => ({
    use: vi.fn().mockReturnThis(),
    init: vi.fn().mockReturnThis(),
  })),
}));

// Global API mocks to prevent network errors during tests
vi.mock('@/services/api', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    WorkspaceAPI: {
      getFirst: vi.fn().mockResolvedValue({ id: 'default-workspace-123' }),
      getById: vi.fn().mockResolvedValue({ id: 'workspace-123' }),
      ...actual.WorkspaceAPI,
    },
    ObjectAPI: {
      get: vi.fn().mockResolvedValue({ id: 'object-1', name: 'Test Object' }),
      save: vi.fn().mockResolvedValue({ id: 'object-1' }),
      update: vi.fn().mockResolvedValue({ id: 'object-1' }),
      findTemplate: vi.fn().mockResolvedValue({ content: btoa('<template></template>') }),
      delete: vi.fn().mockResolvedValue({ success: true }),
      clearData: vi.fn().mockResolvedValue({ success: true }),
      ...actual.ObjectAPI,
    },
    RecordAPI: {
      getRecords: vi.fn().mockResolvedValue([]),
      getRecordById: vi.fn().mockResolvedValue({ id: 'record-1' }),
      save: vi.fn().mockResolvedValue({ id: 'record-1' }),
      update: vi.fn().mockResolvedValue({ id: 'record-1' }),
      updateByField: vi.fn().mockResolvedValue({ id: 'record-1' }),
      delete: vi.fn().mockResolvedValue({ success: true }),
      ...actual.RecordAPI,
    },
    TagAPI: {
      getList: vi.fn().mockResolvedValue([]),
      save: vi.fn().mockResolvedValue({ id: 'tag-1' }),
      delete: vi.fn().mockResolvedValue({ success: true }),
      ...actual.TagAPI,
    },
    ViewAPI: {
      get: vi.fn().mockResolvedValue({ id: 'view-1' }),
      save: vi.fn().mockResolvedValue({ id: 'view-1' }),
      update: vi.fn().mockResolvedValue({ id: 'view-1' }),
      delete: vi.fn().mockResolvedValue({ success: true }),
      ...actual.ViewAPI,
    },
    FieldAPI: {
      save: vi.fn().mockResolvedValue({ id: 'field-1' }),
      update: vi.fn().mockResolvedValue({ id: 'field-1' }),
      delete: vi.fn().mockResolvedValue({ success: true }),
      ...actual.FieldAPI,
    },
  };
});

// Mock @mantine/core components globally
vi.mock('@mantine/core', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    MantineProvider: ({ children }) => children,
    useMantineTheme: vi.fn(() => ({
      colors: {
        decaBlue: ['#E7F5FF', '#D0EBFF', '#A5D8FF', '#74C0FC', '#4DABF7', '#339AF0', '#228BE6'],
        decaGreen: ['#E6FCF5', '#087F5B'],
        decaRed: ['#FFE3E3', '#C92A2A'],
        decaLight: ['#FFFFFF', '#F8F9FA', '#E9ECEF', '#DEE2E6', '#CED4DA', '#ADB5BD'],
        decaNavy: ['#E3F2FD', '#BBDEFB', '#90CAF9', '#64B5F6', '#42A5F5', '#2196F3'],
        decaGrey: [
          '#FAFAFA',
          '#F5F5F5',
          '#EEEEEE',
          '#E0E0E0',
          '#BDBDBD',
          '#9E9E9E',
          '#757575',
          '#616161',
          '#424242',
          '#212121',
        ],
      },
      white: '#FFFFFF',
      fn: {
        size: (value) => `${value}px`,
        rem: (value) => `${value}rem`,
      },
    })),
    Group: vi.fn(({ children }) => children),
    Text: vi.fn(({ children }) => children),
    Anchor: vi.fn(({ children }) => children),
    Flex: vi.fn(({ children }) => children),
    Box: vi.fn(({ children }) => children),
    Stack: vi.fn(({ children }) => children),
    rem: (value) => `${value}rem`,
    createTheme: vi.fn((theme) => ({ ...theme, colorScheme: 'light' })),
  };
});

// Mock @mantine/notifications
vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
    hide: vi.fn(),
    clean: vi.fn(),
    cleanQueue: vi.fn(),
    updateState: vi.fn(),
  },
}));

// Mock @mantine/hooks
vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(() => [false, { open: vi.fn(), close: vi.fn(), toggle: vi.fn() }]),
}));

// Mock @mantine/emotion
vi.mock('@mantine/emotion', () => ({
  MantineEmotionProvider: ({ children }) => children,
  createStyles: vi.fn(() => () => ({ classes: {}, cx: vi.fn() })),
  keyframes: vi.fn(() => 'mock-keyframes'),
}));

// Mock notification utilities
vi.mock('@/utils', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useShowNewRecordNotification: vi.fn(() => vi.fn()),
    useShowRecordUpdateNotification: vi.fn(() => vi.fn()),
    useShowTemplateSavedNotification: vi.fn(() => vi.fn()),
    useShowMessageSentNotification: vi.fn(() => vi.fn()),
    useShowMessageFailedNotification: vi.fn(() => vi.fn()),
    useShowMessageUndoNotification: vi.fn(() => vi.fn()),
    handleColumnOrderChange: vi.fn(),
    handleColumnSizingChange: vi.fn(),
    mapColumnsToView: vi.fn(() => ({
      fieldOrder: ['col1', 'col2'],
      fields: [
        { id: 'col1', size: 200 },
        { id: 'col2', size: 250 },
      ],
    })),
  };
});

// Mock axios to prevent any network requests that might bypass the API layer
vi.mock('axios', () => ({
  default: {
    get: vi.fn().mockResolvedValue({ data: {} }),
    post: vi.fn().mockResolvedValue({ data: {} }),
    put: vi.fn().mockResolvedValue({ data: {} }),
    patch: vi.fn().mockResolvedValue({ data: {} }),
    delete: vi.fn().mockResolvedValue({ data: {} }),
    request: vi.fn().mockResolvedValue({ data: {} }),
    create: vi.fn().mockReturnThis(),
    interceptors: {
      request: { use: vi.fn() },
      response: { use: vi.fn() },
    },
  },
}));

// Extend Vitest's expect method with testing-library methods
expect.extend(matchers);

// Cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Mock matchMedia for testing
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});
