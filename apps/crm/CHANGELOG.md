# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

### [1.15.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.15.1) (2025-07-14)


### Features

* **crm:** Implement toggle for showing hidden fields with localStorage persistence ([#5901](https://github.com/resola-ai/deca-apps/issues/5901)) ([5f4084d](https://github.com/resola-ai/deca-apps/commit/5f4084d383f46a729780918e62e1c70827f8b4e1))
* **crm:** integrate breadcrumb navigation and enhance permission checks ([#5984](https://github.com/resola-ai/deca-apps/issues/5984)) ([ca564c5](https://github.com/resola-ai/deca-apps/commit/ca564c5ed70a5f930422f4cbe41aca63d0c6aec8))
* **crm:** tk-7470 improve sidebar drag ([#5955](https://github.com/resola-ai/deca-apps/issues/5955)) ([0e1d59e](https://github.com/resola-ai/deca-apps/commit/0e1d59ee0b7fd52967b592dea52c55befbc6db26))
* **crm:** TK-8033 - update currency field UI ([#5991](https://github.com/resola-ai/deca-apps/issues/5991)) ([b5b7f9c](https://github.com/resola-ai/deca-apps/commit/b5b7f9c490bee6438f1cfec91f2c6678773f6a62))
* **crm:** tk-8334 - add no access view ([#5883](https://github.com/resola-ai/deca-apps/issues/5883)) ([d66f55f](https://github.com/resola-ai/deca-apps/commit/d66f55f8570139b6d1e5ff5c14ed15f0fb7322a8))
* **crm:** TK-8334 - improve no view access list ([#5931](https://github.com/resola-ai/deca-apps/issues/5931)) ([0a1cf27](https://github.com/resola-ai/deca-apps/commit/0a1cf2764a543b362565de209e0f594277305723))
* **crm:** update Breadcrumbs component to allow more items ([#6033](https://github.com/resola-ai/deca-apps/issues/6033)) ([dae8cb3](https://github.com/resola-ai/deca-apps/commit/dae8cb30a49f34312c222b857acff4fc885dad12))


### Bug Fixes

* **crm:** allow profile fields can be edited ([#5872](https://github.com/resola-ai/deca-apps/issues/5872)) ([ea92a0e](https://github.com/resola-ai/deca-apps/commit/ea92a0e1530276e318e5d673ba7132b6e76e40d2))
* **crm:** enhance SingleSelectCell with permission checks and options management ([1263897](https://github.com/resola-ai/deca-apps/commit/12638975230e25ff0d385899eb03896ca12b85d6))
* **crm:** improve box resize behavior ([#6026](https://github.com/resola-ai/deca-apps/issues/6026)) ([354c946](https://github.com/resola-ai/deca-apps/commit/354c9464031095ab669177a94f61376f7ea3e7dd))
* **crm:** improve object height & scrollbar behavior, minor permission message update ([#6066](https://github.com/resola-ai/deca-apps/issues/6066)) ([b63787b](https://github.com/resola-ai/deca-apps/commit/b63787bb2fba00f5031c418b93eba1520792f00d))
* **crm:** show breadcrumb name properly ([#5996](https://github.com/resola-ai/deca-apps/issues/5996)) ([b8acfbc](https://github.com/resola-ai/deca-apps/commit/b8acfbc898ae16eb2a26a1ab381c0aa96ce70c94))
* **crm:** show NoAccess screens properly, improve filter dropdown behavior ([#6020](https://github.com/resola-ai/deca-apps/issues/6020)) ([de624a0](https://github.com/resola-ai/deca-apps/commit/de624a0a2b81f2120a81a3e8cc0d1006f0b43638))
* **crm:** use correct permission ([#5868](https://github.com/resola-ai/deca-apps/issues/5868)) ([bbd4ddb](https://github.com/resola-ai/deca-apps/commit/bbd4ddb5d110f109a420d42c551a9daf1e6564fd))


## [1.15.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.15.0) (2025-06-30)

### Features

* **crm:** Implement toggle for showing hidden fields with localStorage persistence ([#5901](https://github.com/resola-ai/deca-apps/issues/5901)) ([5f4084d](https://github.com/resola-ai/deca-apps/commit/5f4084d383f46a729780918e62e1c70827f8b4e1))
* **crm:** integrate breadcrumb navigation and enhance permission checks ([#5984](https://github.com/resola-ai/deca-apps/issues/5984)) ([ca564c5](https://github.com/resola-ai/deca-apps/commit/ca564c5ed70a5f930422f4cbe41aca63d0c6aec8))
* **crm:** tk-7470 improve sidebar drag ([#5955](https://github.com/resola-ai/deca-apps/issues/5955)) ([0e1d59e](https://github.com/resola-ai/deca-apps/commit/0e1d59ee0b7fd52967b592dea52c55befbc6db26))
* **crm:** TK-8033 - update currency field UI ([#5991](https://github.com/resola-ai/deca-apps/issues/5991)) ([b5b7f9c](https://github.com/resola-ai/deca-apps/commit/b5b7f9c490bee6438f1cfec91f2c6678773f6a62))
* **crm:** tk-8334 - add no access view ([#5883](https://github.com/resola-ai/deca-apps/issues/5883)) ([d66f55f](https://github.com/resola-ai/deca-apps/commit/d66f55f8570139b6d1e5ff5c14ed15f0fb7322a8))
* **crm:** TK-8334 - improve no view access list ([#5931](https://github.com/resola-ai/deca-apps/issues/5931)) ([0a1cf27](https://github.com/resola-ai/deca-apps/commit/0a1cf2764a543b362565de209e0f594277305723))
* **crm:** update Breadcrumbs component to allow more items ([#6033](https://github.com/resola-ai/deca-apps/issues/6033)) ([dae8cb3](https://github.com/resola-ai/deca-apps/commit/dae8cb30a49f34312c222b857acff4fc885dad12))
### Bug Fixes

* **crm:** allow profile fields can be edited ([#5872](https://github.com/resola-ai/deca-apps/issues/5872)) ([ea92a0e](https://github.com/resola-ai/deca-apps/commit/ea92a0e1530276e318e5d673ba7132b6e76e40d2))
* **crm:** improve box resize behavior ([#6026](https://github.com/resola-ai/deca-apps/issues/6026)) ([354c946](https://github.com/resola-ai/deca-apps/commit/354c9464031095ab669177a94f61376f7ea3e7dd))
* **crm:** improve object height & scrollbar behavior, minor permission message update ([#6066](https://github.com/resola-ai/deca-apps/issues/6066)) ([b63787b](https://github.com/resola-ai/deca-apps/commit/b63787bb2fba00f5031c418b93eba1520792f00d))
* **crm:** show breadcrumb name properly ([#5996](https://github.com/resola-ai/deca-apps/issues/5996)) ([b8acfbc](https://github.com/resola-ai/deca-apps/commit/b8acfbc898ae16eb2a26a1ab381c0aa96ce70c94))
* **crm:** show NoAccess screens properly, improve filter dropdown behavior ([#6020](https://github.com/resola-ai/deca-apps/issues/6020)) ([de624a0](https://github.com/resola-ai/deca-apps/commit/de624a0a2b81f2120a81a3e8cc0d1006f0b43638))
* **crm:** use correct permission ([#5868](https://github.com/resola-ai/deca-apps/issues/5868)) ([bbd4ddb](https://github.com/resola-ai/deca-apps/commit/bbd4ddb5d110f109a420d42c551a9daf1e6564fd))


### [1.14.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.14.1) (2025-06-16)


### Features

* **crm:** add permission checks and user feedback for view and object actions ([#5849](https://github.com/resola-ai/deca-apps/issues/5849)) ([bcd6664](https://github.com/resola-ai/deca-apps/commit/bcd6664cd9fccc8a0dac5afab47a72e8cd8339bf))
* **crm:** add records count functionality to workspace context ([#5819](https://github.com/resola-ai/deca-apps/issues/5819)) ([8a7f788](https://github.com/resola-ai/deca-apps/commit/8a7f7881f76684d074eacebf36c230beeaf99cc2))
* **crm:** enhance workspace and table components with record counter and date range field ([#5839](https://github.com/resola-ai/deca-apps/issues/5839)) ([16b9136](https://github.com/resola-ai/deca-apps/commit/16b913689334b83cf85f2fcf81ad061a2c1be16d))
* **crm:** implement permission checks for workspace and table components ([#5847](https://github.com/resola-ai/deca-apps/issues/5847)) ([e584b5a](https://github.com/resola-ai/deca-apps/commit/e584b5a831ea5f85595aab39c160dcae6df08154))

### Bug Fixes

* **crm:** hide header menu and add field if no permission ([#5852](https://github.com/resola-ai/deca-apps/issues/5852)) ([2e03b55](https://github.com/resola-ai/deca-apps/commit/2e03b55c6ae16ae4b763e8767267007d04c325e9))


## [1.14.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.14.0) (2025-06-11)


### Features

* **crm:** add userconfig support for view managements ([#5701](https://github.com/resola-ai/deca-apps/issues/5701)) ([e4daa35](https://github.com/resola-ai/deca-apps/commit/e4daa3534a121646b0c6a1d2a215b3effb26d942))
* **crm:** add userconfig support for view managements ([#5701](https://github.com/resola-ai/deca-apps/issues/5701)) ([ea7835c](https://github.com/resola-ai/deca-apps/commit/ea7835c44b0109b8143352beaeb5ff804f9fb018))
* **crm:** permission API view ([#5673](https://github.com/resola-ai/deca-apps/issues/5673)) ([b549f12](https://github.com/resola-ai/deca-apps/commit/b549f120fb548fa07aabeb3cb85a147d63d428d4))
* **crm:** permission API view ([#5673](https://github.com/resola-ai/deca-apps/issues/5673)) ([4ef2dac](https://github.com/resola-ai/deca-apps/commit/4ef2dac2392c793a66f989c7ce44b2368b81ce1e))
* **crm:** reorder in object settings and support daterange picker ([#5589](https://github.com/resola-ai/deca-apps/issues/5589)) ([7027dc4](https://github.com/resola-ai/deca-apps/commit/7027dc45a959701140e4f18572cd6a61869cac3e))
* **crm:** TK-2889 - add sorting control modal ([#5516](https://github.com/resola-ai/deca-apps/issues/5516)) ([68ce339](https://github.com/resola-ai/deca-apps/commit/68ce3396558f8e397c45650c106554a709f3986d))
* **crm:** TK-7149 - improve save settings ([#5748](https://github.com/resola-ai/deca-apps/issues/5748)) ([e3f2e64](https://github.com/resola-ai/deca-apps/commit/e3f2e64e25c9627442f076b89dc94d8b8afd66ae))
* **crm:** TK-7470 - TK-6442 - update warning modal ([#5735](https://github.com/resola-ai/deca-apps/issues/5735)) ([0a5134c](https://github.com/resola-ai/deca-apps/commit/0a5134cf7169146778ad485850d3eaf432e77569))
* **crm:** TK-7470 - TK-6442 - update warning modal ([#5735](https://github.com/resola-ai/deca-apps/issues/5735)) ([c64c6f3](https://github.com/resola-ai/deca-apps/commit/c64c6f3c7723648b10073aca8a335f33a3ad7f02))
* **crm:** TK-7470 - update sidebar resizing ([#5715](https://github.com/resola-ai/deca-apps/issues/5715)) ([fd369da](https://github.com/resola-ai/deca-apps/commit/fd369dab938e8981c7ef9520503fc147a19af3ae))
* **crm:** TK-7470 - update sidebar resizing ([#5715](https://github.com/resola-ai/deca-apps/issues/5715)) ([9500cdf](https://github.com/resola-ai/deca-apps/commit/9500cdfb611b0c3641d79c0db472773d30a85688))
* **crm:** TK-7471 - add order objects ([#5572](https://github.com/resola-ai/deca-apps/issues/5572)) ([703b519](https://github.com/resola-ai/deca-apps/commit/703b5195d94c47802f305356f20a16dd9046d5de))
* **crm:** TK-7476 - TK-7729 - TK-8033 - TK-8035 ([#5641](https://github.com/resola-ai/deca-apps/issues/5641)) ([192c919](https://github.com/resola-ai/deca-apps/commit/192c919628a3b33e56947d1a209421968fb893db))

### Bug Fixes

* **crm:** allow export csv ([d4b4e4a](https://github.com/resola-ai/deca-apps/commit/d4b4e4a102f90bdedffba93b16b8c7de842e1551))
* **crm:** check existing view ([21fe54f](https://github.com/resola-ai/deca-apps/commit/21fe54f3cc445ba413bab0934efdc38c71fbcc43))
* **crm:** check existing view ([da67fa5](https://github.com/resola-ai/deca-apps/commit/da67fa5b051efe891afe8d55b893c49357a8b31b))
* **crm:** enhance axios & notification services to support properly permission API ([55ffd9e](https://github.com/resola-ai/deca-apps/commit/55ffd9e6c3f302406db4d1d4e602d0dae4ff0491))
* **crm:** enhance axios & notification services to support properly permission API ([670fca3](https://github.com/resola-ai/deca-apps/commit/670fca3140c9b83c66e04f4a13fb11080eb07862))
* **crm:** Enhance receiver auto-selection logic to prioritize primary email and open a new tab when user clicks on ‘edit object’ button ([099f0d8](https://github.com/resola-ai/deca-apps/commit/099f0d8dcd64cf4d3af20f6ab87cab8a19263eeb))
* **crm:** Fix menu issue  ([#5592](https://github.com/resola-ai/deca-apps/issues/5592)) ([75d21b1](https://github.com/resola-ai/deca-apps/commit/75d21b122adcbf21d50ea4731493eaa15b762b85))
* **crm:** fix relationship record updates and duplicate view name ([#5612](https://github.com/resola-ai/deca-apps/issues/5612)) ([d4f58d7](https://github.com/resola-ai/deca-apps/commit/d4f58d78954cb6399b6b7ff2217994277142a698))
* **crm:** handle checkbox form properly ([#5697](https://github.com/resola-ai/deca-apps/issues/5697)) ([c16e7f8](https://github.com/resola-ai/deca-apps/commit/c16e7f8fcf0773e1b3cee20a43b7c087a43fe967))
* **crm:** handle checkbox form properly ([#5697](https://github.com/resola-ai/deca-apps/issues/5697)) ([249a001](https://github.com/resola-ai/deca-apps/commit/249a00132627e396464666d460555d129709e738))
* **crm:** handle to load views properly ([#5691](https://github.com/resola-ai/deca-apps/issues/5691)) ([5cc3824](https://github.com/resola-ai/deca-apps/commit/5cc3824d5c2fd40707edeb44d9c623c21d1b5b78))
* **crm:** handle to load views properly ([#5691](https://github.com/resola-ai/deca-apps/issues/5691)) ([c48a2b3](https://github.com/resola-ai/deca-apps/commit/c48a2b379dc57db56d91c342196ab721c228da97))
* **crm:** handle viewGroups properly base on permission ([#5716](https://github.com/resola-ai/deca-apps/issues/5716)) ([2c1ae02](https://github.com/resola-ai/deca-apps/commit/2c1ae024ba2dc295d16a7163b18a72174c8d0276))
* **crm:** handle viewGroups properly base on permission ([#5716](https://github.com/resola-ai/deca-apps/issues/5716)) ([d88b0cf](https://github.com/resola-ai/deca-apps/commit/d88b0cf3c03bf1c653c211a2baac0c19a3af6fee))
* **crm:** hide date range, minor bug fixes ([a0bfb8c](https://github.com/resola-ai/deca-apps/commit/a0bfb8c568cbbb7782066b94fa4c2aa0b0ff0b41))
* **crm:** hide date range, minor bug fixes ([f37514d](https://github.com/resola-ai/deca-apps/commit/f37514da7485c22f5117d6608058d9f5cd21c6de))
* **crm:** hide date range, minor bug fixes ([#5683](https://github.com/resola-ai/deca-apps/issues/5683)) ([97a0c0d](https://github.com/resola-ai/deca-apps/commit/97a0c0d798bfeedcd06a80679a0600440726888c))
* **crm:** improve table navigation logic ([#5602](https://github.com/resola-ai/deca-apps/issues/5602)) ([b306547](https://github.com/resola-ai/deca-apps/commit/b30654726eddbe2b2823d6a57a5539c3a75ef442))
* **crm:** minor UI bugs ([#5802](https://github.com/resola-ai/deca-apps/issues/5802)) ([ef4cfb3](https://github.com/resola-ai/deca-apps/commit/ef4cfb3699bbcfebc09023753da71a93262fcae0))
* **crm:** mutate views when get updated ([7f040a1](https://github.com/resola-ai/deca-apps/commit/7f040a1096d0aa24bc6c0f774a28e0484da02250))
* **crm:** mutate views when get updated ([23fba98](https://github.com/resola-ai/deca-apps/commit/23fba98d1c197ef88eb7be65539068398fa8faa5))
* **crm:** re-render cells if configs updated ([#5728](https://github.com/resola-ai/deca-apps/issues/5728)) ([f30bb45](https://github.com/resola-ai/deca-apps/commit/f30bb45bef74eb48d416577103183aea13770435))
* **crm:** re-render cells if configs updated ([#5728](https://github.com/resola-ai/deca-apps/issues/5728)) ([abc58a2](https://github.com/resola-ai/deca-apps/commit/abc58a28d1aaa90fb3ac133f304c8190177b4645))
* **crm:** TK-6650 - reset error when selecting correct receiver ([#5668](https://github.com/resola-ai/deca-apps/issues/5668)) ([dc5cee1](https://github.com/resola-ai/deca-apps/commit/dc5cee162b6f75851551701eed5c413b5079b8c1))
* **crm:** TK-6650 - reset error when selecting correct receiver ([#5668](https://github.com/resola-ai/deca-apps/issues/5668)) ([1be21f2](https://github.com/resola-ai/deca-apps/commit/1be21f24b779de8654062e983999b1351bd300a9))
* **crm:** TK-7849 - TK-8127 - fix cell not removed and body template cannot undo ([#5721](https://github.com/resola-ai/deca-apps/issues/5721)) ([3219fec](https://github.com/resola-ai/deca-apps/commit/3219fec9aa5895a4ab086e5eb10d28b0136c7941))
* **crm:** TK-7849 - TK-8127 - fix cell not removed and body template cannot undo ([#5721](https://github.com/resola-ai/deca-apps/issues/5721)) ([b44c53d](https://github.com/resola-ai/deca-apps/commit/b44c53d51d8dcf9fedcb2f6c30f16dfc3e646aa2))
* **crm:** TK-8060 - apply shadow and click out side popover ([#5705](https://github.com/resola-ai/deca-apps/issues/5705)) ([0d98381](https://github.com/resola-ai/deca-apps/commit/0d98381ff343c46708b40ccbc820bd7dda05eff8))
* **crm:** TK-8060 - apply shadow and click out side popover ([#5705](https://github.com/resola-ai/deca-apps/issues/5705)) ([a87df94](https://github.com/resola-ai/deca-apps/commit/a87df94c251ec229af5e1cc74ccaf2f89cd6762f))
* **crm:** TK-8100 - fix search disabled when view is locked ([#5666](https://github.com/resola-ai/deca-apps/issues/5666)) ([d9127ce](https://github.com/resola-ai/deca-apps/commit/d9127ce38c9c89ba31a9fdd3c9855299395728f8))
* **crm:** TK-8100 - fix search disabled when view is locked ([#5666](https://github.com/resola-ai/deca-apps/issues/5666)) ([6857f6a](https://github.com/resola-ai/deca-apps/commit/6857f6adf02bb03279768d1a00950d984bfaf528))
* **crm:** TK-8160 - Fix cannot navigation using keyboard ([#5698](https://github.com/resola-ai/deca-apps/issues/5698)) ([634506c](https://github.com/resola-ai/deca-apps/commit/634506ced2bd8a49171d58732f206bad740a3325))
* **crm:** TK-8160 - Fix cannot navigation using keyboard ([#5698](https://github.com/resola-ai/deca-apps/issues/5698)) ([732276b](https://github.com/resola-ai/deca-apps/commit/732276b426fdf216d458195c2df9007acff860b9))
* **crm:** update userconfig property name and enhance view group handling ([#5708](https://github.com/resola-ai/deca-apps/issues/5708)) ([71b0976](https://github.com/resola-ai/deca-apps/commit/71b09764952a5837c886f9120f3f8775c8d4c619))
* **crm:** update userconfig property name and enhance view group handling ([#5708](https://github.com/resola-ai/deca-apps/issues/5708)) ([81e573f](https://github.com/resola-ai/deca-apps/commit/81e573fb3cd32f39114517566ceb1072514642d7))


### [1.13.5](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.13.5) (2025-06-09)


### Features

* **crm:** add userconfig support for view managements ([bf3f754](https://github.com/resola-ai/deca-apps/commit/bf3f75441d983800377d149f832cf4efc909f96c))


### Bug Fixes

* **crm:** handle to load views properly ([7a4560c](https://github.com/resola-ai/deca-apps/commit/7a4560c7f605de4b6315c8daf2f2cb888d23021c))
* **crm:** handle viewGroups properly base on permission ([211296e](https://github.com/resola-ai/deca-apps/commit/211296eba4b3c8dbb30bd1e1a69c0790e11302fc))
* **crm:** handle viewGroups properly base on permission ([b8477d9](https://github.com/resola-ai/deca-apps/commit/b8477d9f05c890127156b68442c74fcd6bd1b8e8))
* **crm:** mutate views when get updated ([7b25ec5](https://github.com/resola-ai/deca-apps/commit/7b25ec54e82eeeafbc98b4372819acfdc1e447ac))
* **crm:** update userconfig property name and enhance view group handling ([af8c696](https://github.com/resola-ai/deca-apps/commit/af8c696b26b23635da136b89d2ef61436775853d))

### [1.13.4](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.13.4) (2025-06-04)


### Features

* **crm:** permission API view ([#5673](https://github.com/resola-ai/deca-apps/issues/5673)) ([a4ad334](https://github.com/resola-ai/deca-apps/commit/a4ad3347fc86d8e86f305521dc60cdd4abdd8067))


### Bug Fixes

* **crm:** check existing view ([87b4933](https://github.com/resola-ai/deca-apps/commit/87b49336dcfc7c5cebec7cb945d861881564ff35))
* **crm:** enhance axios & notification services to support properly permission API ([5f35417](https://github.com/resola-ai/deca-apps/commit/5f354178d911efad33865fe6725609c406d9902a))
* **crm:** hide date range, minor bug fixes ([#5683](https://github.com/resola-ai/deca-apps/issues/5683)) ([ddc30b2](https://github.com/resola-ai/deca-apps/commit/ddc30b2c712e3681bbf5269454ce63c52f858d43))

### [1.13.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.13.3) (2025-06-02)


### Bug Fixes

* **crm:** Enhance receiver auto-selection logic to prioritize primary email and open a new tab when user clicks on ‘edit object’ button ([9f9c1b3](https://github.com/resola-ai/deca-apps/commit/9f9c1b3d87942021e08d66aeea622db4b3c870d6))

### [1.13.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.13.2) (2025-05-29)

### Bug Fixes

* **crm:** Jump to new tab when opening a linked record


### [1.13.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.13.1) (2025-05-26)


### Features

* **crm:** Enhance cell update handling and tooltip functionality in workspace components ([#5486](https://github.com/resola-ai/deca-apps/issues/5486)) ([d444a82](https://github.com/resola-ai/deca-apps/commit/d444a8233a17bbc3032b306d3db9d3a6b7728162))
* **crm:** Enhance mention tooltip functionality and user View configuration handling ([#5456](https://github.com/resola-ai/deca-apps/issues/5456)) ([f51ad2d](https://github.com/resola-ai/deca-apps/commit/f51ad2d17d1c2347e7da53ca4efa9d119128ce8b))
* **crm:** TK-7007- Enhance components profile UI ([#5493](https://github.com/resola-ai/deca-apps/issues/5493)) ([5296b88](https://github.com/resola-ai/deca-apps/commit/5296b88d7fa91f7a06597e028b563ba7476c10b5))
* **crm:** TK-7470-TK-7472 - Update sidebar objects ([#5457](https://github.com/resola-ai/deca-apps/issues/5457)) ([336c8b3](https://github.com/resola-ai/deca-apps/commit/336c8b3a1611b6a15133c8868cd41e4bd21b4810))
* **crm:** TK-7722 Add border for receiver ([#5503](https://github.com/resola-ai/deca-apps/issues/5503)) ([37649cc](https://github.com/resola-ai/deca-apps/commit/37649cca203c27726b71e0f2880a30d7af2054d3))
* **crm:** TK-7722- fix missing border modal ([#5499](https://github.com/resola-ai/deca-apps/issues/5499)) ([c19bd6e](https://github.com/resola-ai/deca-apps/commit/c19bd6ec4d44177d060ee0fdc617cb9594798b06))
* **crm:** TK-7722-TK-7146- update deca modal ([#5425](https://github.com/resola-ai/deca-apps/issues/5425)) ([fc869a4](https://github.com/resola-ai/deca-apps/commit/fc869a4f9a946f47267727c7148947e1069a2851))

### Bug Fixes

* **crm:** Enhance workspace context and modal management functionality ([#5501](https://github.com/resola-ai/deca-apps/issues/5501)) ([fc6b8d4](https://github.com/resola-ai/deca-apps/commit/fc6b8d4cc8ff97580e3d75f20307f31bd9f57173))
* **crm:** update controlledFields correctly ([04305af](https://github.com/resola-ai/deca-apps/commit/04305af8f81c0880d3b73d01e004db9d90fff7b0))


## [1.13.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.13.0) (2025-05-20)


### Features

* **crm:** Enhance template handling and mention suggestions ([fd7b997](https://github.com/resola-ai/deca-apps/commit/fd7b9978b3b3edc1925d1c02225ed7aeb5c08167))
* **crm:** enhance view management by integrating view settings into object API calls ([#5284](https://github.com/resola-ai/deca-apps/issues/5284)) ([9580a6b](https://github.com/resola-ai/deca-apps/commit/9580a6b2737efae062e34dd787b7dbf2573a9fa2))
* **crm:** Implement lazy loading for tab components and enhance tab management in ProfileSettings ([d50d1c0](https://github.com/resola-ai/deca-apps/commit/d50d1c061e7a3b7928f10dd1b3d72c7dea0a1ab9))
* **crm:** integrate emoji picker and update view management ([#5296](https://github.com/resola-ai/deca-apps/issues/5296)) ([78f056d](https://github.com/resola-ai/deca-apps/commit/78f056d51cd44ee2f5a1f9582a9445e06443896a))
* **crm:** TK-6930 - Update merge record UI ([#5391](https://github.com/resola-ai/deca-apps/issues/5391)) ([71ccbf9](https://github.com/resola-ai/deca-apps/commit/71ccbf92b7d36d94edf7880c03ffa5cc6c425c52))
* **crm:** TK-6962 Add notification successfully deleted ([#5375](https://github.com/resola-ai/deca-apps/issues/5375)) ([ebd57a1](https://github.com/resola-ai/deca-apps/commit/ebd57a158e02d822be40147eba9a1ed56a136a5a))
* **crm:** TK-6962 Improve file category  ([#5367](https://github.com/resola-ai/deca-apps/issues/5367)) ([6f91342](https://github.com/resola-ai/deca-apps/commit/6f91342b0e9a30747db16f82b058f0cb610e356e))
* **crm:** TK-6967 - Update create activities UI ([#5275](https://github.com/resola-ai/deca-apps/issues/5275)) ([85bf4f7](https://github.com/resola-ai/deca-apps/commit/85bf4f7f77cc0fb00f6a3d624be93380e3d56c20))
* **crm:** TK-6967 - Update feedback on activity UI ([#5305](https://github.com/resola-ai/deca-apps/issues/5305)) ([ea4bbad](https://github.com/resola-ai/deca-apps/commit/ea4bbad0ab7cba7054c8f84c7be74fab6c56c5d5))
* **crm:** TK-6973 - Update modal center ([#5304](https://github.com/resola-ai/deca-apps/issues/5304)) ([28719a8](https://github.com/resola-ai/deca-apps/commit/28719a830571755b34246d8bbf47b232099797b2))

### Bug Fixes

* **crm:** enhance NavbarContext and improve drag-and-drop behavior in ManageViewModal ([#5373](https://github.com/resola-ai/deca-apps/issues/5373)) ([fbd84c8](https://github.com/resola-ai/deca-apps/commit/fbd84c8711e9158be7370a442b02a4ad401233d9))
* **crm:** optimizing column order and sizing handling ([3d439a9](https://github.com/resola-ai/deca-apps/commit/3d439a9faf375757fc696efdf3cb873e7043c71b))
* **crm:** show all object fields in email template ([#5400](https://github.com/resola-ai/deca-apps/issues/5400)) ([d637e86](https://github.com/resola-ai/deca-apps/commit/d637e861453ee270f4f2d41fd8143071fd7783c4))
* **crm:** update lock file ([b5fb6fc](https://github.com/resola-ai/deca-apps/commit/b5fb6fccc5b1f1c2b546d7de5145100e71ba41f2))
* **crm:** use latest node and apply circular-dependency ([#5318](https://github.com/resola-ai/deca-apps/issues/5318)) ([8e74b3b](https://github.com/resola-ai/deca-apps/commit/8e74b3b4270d86f795b99fbc1aa94dd1f895250a))
* **crm:** validate email subject, minor bug fixes ([326f38c](https://github.com/resola-ai/deca-apps/commit/326f38c287322c26868bb77e8e96e76109fd19e1))
* **crm:** validate email subject, minor bug fixes ([#5328](https://github.com/resola-ai/deca-apps/issues/5328)) ([15a18d2](https://github.com/resola-ai/deca-apps/commit/15a18d2ad261fafebef5e4bfc95dfc855309d6f9))


### [1.12.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.12.1) (2025-05-12)


### Features

* **crm:** add more unit test ([#5249](https://github.com/resola-ai/deca-apps/issues/5249)) ([ce56e3c](https://github.com/resola-ai/deca-apps/commit/ce56e3c6b296466dfcefc1c9959ff3049b747922))
* **crm:** add tests for crm project ([#4817](https://github.com/resola-ai/deca-apps/issues/4817)) ([c759b90](https://github.com/resola-ai/deca-apps/commit/c759b9091d376a486f18e1b0ec6d0207540569a2))
* **crm:** Allow user to use variables for emails on template ([#5244](https://github.com/resola-ai/deca-apps/issues/5244)) ([a84e1f2](https://github.com/resola-ai/deca-apps/commit/a84e1f24bfb8ff6bb1cdf59dc3c49c5674006487))
* **crm:** Fix order within group not work ([#5256](https://github.com/resola-ai/deca-apps/issues/5256)) ([22e26d2](https://github.com/resola-ai/deca-apps/commit/22e26d2fef50f2ebcf336a93de2285479c7eadd1))
* **crm:** improve drag drop ([#5183](https://github.com/resola-ai/deca-apps/issues/5183)) ([36cf9a4](https://github.com/resola-ai/deca-apps/commit/36cf9a41fe5f90f7bee3831316c0d64609e34fa7))
* **crm:** TK-6592 - disabled rename, delete view when it is locked ([#5191](https://github.com/resola-ai/deca-apps/issues/5191)) ([fda9ef9](https://github.com/resola-ai/deca-apps/commit/fda9ef905bbb8b005092b6fea6e48115e3564566))
* **crm:** TK-6800 -  improve style icon and modal ([#5165](https://github.com/resola-ai/deca-apps/issues/5165)) ([555bda0](https://github.com/resola-ai/deca-apps/commit/555bda018903aa53e6b51c9a8d6cc7dfb248ce85))
* **crm:** TK-6800 update tableHeight close and button style ([#5225](https://github.com/resola-ai/deca-apps/issues/5225)) ([be6f83c](https://github.com/resola-ai/deca-apps/commit/be6f83cd3b939ca02987d323b568829ecdac2b51))
* **crm:** update context, file structure, minor bug fixes ([#5178](https://github.com/resola-ai/deca-apps/issues/5178)) ([45b4bab](https://github.com/resola-ai/deca-apps/commit/45b4bab2edcb6cdc7134d41272d7e6158d56c76c))
* **crm:** update viewGroups wrong orderViews ([#5251](https://github.com/resola-ai/deca-apps/issues/5251)) ([c2237a6](https://github.com/resola-ai/deca-apps/commit/c2237a6f8fafc9ff9ae9030e48e2088374894e40))


### Bug Fixes

* **crm:** improve ws and object API call, minor bug fixes ([#5260](https://github.com/resola-ai/deca-apps/issues/5260)) ([80f07f3](https://github.com/resola-ai/deca-apps/commit/80f07f372f317b98d85d730d4695e89fa76cbcd0))
* **crm:** use correct field from API ([#5238](https://github.com/resola-ai/deca-apps/issues/5238)) ([fcf7970](https://github.com/resola-ai/deca-apps/commit/fcf79709e0caf85fd5d2c6780e392e718e4edc2f))


## [1.12.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.12.0) (2025-04-25)


### Features

* **crm:** enhance Workspace component with row height adjustments and field group management ([e1a3191](https://github.com/resola-ai/deca-apps/commit/e1a3191f3a73df46ea728fe1ff25edf3d79d0ddc))
* **crm:** integrate mention functionality in editor ([#5081](https://github.com/resola-ai/deca-apps/issues/5081)) ([c2a2b6d](https://github.com/resola-ai/deca-apps/commit/c2a2b6d91d2d943848c44beb4f6ae12182d205c2))
* **crm:** TK-6437 - Add search view ([#5120](https://github.com/resola-ai/deca-apps/issues/5120)) ([36326cf](https://github.com/resola-ai/deca-apps/commit/36326cf1912695cf26c64cf86d8c954009d220f2))
* **crm:** update Querybuilder to retain 1 item in group, improve Workspace object and view handling ([d146c19](https://github.com/resola-ai/deca-apps/commit/d146c19bd3eea8a25dd84584fcbb2d59f3966266))
* **crm:** update Querybuilder to retain 1 item in group, improve Workspace object and view handling ([#5107](https://github.com/resola-ai/deca-apps/issues/5107)) ([987f839](https://github.com/resola-ai/deca-apps/commit/987f839201f1f5f78d44cf601ccf7cf785488658))

### Bug Fixes

* **crm:** fix build failed ([98b920a](https://github.com/resola-ai/deca-apps/commit/98b920a3cc6044c648f05c626e20103bfdd8619a))
* **crm:** update file item interaction and enhance attachment hook setting ([ce59869](https://github.com/resola-ai/deca-apps/commit/ce59869124d7c7d833db30deba91a017abf69506))


### [1.11.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.11.2) (2025-04-17)


### Features

* **crm:** add file preview modals for PDF, implement attachment fetching hook ([#4982](https://github.com/resola-ai/deca-apps/issues/4982)) ([7a5a53f](https://github.com/resola-ai/deca-apps/commit/7a5a53fbb6a43fffc44cf815d954d51c2574e1ab))
* **crm:** update assetType ([#4945](https://github.com/resola-ai/deca-apps/issues/4945)) ([098c257](https://github.com/resola-ai/deca-apps/commit/098c257d9603863e8984b40ce7deced961711c07))

### Bug Fixes

* **crm:** Unable to change order of fields in the CRM Profile record details screen ([966ee99](https://github.com/resola-ai/deca-apps/commit/966ee99cb4c03986d1e84c52c6a5eca05b1e3b5d))


### [1.11.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.11.1) (2025-04-11)


### Features

* **crm:** Add live chat functionality and enhance notification rendering ([#4921](https://github.com/resola-ai/deca-apps/issues/4921)) ([935b154](https://github.com/resola-ai/deca-apps/commit/935b1547258152d0cc91e7aa6025005cf22a6547))
* **crm:** TK-5881 Implement CustomDetailView and refactor FieldRender for improved field management ([#4903](https://github.com/resola-ai/deca-apps/issues/4903)) ([29598aa](https://github.com/resola-ai/deca-apps/commit/29598aa2e21e02edcd0d835b26136673255fc0a3))
* **crm:** Update API attachment, improve stable, minor UI fixes ([#4933](https://github.com/resola-ai/deca-apps/issues/4933)) ([28dbe3b](https://github.com/resola-ai/deca-apps/commit/28dbe3b21fe1284ce9cf6a7f39dea9dfb0d7aa59))

### Bug Fixes

* **crm:** improve groups order performance, UI validation ([#4925](https://github.com/resola-ai/deca-apps/issues/4925)) ([8f0fd24](https://github.com/resola-ai/deca-apps/commit/8f0fd24e39deb9dbb3a7c3b985700924044a8c7f))


## [1.11.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.11.0) (2025-04-08)


### Features

* **crm:** enhance file management with category support ([#4768](https://github.com/resola-ai/deca-apps/issues/4768)) ([0432669](https://github.com/resola-ai/deca-apps/commit/04326690379db4c0c5b72dc5833daa436b71d13a))
* **crm:** restructure hooks, setup vitest and write functional testing ([#4531](https://github.com/resola-ai/deca-apps/issues/4531)) ([cdd7e16](https://github.com/resola-ai/deca-apps/commit/cdd7e16965af48080375eb27e9e535d1463e9c5c))
* **crm:** TK-4018 - Add UI for custom action ([#4623](https://github.com/resola-ai/deca-apps/issues/4623)) ([e34dfec](https://github.com/resola-ai/deca-apps/commit/e34dfec4a9e6b86d7160e01090f8bd6e06563db8))
* **crm:** TK-4267 Add support for Line messaging and integrate emoji picker ([#4699](https://github.com/resola-ai/deca-apps/issues/4699)) ([4fe479f](https://github.com/resola-ai/deca-apps/commit/4fe479f3b6338af3761a6d52aad372aed6de4d43))
* **crm:** TK-5876 Enable attachments to CRM emails ([#4720](https://github.com/resola-ai/deca-apps/issues/4720)) ([4e4355a](https://github.com/resola-ai/deca-apps/commit/4e4355a20a709a7c76edfc54e9bbb9d0c6ee161e))
* **crm:** TK-5876 support sending email with attachment, support line activity ([#4747](https://github.com/resola-ai/deca-apps/issues/4747)) ([54aea05](https://github.com/resola-ai/deca-apps/commit/54aea05410786ec2d6f100176eed57b4591ef6d7))
* **crm:** TK-6171 Render directly value of the relationship field on reading mode ([31a2732](https://github.com/resola-ai/deca-apps/commit/31a273226cef1c3569c27780b391ee1cf6f907f5))
* **crm:** TK-6171 render more detail on history tab ([4f28d7b](https://github.com/resola-ai/deca-apps/commit/4f28d7bfd6476867481a29beb319d64b6c036f82))


### Bug Fixes

* **crm:** fix bugs on relationship fields, profile detail and some minor improvements ([#4805](https://github.com/resola-ai/deca-apps/issues/4805)) ([8deb486](https://github.com/resola-ai/deca-apps/commit/8deb4861dce5a131644288b99c521f2440ac0105))
* **crm:** minor bug fixes on File and Profile detail ([#4822](https://github.com/resola-ai/deca-apps/issues/4822)) ([b227553](https://github.com/resola-ai/deca-apps/commit/b2275538eed642c32f2f4f643d2a5da7fb350a07))
* **crm:** TK-6341 TK-6217 TK-2618 improve CRM state management ([#4761](https://github.com/resola-ai/deca-apps/issues/4761)) ([728882d](https://github.com/resola-ai/deca-apps/commit/728882d98bcf609971980c1182f690062500fa6e))
* **crm:** TK-6351 - Fix record after changing relationship field ([#4775](https://github.com/resola-ai/deca-apps/issues/4775)) ([ecbc5fc](https://github.com/resola-ai/deca-apps/commit/ecbc5fca04e2d7df42f46644751f793a37dfa831))
* **crm:** TK-6369 - Fix navigation not work ([#4737](https://github.com/resola-ai/deca-apps/issues/4737)) ([c9b3bdb](https://github.com/resola-ai/deca-apps/commit/c9b3bdbc23034e2a239a0fa14496b46084e65f17))
* **crm:** TK-6369 - Fix unexpected navigation when clicking back ([#4808](https://github.com/resola-ai/deca-apps/issues/4808)) ([8689a17](https://github.com/resola-ai/deca-apps/commit/8689a17b5ba25adb26fe5005b0274ad83cd9c4c0))
* **crm:** update translation text ([eac666f](https://github.com/resola-ai/deca-apps/commit/eac666f64db53ccaba112b7ac4f1ca8424b2bb7b))


## [1.10.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.10.0) (2025-03-17)


### Features

* **crm:** Improve color menu is hidden ([#4522](https://github.com/resola-ai/deca-apps/issues/4522)) ([8fcb0a4](https://github.com/resola-ai/deca-apps/commit/8fcb0a40df8215a2fbafa30933e5aef5b6e0515f))

### Bug Fixes

* **crm:** update sorting value ([d839005](https://github.com/resola-ai/deca-apps/commit/d83900556f47b391c23b6978aaf234673a24a2c3))


### [1.9.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.9.1) (2025-03-04)


### Features

* **crm:** adjust fetch records setting ([20351a2](https://github.com/resola-ai/deca-apps/commit/20351a245dc2835e42af3e635550a99d93d0ba68))
* **crm:** CRM - TK-4335 - Update text sort function ([#3917](https://github.com/resola-ai/deca-apps/issues/3917)) ([2a86312](https://github.com/resola-ai/deca-apps/commit/2a86312857d08960f9628eb95131885bcf502d2d))
* **crm:** CRM - TK-5079 - update default value select type ([#4192](https://github.com/resola-ai/deca-apps/issues/4192)) ([dc8da80](https://github.com/resola-ai/deca-apps/commit/dc8da8092e622ef0c6c7915793ce69f08e797aa2))
* **crm:** CRM - update wrong name object ([#3912](https://github.com/resola-ai/deca-apps/issues/3912)) ([736d672](https://github.com/resola-ai/deca-apps/commit/736d672e749354d10fa981d19e777bb7fb381089))
* **crm:** improve context and component loading ([5ee891e](https://github.com/resola-ai/deca-apps/commit/5ee891ea61af8542ff10cc7857d73156fecc3e20))
* **crm:** improve context and component loading ([80b5737](https://github.com/resola-ai/deca-apps/commit/80b5737bb0e6b1740eeb7ce24e1d9ecac37ceb7f))
* **crm:** TK-3395 - Add export csv to view ([#4187](https://github.com/resola-ai/deca-apps/issues/4187)) ([137dcc2](https://github.com/resola-ai/deca-apps/commit/137dcc2bbd23c01729889d4b836bffc8048d5d9a))
* **crm:** TK-4334 - Add protected field ([#4035](https://github.com/resola-ai/deca-apps/issues/4035)) ([3b551ef](https://github.com/resola-ai/deca-apps/commit/3b551efe9bf4f1dc54f4b09f9f81c7638a710f4d))
* **crm:** TK-4344 Show content of sent mails in CRM' ([11ec179](https://github.com/resola-ai/deca-apps/commit/11ec179e8289a405cf0453c9ec00ecdbb1fbb037))
* **crm:** TK-4351 Enhance table filter with relative date functionality ([#4282](https://github.com/resola-ai/deca-apps/issues/4282)) ([6bc97a6](https://github.com/resola-ai/deca-apps/commit/6bc97a6f7132d8884d72c7799d0c114c0532f760))
* **crm:** TK-4351 relative date with anchorField ([#4303](https://github.com/resola-ai/deca-apps/issues/4303)) ([786fe0a](https://github.com/resola-ai/deca-apps/commit/786fe0a7726e605b92f86519d296bb07650d7df5))
* **crm:** TK-4519 - Update order custom object fields ([#3984](https://github.com/resola-ai/deca-apps/issues/3984)) ([0ef3957](https://github.com/resola-ai/deca-apps/commit/0ef395780388ecf97c4f59b79b89e8ca90a23c67))
* **crm:** TK-4926 - Access Views via url ([#4341](https://github.com/resola-ai/deca-apps/issues/4341)) ([13ea412](https://github.com/resola-ai/deca-apps/commit/13ea412e9e01e8deeb5baf0832cb8cee5f9cedd7))
* **crm:** TK-5075 add pagination, move some components out ws context ([#4365](https://github.com/resola-ai/deca-apps/issues/4365)) ([6a05e6c](https://github.com/resola-ai/deca-apps/commit/6a05e6cef16309fb93c363905f744385bcb5ef68))
* **crm:** TK-5088 Improve CRM performance with reference object ([10c3416](https://github.com/resola-ai/deca-apps/commit/10c34164ad5bd9fd53f98dda581ed25fe02ccf6d))
* **crm:** TK-5165 - restructure and clean up ([#4381](https://github.com/resola-ai/deca-apps/issues/4381)) ([59356de](https://github.com/resola-ai/deca-apps/commit/59356de6c499a94518936e5ed6aa0a4d5a2816d2))
* **crm:** TK-5570 - Load detail profile ỉmprovement ([#4400](https://github.com/resola-ai/deca-apps/issues/4400)) ([679d902](https://github.com/resola-ai/deca-apps/commit/679d9025c57060eb67f2907512669c3be7638996))
* **crm:** UI issue related to mantine v7 migration ([#3997](https://github.com/resola-ai/deca-apps/issues/3997)) ([21adff5](https://github.com/resola-ai/deca-apps/commit/21adff5664c3467e811066b292052eecb8c4a920))
* **crm:** use pageSize for manually trigger page size ([236be6b](https://github.com/resola-ai/deca-apps/commit/236be6b9009dc4069e21524ab7cfd22a8f8ea63a))

### Bug Fixes

* **crm:** check empty receiver before sending ([46ddc47](https://github.com/resola-ai/deca-apps/commit/46ddc47fc9309b1aa3793d7f92240cdf38b13ab4))
* **crm:** correct record loading state ([9b75b8f](https://github.com/resola-ai/deca-apps/commit/9b75b8f44616ff9a19a9f68cf7aadc3e7b0213c2))
* **crm:** Fix wrong id view when hiding field ([#4052](https://github.com/resola-ai/deca-apps/issues/4052)) ([dd41de5](https://github.com/resola-ai/deca-apps/commit/dd41de550e8ec44d8ccbf05729ef04248d80863b))
* **crm:** make cell render correctly on virtual column ([3499fed](https://github.com/resola-ai/deca-apps/commit/3499fed2cad27b3509117bbd97cd639c53b84186))
* **crm:** make relative dates work correctly with timezone ([26d31e1](https://github.com/resola-ai/deca-apps/commit/26d31e14bc71ee9e911bed1a2681edb545f09aa3))
* **crm:** TK - 4459 - Fix edit trigger wrong fields ([#3929](https://github.com/resola-ai/deca-apps/issues/3929)) ([2f1e510](https://github.com/resola-ai/deca-apps/commit/2f1e510d75bc8a914457787f252e7f237a43992b))
* **crm:** TK-4370 reduce html template requests ([95e8a6c](https://github.com/resola-ai/deca-apps/commit/95e8a6ca115cbc03b4b4dba6883fd8ce1a951c61))
* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([753053f](https://github.com/resola-ai/deca-apps/commit/753053fdd4e9d7b8ed5382b479c875698b0a1dec))
* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([2213d69](https://github.com/resola-ai/deca-apps/commit/2213d69d456c1096d21e31744a302aa9f616c8b0))
* **crm:** tk-4552 Return correct status for Email/SMS when sending failed ([06f6c81](https://github.com/resola-ai/deca-apps/commit/06f6c81c9128462fd3e7cda0d9ebf3ce0b834342))
* **crm:** TK-4577, TK-4582 fix highlight and missing icon ([#4006](https://github.com/resola-ai/deca-apps/issues/4006)) ([22b021f](https://github.com/resola-ai/deca-apps/commit/22b021f20f16cd591650c350f400ddff1ff957e4))
* **crm:** TK-4765 - update non manager do not allow to edit protected field ([#4072](https://github.com/resola-ai/deca-apps/issues/4072)) ([95061f5](https://github.com/resola-ai/deca-apps/commit/95061f58268e71c7a507745bedb50aaf86b935ae))
* **crm:** TK-5075 mutate records locally ([#4388](https://github.com/resola-ai/deca-apps/issues/4388)) ([567bce7](https://github.com/resola-ai/deca-apps/commit/567bce74823562ff267aaade3c6ec8b812bc451a))
* **crm:** TK-5075 update searchbox to work properly ([#4397](https://github.com/resola-ai/deca-apps/issues/4397)) ([c4ebebc](https://github.com/resola-ai/deca-apps/commit/c4ebebc081e79da7523664ebb3f1d98eea8eaaba))
* **crm:** TK-5169 TK-5171 cannot move the column ([#4233](https://github.com/resola-ai/deca-apps/issues/4233)) ([cf5b016](https://github.com/resola-ai/deca-apps/commit/cf5b016392edd7cae10b314cd0dd822a28be347d))
* **crm:** TK-5178 revalidate when  update view ([0621709](https://github.com/resola-ai/deca-apps/commit/0621709d5760d4b70461d2b0392d051826e4c5a8))
* **crm:** tk-5202 ([#4265](https://github.com/resola-ai/deca-apps/issues/4265)) ([b6867c0](https://github.com/resola-ai/deca-apps/commit/b6867c09d73ac7210dbf9b049c14361e1bebcd20))
* **crm:** update email template and editor with mantine v7 ([fa60514](https://github.com/resola-ai/deca-apps/commit/fa605142192b376221e1dab07163af443a479fad))
* **crm:** update type ([cc1d4ab](https://github.com/resola-ai/deca-apps/commit/cc1d4ab81a1810f7d57b398dac824c45086d1baa))


## [1.9.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.9.0) (2025-02-24)

### Bug Fixes

* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([753053f](https://github.com/resola-ai/deca-apps/commit/753053fdd4e9d7b8ed5382b479c875698b0a1dec))
* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/issues/4243)) ([2213d69](https://github.com/resola-ai/deca-apps/commit/2213d69d456c1096d21e31744a302aa9f616c8b0))
* **crm:** tk-5202 ([#4265](https://github.com/resola-ai/deca-apps/issues/4265)) ([b6867c0](https://github.com/resola-ai/deca-apps/commit/b6867c09d73ac7210dbf9b049c14361e1bebcd20))


### [1.0.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.0.1) (2025-02-18)


### Features

* **crm:** CRM - TK-5079 - update default value select type ([#4192](https://github.com/resola-ai/deca-apps/issues/4192)) ([dc8da80](https://github.com/resola-ai/deca-apps/commit/dc8da8092e622ef0c6c7915793ce69f08e797aa2))
* **crm:** improve context and component loading ([5ee891e](https://github.com/resola-ai/deca-apps/commit/5ee891ea61af8542ff10cc7857d73156fecc3e20))
* **crm:** improve context and component loading ([80b5737](https://github.com/resola-ai/deca-apps/commit/80b5737bb0e6b1740eeb7ce24e1d9ecac37ceb7f))
* **crm:** TK-5088 Improve CRM performance with reference object ([10c3416](https://github.com/resola-ai/deca-apps/commit/10c34164ad5bd9fd53f98dda581ed25fe02ccf6d))

### Bug Fixes

* **crm:** make cell render correctly on virtual column ([3499fed](https://github.com/resola-ai/deca-apps/commit/3499fed2cad27b3509117bbd97cd639c53b84186))
* **crm:** TK-4765 - update non manager do not allow to edit protected field ([#4072](https://github.com/resola-ai/deca-apps/issues/4072)) ([95061f5](https://github.com/resola-ai/deca-apps/commit/95061f58268e71c7a507745bedb50aaf86b935ae))
* **crm:** TK-5169 TK-5171 cannot move the column ([#4233](https://github.com/resola-ai/deca-apps/issues/4233)) ([cf5b016](https://github.com/resola-ai/deca-apps/commit/cf5b016392edd7cae10b314cd0dd822a28be347d))
* **crm:** TK-5178 revalidate when  update view ([0621709](https://github.com/resola-ai/deca-apps/commit/0621709d5760d4b70461d2b0392d051826e4c5a8))
* **crm:** TK-4459 - update mutate object when deleting colums ([#4243](https://github.com/resola-ai/deca-apps/pull/4243)) ([2213d69](https://github.com/resola-ai/deca-apps/commit/https://github.com/resola-ai/deca-apps/commit/2213d69d456c1096d21e31744a302aa9f616c8b0))


## [1.0.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@1.0.0) (2025-02-03)


### Features

* **crm:** CRM - TK-4335 - Update text sort function ([#3917](https://github.com/resola-ai/deca-apps/issues/3917)) ([2a86312](https://github.com/resola-ai/deca-apps/commit/2a86312857d08960f9628eb95131885bcf502d2d))
* **crm:** CRM - update wrong name object ([#3912](https://github.com/resola-ai/deca-apps/issues/3912)) ([736d672](https://github.com/resola-ai/deca-apps/commit/736d672e749354d10fa981d19e777bb7fb381089))
* **crm:** fix build falied ([3e31d15](https://github.com/resola-ai/deca-apps/commit/3e31d153de23b6f71852a3eeea7e77a8ab3f2210))
* **crm:** TK - 3612 - improve condition ([35f2ea3](https://github.com/resola-ai/deca-apps/commit/35f2ea3c93bf9eb75efe691039e608713858ee40))
* **crm:** TK-2264 - Update manual record activity ([9493954](https://github.com/resola-ai/deca-apps/commit/9493954a6fd015d13f5c9c3fda597e3bdb052010))
* **crm:** TK-2568 - Review error in Japanese ([#3845](https://github.com/resola-ai/deca-apps/issues/3845)) ([b025587](https://github.com/resola-ai/deca-apps/commit/b025587bdea315fbd89f01312599fdf0a49e21d9))
* **crm:** TK-3186 Display toast message and email status when sending and email ([2d314d5](https://github.com/resola-ai/deca-apps/commit/2d314d5b4f27f36d22d930acc3886641eecbe378))
* **crm:** TK-3948 - not allow edit field in profile detail when view is locked ([6c0ec10](https://github.com/resola-ai/deca-apps/commit/6c0ec10cfcc116961a9c4534404b1e968d201a37))
* **crm:** TK-3958 Implement new long text field with support pin to tabbar ([4b7b936](https://github.com/resola-ai/deca-apps/commit/4b7b9363d2cb2b2f36a9ed091b5cdc894c276df0))
* **crm:** TK-3979 - Add UI custom object fields ([5ef716f](https://github.com/resola-ai/deca-apps/commit/5ef716ffeb372f9b7d55987c815795ec97fb5149))
* **crm:** TK-3979 - Improve showing full fields ([#3876](https://github.com/resola-ai/deca-apps/issues/3876)) ([2911094](https://github.com/resola-ai/deca-apps/commit/29110948e315674bef88ebf242ace1767cc279cd))
* **crm:** TK-3979 - show full fields configurations ([#3857](https://github.com/resola-ai/deca-apps/issues/3857)) ([ff935a4](https://github.com/resola-ai/deca-apps/commit/ff935a418ac0d70ef0025438c59aae9e0ce1eb52))
* **crm:** TK-4153 TK-4150 make profile data work properly, minor UI updates ([d3359ca](https://github.com/resola-ai/deca-apps/commit/d3359ca2fc488114dd4fde719f61df68ea26db3a))
* **crm:** TK-4168 - Update template activities ([#3836](https://github.com/resola-ai/deca-apps/issues/3836)) ([1f1dde5](https://github.com/resola-ai/deca-apps/commit/1f1dde52d28c423e68fc11dba39b43a9a0e5c636))
* **crm:** TK-4238 remove white spaces onSave text data ([44345c1](https://github.com/resola-ai/deca-apps/commit/44345c1bf6cc4e1279aa13b9336aefb5ede43edc))
* **crm:** TK-4334 - Add protected field ([#4035](https://github.com/resola-ai/deca-apps/issues/4035)) ([3b551ef](https://github.com/resola-ai/deca-apps/commit/3b551efe9bf4f1dc54f4b09f9f81c7638a710f4d))
* **crm:** TK-4344 Show content of sent mails in CRM' ([11ec179](https://github.com/resola-ai/deca-apps/commit/11ec179e8289a405cf0453c9ec00ecdbb1fbb037))
* **crm:** TK-4519 - Update order custom object fields ([#3984](https://github.com/resola-ai/deca-apps/issues/3984)) ([0ef3957](https://github.com/resola-ai/deca-apps/commit/0ef395780388ecf97c4f59b79b89e8ca90a23c67))
* **crm:** UI issue related to mantine v7 migration ([#3997](https://github.com/resola-ai/deca-apps/issues/3997)) ([21adff5](https://github.com/resola-ai/deca-apps/commit/21adff5664c3467e811066b292052eecb8c4a920))
* **crm:** update default language tolgee ([b3a9591](https://github.com/resola-ai/deca-apps/commit/b3a9591aae7b89e71a524bf89a7f10c7073fb0de))
* **crm:** Upload view UI when it is locked ([868f62a](https://github.com/resola-ai/deca-apps/commit/868f62a1f1ad8e6fccc967b1303c984e8a3a4172))

### Bug Fixes

* **crm:** check empty receiver before sending ([46ddc47](https://github.com/resola-ai/deca-apps/commit/46ddc47fc9309b1aa3793d7f92240cdf38b13ab4))
* **crm:** Fix wrong id view when hiding field ([#4052](https://github.com/resola-ai/deca-apps/issues/4052)) ([dd41de5](https://github.com/resola-ai/deca-apps/commit/dd41de550e8ec44d8ccbf05729ef04248d80863b))
* **crm:** Support search with key arrow up and down ([54e8162](https://github.com/resola-ai/deca-apps/commit/54e8162d94c91741ff613e07a8c47d69f047767a))
* **crm:** TK - 4459 - Fix edit trigger wrong fields ([#3929](https://github.com/resola-ai/deca-apps/issues/3929)) ([2f1e510](https://github.com/resola-ai/deca-apps/commit/2f1e510d75bc8a914457787f252e7f237a43992b))
* **crm:** TK-3186 show error notification correctly ([46eecc0](https://github.com/resola-ai/deca-apps/commit/46eecc094cf644c228a5ee8b7479ac44c2c07188))
* **crm:** TK-3477 - Update tags list ([7698b73](https://github.com/resola-ai/deca-apps/commit/7698b73190a2b2e4ecf1a160c32285fe22160a72))
* **crm:** TK-3945 - Fix cannot save value with blank ([dc3d916](https://github.com/resola-ai/deca-apps/commit/dc3d916d358a5388a7c342ce4b262bdb596ad40c))
* **crm:** TK-4262 correct filter name ([128be7d](https://github.com/resola-ai/deca-apps/commit/128be7d9d2b2d75168100c122c64bf9bd58a1c0e))
* **crm:** TK-4370 reduce html template requests ([95e8a6c](https://github.com/resola-ai/deca-apps/commit/95e8a6ca115cbc03b4b4dba6883fd8ce1a951c61))
* **crm:** tk-4552 Return correct status for Email/SMS when sending failed ([06f6c81](https://github.com/resola-ai/deca-apps/commit/06f6c81c9128462fd3e7cda0d9ebf3ce0b834342))
* **crm:** TK-4577, TK-4582 fix highlight and missing icon ([#4006](https://github.com/resola-ai/deca-apps/issues/4006)) ([22b021f](https://github.com/resola-ai/deca-apps/commit/22b021f20f16cd591650c350f400ddff1ff957e4))
* **crm:** update email template and editor with mantine v7 ([fa60514](https://github.com/resola-ai/deca-apps/commit/fa605142192b376221e1dab07163af443a479fad))
* **crm:** update type ([cc1d4ab](https://github.com/resola-ai/deca-apps/commit/cc1d4ab81a1810f7d57b398dac824c45086d1baa))


### [0.5.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.1) (2025-01-14)

### Features

* **crm:** update Tolgee translation


## [0.5.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.5.0) (2025-01-14)


### Features

* **crm:** fix build falied ([3e31d15](https://github.com/resola-ai/deca-apps/commit/3e31d153de23b6f71852a3eeea7e77a8ab3f2210))
* **crm:** TK - 3612 - improve condition ([35f2ea3](https://github.com/resola-ai/deca-apps/commit/35f2ea3c93bf9eb75efe691039e608713858ee40))
* **crm:** TK-2264 - Update manual record activity ([9493954](https://github.com/resola-ai/deca-apps/commit/9493954a6fd015d13f5c9c3fda597e3bdb052010))
* **crm:** TK-2568 - Review error in Japanese ([#3845](https://github.com/resola-ai/deca-apps/issues/3845)) ([b025587](https://github.com/resola-ai/deca-apps/commit/b025587bdea315fbd89f01312599fdf0a49e21d9))
* **crm:** TK-3186 Display toast message and email status when sending and email ([2d314d5](https://github.com/resola-ai/deca-apps/commit/2d314d5b4f27f36d22d930acc3886641eecbe378))
* **crm:** TK-3948 - not allow edit field in profile detail when view is locked ([6c0ec10](https://github.com/resola-ai/deca-apps/commit/6c0ec10cfcc116961a9c4534404b1e968d201a37))
* **crm:** TK-3958 Implement new long text field with support pin to tabbar ([4b7b936](https://github.com/resola-ai/deca-apps/commit/4b7b9363d2cb2b2f36a9ed091b5cdc894c276df0))
* **crm:** TK-3979 - Add UI custom object fields ([5ef716f](https://github.com/resola-ai/deca-apps/commit/5ef716ffeb372f9b7d55987c815795ec97fb5149))
* **crm:** TK-3979 - Improve showing full fields ([#3876](https://github.com/resola-ai/deca-apps/issues/3876)) ([2911094](https://github.com/resola-ai/deca-apps/commit/29110948e315674bef88ebf242ace1767cc279cd))
* **crm:** TK-3979 - show full fields configurations ([#3857](https://github.com/resola-ai/deca-apps/issues/3857)) ([ff935a4](https://github.com/resola-ai/deca-apps/commit/ff935a418ac0d70ef0025438c59aae9e0ce1eb52))
* **crm:** TK-4153 TK-4150 make profile data work properly, minor UI updates ([d3359ca](https://github.com/resola-ai/deca-apps/commit/d3359ca2fc488114dd4fde719f61df68ea26db3a))
* **crm:** TK-4168 - Update template activities ([#3836](https://github.com/resola-ai/deca-apps/issues/3836)) ([1f1dde5](https://github.com/resola-ai/deca-apps/commit/1f1dde52d28c423e68fc11dba39b43a9a0e5c636))
* **crm:** TK-4238 remove white spaces onSave text data ([44345c1](https://github.com/resola-ai/deca-apps/commit/44345c1bf6cc4e1279aa13b9336aefb5ede43edc))
* **crm:** update default language tolgee ([b3a9591](https://github.com/resola-ai/deca-apps/commit/b3a9591aae7b89e71a524bf89a7f10c7073fb0de))
* **crm:** Upload view UI when it is locked ([868f62a](https://github.com/resola-ai/deca-apps/commit/868f62a1f1ad8e6fccc967b1303c984e8a3a4172))

### Bug Fixes

* **crm:** Support search with key arrow up and down ([54e8162](https://github.com/resola-ai/deca-apps/commit/54e8162d94c91741ff613e07a8c47d69f047767a))
* **crm:** TK-3186 show error notification correctly ([46eecc0](https://github.com/resola-ai/deca-apps/commit/46eecc094cf644c228a5ee8b7479ac44c2c07188))
* **crm:** TK-3477 - Update tags list ([7698b73](https://github.com/resola-ai/deca-apps/commit/7698b73190a2b2e4ecf1a160c32285fe22160a72))
* **crm:** TK-3945 - Fix cannot save value with blank ([dc3d916](https://github.com/resola-ai/deca-apps/commit/dc3d916d358a5388a7c342ce4b262bdb596ad40c))
* **crm:** TK-4262 correct filter name ([128be7d](https://github.com/resola-ai/deca-apps/commit/128be7d9d2b2d75168100c122c64bf9bd58a1c0e))


### [0.4.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.2) (2024-12-20)


### Features

* **crm:** CRM - 2655- Improve tags list ([7ee600f](https://github.com/resola-ai/deca-apps/commit/7ee600ff4728f4573b064b54a225368a3695beff))
* **crm:** CRM - 348 - Show message when there is no fields ([23628a4](https://github.com/resola-ai/deca-apps/commit/23628a4de473dd5a3fcbc1dac9b3d79f906cba7b))
* **crm:** TK-2655 - Tags improvement ([27ec447](https://github.com/resola-ai/deca-apps/commit/27ec447d6cf5471c14a6a2405509a5014d5ffb63))
* **crm:** TK-3375 - Improve visible selected object on sidebar ([74d33f4](https://github.com/resola-ai/deca-apps/commit/74d33f4618f4db480ebde60cd451a46b0c0e7944))
* **crm:** TK-3612 - Show index if name is empty ([32147f6](https://github.com/resola-ai/deca-apps/commit/32147f6591466317b1cebfe78423b40577b39e05))
* **crm:** update UI for crm sort options ([4df03f5](https://github.com/resola-ai/deca-apps/commit/4df03f546d7d1d879c7aa9ba9afbd576d03b0788))

### Bug Fixes

* **crm:** addNewRow button ([abf4c14](https://github.com/resola-ai/deca-apps/commit/abf4c148fa7149b58afaf788daa0fa88773641c8))
* **crm:** improve padding for sort select items ([29fb1d6](https://github.com/resola-ai/deca-apps/commit/29fb1d607df431a026dcd23c3ccbfb6ca7b7f41d))
* **crm:** make autoSorting work properly ([3e19446](https://github.com/resola-ai/deca-apps/commit/3e194467c0332388153d9c0d2a2c9cea65500dae))
* **crm:** TK-2908 - Fix cannot edit type long text ([a2b644c](https://github.com/resola-ai/deca-apps/commit/a2b644c15e0561b023678c7b93aa4c400b015159))
* **crm:** TK-3069 - fix reset merge value when canceling ([877a9c3](https://github.com/resola-ai/deca-apps/commit/877a9c34685599f10e55e6fa13c44366d5ed0ac7))
* **crm:** TK-3358 bring back cancel/sort buttons for empty case ([fc9728a](https://github.com/resola-ai/deca-apps/commit/fc9728a3b04664fee995d2f7f1b1b217df0d932e))

### [0.4.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.1) (2024-11-29)


### Features

* **crm:** CRM - 303 - improve tags selected ([e2f8a53](https://github.com/resola-ai/deca-apps/commit/e2f8a5340ac8bb77afef9d617d17e5453cf9459e))
* **crm:** CRM - 320 - Improve UI object list ([6342944](https://github.com/resola-ai/deca-apps/commit/63429448490abf384cc2b041e9160ffc04e84e19))
* **crm:** CRM - 345 - Update description sort ([8102a97](https://github.com/resola-ai/deca-apps/commit/8102a9749fb120c4f5609fdaaba1803955770254))
* **crm:** CRM - 351 - Add hover for primary email ([c492fe1](https://github.com/resola-ai/deca-apps/commit/c492fe1faa5c5e1792432e7adebff33865c437d5))
* **crm:** CRM - 351 - show primary description ([f2ac706](https://github.com/resola-ai/deca-apps/commit/f2ac70660ba8d185e547d0557ab03aa9a4496401))
* **crm:** CRM - 357 - Disable delete button if no views ([56f0047](https://github.com/resola-ai/deca-apps/commit/56f0047ba8937212247bec769a81db9afd0ae0dc))
* **crm:** CRM - 365 - Filter by tags ([dcc6ca9](https://github.com/resola-ai/deca-apps/commit/dcc6ca9504aaa28e553c8d51acbc1b44dfee6606))
* **crm:** CRM - 365 - Fix remove tag selected ([19366f8](https://github.com/resola-ai/deca-apps/commit/19366f83d9144f8f4007510375cd7ef444610637))
* **crm:** CRM - 365 - Improve tags filter ([8b60a9c](https://github.com/resola-ai/deca-apps/commit/8b60a9c3e2375b5975aecb108ad8870643e13d89))
* **crm:** CRM - 393 - Improve merge columns ([03f747d](https://github.com/resola-ai/deca-apps/commit/03f747d2fa7640c925825cc339e0513c2a5c4dda))
* **crm:** CRM - 393 - Merge profile ([aa4895e](https://github.com/resola-ai/deca-apps/commit/aa4895e089208c5f5a8c52435de465dbec58461b))
* **crm:** CRM - 410 - Improve history rendering ([d7d0664](https://github.com/resola-ai/deca-apps/commit/d7d06645fbf8c67ac07c25ea3e28fcd73a3124fe))
* **crm:** CRM - 417 - prevent merge if input  the wrong values ([b4136db](https://github.com/resola-ai/deca-apps/commit/b4136db292c944cd667d20cf5e71837b5849e830))
* **crm:** CRM - link to record cell improvement ([d30a074](https://github.com/resola-ai/deca-apps/commit/d30a074785d8ee41699224fc62bdc5dc775f2790))
* **crm:** CRM - update translation ([f82ed5b](https://github.com/resola-ai/deca-apps/commit/f82ed5bfeb20eb2ef2368037e8576a5715cc139f))
* **crm:** duplicate view alert, templates fixing ([907a0f0](https://github.com/resola-ai/deca-apps/commit/907a0f09eb7b830bfa730c41543deeed0f050abe))
* **crm:** email/sms UI improvements ([ec36a0e](https://github.com/resola-ai/deca-apps/commit/ec36a0ee82ba5e839f12912475ee26912e3ad859))
* **crm:** fix type build failed ([d25edd5](https://github.com/resola-ai/deca-apps/commit/d25edd55b5dae42377b62493de62b6b75da6c2db))
* **crm:** Improve edit field in profile ([d3e8eb4](https://github.com/resola-ai/deca-apps/commit/d3e8eb49f8ea86fe2da5c902916a0927e4a21ba9))
* **crm:** Improvement for fill activities ([251307b](https://github.com/resola-ai/deca-apps/commit/251307bd8cec3d82ac2f044b2da8b1f2b752b13e))
* **crm:** Manual fill for Activities ([683495a](https://github.com/resola-ai/deca-apps/commit/683495a1de6084eaf1a802253a3de27433311c15))
* **crm:** prefill receiver on email template ([4bbdef0](https://github.com/resola-ai/deca-apps/commit/4bbdef0b6b15e70539f38302c6040304a947af3e))
* **crm:** save autoSort to BE, make sort/filter work more properly based on requests ([4ac1f45](https://github.com/resola-ai/deca-apps/commit/4ac1f457f297dbc9c24e473e554e2450ac30adf4))
* **crm:** Separate activity modal ([ec507f5](https://github.com/resola-ai/deca-apps/commit/ec507f52beba3d41ac82d18a28c1a7507d676a16))
* **crm:** sms/email templates UI improvements ([1b898b4](https://github.com/resola-ai/deca-apps/commit/1b898b4450df26e8ad679f9e52d4178ea478e883))
* **crm:** Sorting behavior update and improvement ([05d8a36](https://github.com/resola-ai/deca-apps/commit/05d8a3619fdb1ec67a6451f823ec547b1035c952))
* **crm:** support activity templates multi language ([dc49ec1](https://github.com/resola-ai/deca-apps/commit/dc49ec152f514231f4d28a198dedab5c5a4dcd11))
* **crm:** support activity templates multi language ([110c710](https://github.com/resola-ai/deca-apps/commit/110c7101c20efcd465b91cead2323a456f0b399c))
* **crm:** sync dev ([46cadc2](https://github.com/resola-ai/deca-apps/commit/46cadc264187fdd6a3cf902c7a64696bf3762dca))
* **crm:** sync dev ([a3e960f](https://github.com/resola-ai/deca-apps/commit/a3e960fb0f8c9f5f0f4a29c8cd7a436feb5b7d6e))
* **crm:** sync locales with tolgee, update spacing ([c220f36](https://github.com/resola-ai/deca-apps/commit/c220f36763201f5177b5d3fbc8f8616e0deae213))
* **crm:** update CRM filter UI, sort behavior ([d7e19c7](https://github.com/resola-ai/deca-apps/commit/d7e19c7a26ec25ed1b1640414e2be7a72a32ed00))
* **crm:** update sort/filter hightlights ([a4690a8](https://github.com/resola-ai/deca-apps/commit/a4690a81bad82d130754d4cd7a59a4fc890765a3))
* **crm:** update template format ([aca9de0](https://github.com/resola-ai/deca-apps/commit/aca9de0709cc8f52182bb5eb4e8c9cd6bd62a2c3))
* **crm:** Update tolgee structure folder ([a7f943b](https://github.com/resola-ai/deca-apps/commit/a7f943b94231ff4bfb8b87301d21c8286f06df41))
* **crm:** update toolbar constant ([6e7da39](https://github.com/resola-ai/deca-apps/commit/6e7da3978ac285acf3c4477b448716364365216f))
* **TK-2644:** CRM - TK-2644 - Update validation ([f053a83](https://github.com/resola-ai/deca-apps/commit/f053a83b958079ae7eab527351486e83387d98d7))

### Bug Fixes

* **crm:** CRM - 412 - add min height prevent shinked ([6e19af8](https://github.com/resola-ai/deca-apps/commit/6e19af80bc3903a05fd61db4d4da11a7b43e4eee))
* **crm:** CRM - 415 - Fix icon is small ([5f8b6b7](https://github.com/resola-ai/deca-apps/commit/5f8b6b74962e072112922a76c618abf905a91676))
* **crm:** Fix align text and icon ([e02f8f5](https://github.com/resola-ai/deca-apps/commit/e02f8f58042992603d038bc3724e768838312fc0))
* **crm:** Fix validate fields merging ([c80a34e](https://github.com/resola-ai/deca-apps/commit/c80a34ed1cde76161b0f6d7cb73c5e7f430a7789))
* **crm:** use the same prosemirror-model package ([f7c61e4](https://github.com/resola-ai/deca-apps/commit/f7c61e46539909a0c5ecc12c061e5bd22c281ce0))


## [0.4.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.4.0) (2024-11-15)


### Features
* **crm:** CRM - 320 - Improve UI object list ([6342944](https://github.com/resola-ai/deca-apps/commit/63429448490abf384cc2b041e9160ffc04e84e19))
* **crm:** CRM - 365 - Filter by tags ([dcc6ca9](https://github.com/resola-ai/deca-apps/commit/dcc6ca9504aaa28e553c8d51acbc1b44dfee6606))
* **crm:** CRM - 365 - Fix remove tag selected ([19366f8](https://github.com/resola-ai/deca-apps/commit/19366f83d9144f8f4007510375cd7ef444610637))
* **crm:** CRM - 365 - Improve tags filter ([8b60a9c](https://github.com/resola-ai/deca-apps/commit/8b60a9c3e2375b5975aecb108ad8870643e13d89))
* **crm:** CRM - 393 - Improve merge columns ([03f747d](https://github.com/resola-ai/deca-apps/commit/03f747d2fa7640c925825cc339e0513c2a5c4dda))
* **crm:** CRM - 393 - Merge profile ([aa4895e](https://github.com/resola-ai/deca-apps/commit/aa4895e089208c5f5a8c52435de465dbec58461b))
* **crm:** CRM - 410 - Improve history rendering ([d7d0664](https://github.com/resola-ai/deca-apps/commit/d7d06645fbf8c67ac07c25ea3e28fcd73a3124fe))
* **crm:** CRM - update translation ([f82ed5b](https://github.com/resola-ai/deca-apps/commit/f82ed5bfeb20eb2ef2368037e8576a5715cc139f))
* **crm:** duplicate view alert, templates fixing ([907a0f0](https://github.com/resola-ai/deca-apps/commit/907a0f09eb7b830bfa730c41543deeed0f050abe))
* **crm:** email/sms UI improvements ([ec36a0e](https://github.com/resola-ai/deca-apps/commit/ec36a0ee82ba5e839f12912475ee26912e3ad859))
* **crm:** fix type build failed ([d25edd5](https://github.com/resola-ai/deca-apps/commit/d25edd55b5dae42377b62493de62b6b75da6c2db))
* **crm:** Improve edit field in profile ([d3e8eb4](https://github.com/resola-ai/deca-apps/commit/d3e8eb49f8ea86fe2da5c902916a0927e4a21ba9))
* **crm:** Improvement for fill activities ([251307b](https://github.com/resola-ai/deca-apps/commit/251307bd8cec3d82ac2f044b2da8b1f2b752b13e))
* **crm:** Manual fill for Activities ([683495a](https://github.com/resola-ai/deca-apps/commit/683495a1de6084eaf1a802253a3de27433311c15))
* **crm:** prefill receiver on email template ([4bbdef0](https://github.com/resola-ai/deca-apps/commit/4bbdef0b6b15e70539f38302c6040304a947af3e))
* **crm:** Separate activity modal ([ec507f5](https://github.com/resola-ai/deca-apps/commit/ec507f52beba3d41ac82d18a28c1a7507d676a16))
* **crm:** sms/email templates UI improvements ([1b898b4](https://github.com/resola-ai/deca-apps/commit/1b898b4450df26e8ad679f9e52d4178ea478e883))
* **crm:** support activity templates multi language ([dc49ec1](https://github.com/resola-ai/deca-apps/commit/dc49ec152f514231f4d28a198dedab5c5a4dcd11))
* **crm:** support activity templates multi language ([110c710](https://github.com/resola-ai/deca-apps/commit/110c7101c20efcd465b91cead2323a456f0b399c))
* **crm:** sync dev ([46cadc2](https://github.com/resola-ai/deca-apps/commit/46cadc264187fdd6a3cf902c7a64696bf3762dca))
* **crm:** sync dev ([a3e960f](https://github.com/resola-ai/deca-apps/commit/a3e960fb0f8c9f5f0f4a29c8cd7a436feb5b7d6e))
* **crm:** update template format ([aca9de0](https://github.com/resola-ai/deca-apps/commit/aca9de0709cc8f52182bb5eb4e8c9cd6bd62a2c3))
### Bug Fixes
* **crm:** Fix align text and icon ([e02f8f5](https://github.com/resola-ai/deca-apps/commit/e02f8f58042992603d038bc3724e768838312fc0))
* **crm:** use the same prosemirror-model package ([f7c61e4](https://github.com/resola-ai/deca-apps/commit/f7c61e46539909a0c5ecc12c061e5bd22c281ce0))
### [0.3.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.2) (2024-11-01)

### Features

* **crm:** Add custom font-family and font-size to editor ([026a001](https://github.com/resola-ai/deca-apps/commit/026a001cdd7e7c00da257c344fe2dda9c506d349))
* **crm:** Change 'long date' format for Japanese ([ba07d38](https://github.com/resola-ai/deca-apps/commit/ba07d387f39b6be82572867ca4cd2358190e0a07))
* **crm:** CRM - 370 - 394 - Allow edit record cell in profile ([031f50b](https://github.com/resola-ai/deca-apps/commit/031f50b1a92af5db7009b5ea3fcd8b68abd79a7a))
* **crm:** CRM - 393 - Merge Profile UI ([116a8a9](https://github.com/resola-ai/deca-apps/commit/116a8a931ec264f95ed90602091dfaa52e91c3c0))
* **crm:** CRM - 404 - enable crm navigation ([933b405](https://github.com/resola-ai/deca-apps/commit/933b4054292af4ac8d5d5a51292d9bf700943d28))
* **crm:** Disable merge feature ([71879f5](https://github.com/resola-ai/deca-apps/commit/71879f527a720256edea733b848fb84321139c28))
* **crm:** disable merge profile ([b362a38](https://github.com/resola-ai/deca-apps/commit/b362a38332ff0e9120b2e5ddb9c859e3a6168d84))
* **crm:** Improve select type render in form ([2798bbd](https://github.com/resola-ai/deca-apps/commit/2798bbd10225304cda0762f0a471e97d91b919b9))


### [0.3.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.1) (2024-10-25)

### Bug Fixes
* **crm:** update CRM locales ([7d16481](https://github.com/resola-ai/deca-apps/commit/7d164817dfcefafeb1664ea532d9279d1a258b46))


## [0.3.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.3.0) (2024-10-25)

### Features

* **crm:** Add data row id ([dc4eda8](https://github.com/resola-ai/deca-apps/commit/dc4eda8434df6e6ee69e1e7e17eb31fe1e663467))
* **crm:** add email noti when sending or saving, error state for required fields ([63f95d7](https://github.com/resola-ai/deca-apps/commit/63f95d75f8cbf43e5862d9edb2c2c125bc4997c3))
* **crm:** add email noti when sending or saving, error state for required fields ([9dabf9d](https://github.com/resola-ai/deca-apps/commit/9dabf9d24bdda6f82b176636f7908f7f16f04c56))
* **crm:** CRM - 347 - Freeze add button (+) column ([90d8cf3](https://github.com/resola-ai/deca-apps/commit/90d8cf325034937061753c5450e763f1ea55c3b0))
* **crm:** CRM - 347 - update css ([7fc1a92](https://github.com/resola-ai/deca-apps/commit/7fc1a926efcbca004340ba290d3cd67ad25ca9e5))
* **crm:** CRM - 350 - Allow user selection none ([8aed553](https://github.com/resola-ai/deca-apps/commit/8aed5538e38594a4f5d996fa38657940c54b502a))
* **crm:** feat - 349 - Match field order record same as view ([29b5001](https://github.com/resola-ai/deca-apps/commit/29b5001f4ba60bd83b6475916231912e9fcdc58e))
* **crm:** Fix chevron is reversed ([1405ee5](https://github.com/resola-ai/deca-apps/commit/1405ee59ff8ef71fc7d2179ca7cb1e9960cef26c))
* **crm:** Improve height for scroll bar ([bf53741](https://github.com/resola-ai/deca-apps/commit/bf537412e3d2bc1b74cb72ef90825e2ef29449e8))
* **crm:** Improve view lock error ([0d49ac5](https://github.com/resola-ai/deca-apps/commit/0d49ac5c0c72273aaacb7012f9ff6f9266f64f0c))
* **crm:** loading state on toolbar ([54b94a7](https://github.com/resola-ai/deca-apps/commit/54b94a72f796dddb1a60758d52f2e20dfbd6a12c))
* **crm:** make search box work stable, update locales and states ([df68982](https://github.com/resola-ai/deca-apps/commit/df68982cfa0bcd9a20c7ae7f03940a86980c85c3))
* **crm:** Not allow box resize if not meet min height ([67b8d66](https://github.com/resola-ai/deca-apps/commit/67b8d66449c2f9bf47f0cc2021b30a5104ba18fc))
* **crm:** remove console.log ([5d35648](https://github.com/resola-ai/deca-apps/commit/5d356487d1020f49bbaad7a7068fcfb0416dee26))
* **crm:** sms/email templates ([1e70f8a](https://github.com/resola-ai/deca-apps/commit/1e70f8a453cb0e2ae3d59ad735a1b2e45d59ae42))
* **crm:** sync dev ([da4dc3a](https://github.com/resola-ai/deca-apps/commit/da4dc3a89d147a33b6300d51193512a1d3920c01))
* **crm:** sync dev ([0ebd180](https://github.com/resola-ai/deca-apps/commit/0ebd18006931617c3a4efa2ad02266ff191ca69d))
* **crm:** update missing select value object fields ([4926ff3](https://github.com/resola-ai/deca-apps/commit/4926ff350874a09e5583c042c77fac86fb44759c))
* **crm:** workspace context and table improvements ([f2d6167](https://github.com/resola-ai/deca-apps/commit/f2d6167675ffaf6d9254f1daaeb76ac9bbfcf7e4))

### Bug Fixes

* **crm:** CRM - 339 - Fix icon is shrinked ([#3071](https://github.com/resola-ai/deca-apps/issues/3071)) ([c0b9dc5](https://github.com/resola-ai/deca-apps/commit/c0b9dc501a86c06ec3f70111f0b92a7e4e1440af))
* **crm:** CRM - 366 - Fix modal not closed ([0801c41](https://github.com/resola-ai/deca-apps/commit/0801c4102bd725fc129504f78930b56f37b1121d))
* **crm:** CRM - 367 - Remove format phone ([5c8dba6](https://github.com/resola-ai/deca-apps/commit/5c8dba6089d476796b844a44c5e78aab56de7cdb))
* **crm:** CRM - 369 - Fix search is not cleared ([f58bbd7](https://github.com/resola-ai/deca-apps/commit/f58bbd798dd52cbf29458b55f7cffaf3ca4c0fe3))
* **crm:** CRM - 373 - Missing remove button ([79aaaac](https://github.com/resola-ai/deca-apps/commit/79aaaac94b6dc01070eea85be9e680d7cc4f17c2))
* **crm:** CRM - 375 - Hide unused menu ([50feb25](https://github.com/resola-ai/deca-apps/commit/50feb254c09862d3a7f1f2045724fc4958da6815))
* **crm:** email/sms templates improvement, bug fixes ([ee1564f](https://github.com/resola-ai/deca-apps/commit/ee1564f4a7d40a9c1460ba82ffb2624f5f8adb5c))
* **crm:** Phone number should have validation min max length ([026e385](https://github.com/resola-ai/deca-apps/commit/026e3855b60b492b2e4749b51663a62b376cb8c2))
* **crm:** reset input when click clear button ([85b26bc](https://github.com/resola-ai/deca-apps/commit/85b26bc38cac8f66b9d32b771da7ea0dfbcfc2a4))
* **crm:** reset input when click clear button ([a184d5e](https://github.com/resola-ai/deca-apps/commit/a184d5e6efe41a8fcb4340f24e884a1fd79a908c))
* **crm:** support locales for filter ([5f12304](https://github.com/resola-ai/deca-apps/commit/5f12304cf4f84e9feb5f01f1a2f56e9bacaf6689))
* **crm:** update CRM JA text ([70252d9](https://github.com/resola-ai/deca-apps/commit/70252d934668fd739106fe848632ad4d50d139a0))


### Features

* **crm:** add email activity, UI bug fixes ([#2976](https://github.com/resola-ai/deca-apps/issues/2976)) ([4409c3a](https://github.com/resola-ai/deca-apps/commit/4409c3aedac440b82dbae4dd6fdad3d4790ea4dc))
* **crm:** CRM - 254 - Add drag icon card box ([#2848](https://github.com/resola-ai/deca-apps/issues/2848)) ([7887258](https://github.com/resola-ai/deca-apps/commit/7887258489aad5d30eac8922bac53030cded63f2))
* **crm:** CRM - 254 - improve loading forms ([36b1d69](https://github.com/resola-ai/deca-apps/commit/36b1d69d63dacd2d64cde5fabf3011dc15b972d6))
* **crm:** CRM - 254 - Improve profile detail form box ([5dee343](https://github.com/resola-ai/deca-apps/commit/5dee343718e6ef25baf26046daa4dbc03e32b9d2))
* **crm:** CRM - 287 - Implement lock view ([3ec88ac](https://github.com/resola-ai/deca-apps/commit/3ec88ac550a29267ba263715293b8be47d8a26a3))
* **crm:** CRM - 287 - improve cell when lock view ([8a88e99](https://github.com/resola-ai/deca-apps/commit/8a88e9963e20ab2377f850b833148996ee71f4fe))
* **crm:** CRM - 291 - Improve lock view and clear ([dd65aa5](https://github.com/resola-ai/deca-apps/commit/dd65aa57c8ee289039650027a6e1bd5d38b13796))
* **crm:** CRM - 311 - Add edit record object in profile detail ([f057757](https://github.com/resola-ai/deca-apps/commit/f05775730f49b9752a275cbf4b0dd27b4069d056))
* **crm:** CRM - 312 - Add button redirect to linked record ([edd5803](https://github.com/resola-ai/deca-apps/commit/edd580358785035138af70b7ff9c2fd07f997e34))
* **crm:** CRM - 312 - update hover state ([b171e22](https://github.com/resola-ai/deca-apps/commit/b171e229400bce6a5b2df3d956096bc5be6975ad))
* **crm:** CRM - 312 - update service translate ([0f15390](https://github.com/resola-ai/deca-apps/commit/0f15390a8c080b35bb7282c91613779be1252b4f))
* **crm:** CRM - 322 - Improve box card feedback ([e4d01aa](https://github.com/resola-ai/deca-apps/commit/e4d01aa9324e48e481293b01db4769c28ccaecbd))
* **crm:** CRM - 322 - update height box cards ([f39f8cc](https://github.com/resola-ai/deca-apps/commit/f39f8cc72d79f16079a57524884c38d32754e438))
* **crm:** CRM - 322 -refetch object ([f569019](https://github.com/resola-ai/deca-apps/commit/f569019a6d169c8488da9c1916aa1dc8e697f03a))
* **crm:** CRM - 335 - improve hyperlink clicking ([0c93ed2](https://github.com/resola-ai/deca-apps/commit/0c93ed2f72fac0d808913594605f103e514cec7b))
* **crm:** CRM - fix cannot delete view ([#2913](https://github.com/resola-ai/deca-apps/issues/2913)) ([2c9fb71](https://github.com/resola-ai/deca-apps/commit/2c9fb719465fefc89520850a324e2cf5c7078c21))
* **crm:** CRM - Remove create default view ([dad07c7](https://github.com/resola-ai/deca-apps/commit/dad07c75ef4d42ef673ecc572acd0434e9320d00))
* **crm:** CRM - remove duplicate code ([a3204ea](https://github.com/resola-ai/deca-apps/commit/a3204eaaccb0138086a97ee778f232fdfaebd3ee))
* **crm:** CRM - update build dev ([#2940](https://github.com/resola-ai/deca-apps/issues/2940)) ([522d44e](https://github.com/resola-ai/deca-apps/commit/522d44e8d75ca24759893c723de04c3c8ac36464))
* **crm:** CRM- 291 - Add message when deleting lock object views ([711060f](https://github.com/resola-ai/deca-apps/commit/711060fc31a5655b5294d967365abb74729e2ae2))
* **crm:** CRM-321 integrate email/sms with API ([#2952](https://github.com/resola-ai/deca-apps/issues/2952)) ([22bcaf8](https://github.com/resola-ai/deca-apps/commit/22bcaf88ba8c8a2a0a083e74c42f6e31b4b38f50))
* **crm:** fix build failed ([34039fd](https://github.com/resola-ai/deca-apps/commit/34039fd394811abbdbfdd6bac120098b836333b1))
* **crm:** Fix build failed ([ac7f836](https://github.com/resola-ai/deca-apps/commit/ac7f836b94eca15c0d698d6f15599da08513efb7))
* **crm:** Improve padding ([abaecfe](https://github.com/resola-ai/deca-apps/commit/abaecfee309985697ebca9baa71b478592864654))
* **crm:** line activity template, switch view on duplicate ([#2947](https://github.com/resola-ai/deca-apps/issues/2947)) ([88994bb](https://github.com/resola-ai/deca-apps/commit/88994bb4eb70cf919f1de82e1caad58a9a4f0fd2))
* **crm:** setting improvement integrate ([8d9d28f](https://github.com/resola-ai/deca-apps/commit/8d9d28f6f098c36ed21ac7057e3e00d7497f7963))
* **crm:** SMS template, add locale for datepicker, fix build failed ([#3004](https://github.com/resola-ai/deca-apps/issues/3004)) ([6e1f53f](https://github.com/resola-ai/deca-apps/commit/6e1f53ff745e3f1fe9de7252bf56c4b4a62d16a0))
* **crm:** update changelog for v0.2.1 ([b7c637a](https://github.com/resola-ai/deca-apps/commit/b7c637a3b2c3e5fb6a7413ef46cf93a06ebbbe76))
* **crm:** update env tolgee vars ([b82d450](https://github.com/resola-ai/deca-apps/commit/b82d4500e4c3f19a43fc37514593c9a09fa66afb))

### Bug Fixes

* **crm:** CRM - 291: Fix cannot clear data ([da6b81e](https://github.com/resola-ai/deca-apps/commit/da6b81e141aa89f730009188fdfaca8d4769099c))
* **crm:** CRM - 308 - Fix disable add row when view is locked ([8db894e](https://github.com/resola-ai/deca-apps/commit/8db894e60beac0f64ea857e6cf64ea110f04d45f))
* **crm:** CRM - 313 - Fix cannot unlock the view ([c8843c8](https://github.com/resola-ai/deca-apps/commit/c8843c865bf6a4eaa0273e6cbeb50bb00d5335f0))
* **crm:** CRM - 315 - remove background disable state ([1f7e040](https://github.com/resola-ai/deca-apps/commit/1f7e040add182820079d2ddfd2284331a71ceb78))
* **crm:** CRM - 317 - Fix crash using gg translate ([3d0bc3d](https://github.com/resola-ai/deca-apps/commit/3d0bc3d3430a80ad0995d39d87fb534b76ad6b98))
* **crm:** CRM - 325 - add scrolling to menu dropdown ([4b292fd](https://github.com/resola-ai/deca-apps/commit/4b292fd17d288844966d68ec1345c3b0906f5329))
* **crm:** CRM-316 CRM-328 make profile datetime show properly, MA activity template, minor bug fixes ([#2988](https://github.com/resola-ai/deca-apps/issues/2988)) ([5c9fb15](https://github.com/resola-ai/deca-apps/commit/5c9fb15c9a37aabff74df511a955f86a1b32859b))
* **crm:** Fix loading endlessly ([25bc21a](https://github.com/resola-ai/deca-apps/commit/25bc21a22d9b4a5cc603b550b9e27cfdf6c908c8))
* **crm:** Fix-291- add missing props and remove redundant code ([86308b3](https://github.com/resola-ai/deca-apps/commit/86308b3514ade48ef668376efa5b42c72f06c8d5))
* **crm:** ignore Segmenter type ([#2857](https://github.com/resola-ai/deca-apps/issues/2857)) ([bd3df4e](https://github.com/resola-ai/deca-apps/commit/bd3df4e26732e6f6467db5e434affe1836aa8c5d))
* **crm:** support text underline on email ([#3036](https://github.com/resola-ai/deca-apps/issues/3036)) ([7980c22](https://github.com/resola-ai/deca-apps/commit/7980c2233fc7ebf4dc82de080a59a159a22f471a))
* **crm:** update CRM forms behavior ([6d400c7](https://github.com/resola-ai/deca-apps/commit/6d400c7a17dc4a59914858e16f5e2f2d6e05668b))
* **crm:** update image asset, minor UI improvement ([#2967](https://github.com/resola-ai/deca-apps/issues/2967)) ([f59cade](https://github.com/resola-ai/deca-apps/commit/f59cadedae3a367bd09962b590a35fe375032af8))
* **crm:** update to show Form objects ([a89d193](https://github.com/resola-ai/deca-apps/commit/a89d193640bc86d77d9834ee5a4456faafeb072e))


### [0.2.4](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.4) (2024-10-10)


### Features

* **crm:** add email activity, UI bug fixes ([#2976](https://github.com/resola-ai/deca-apps/issues/2976)) ([4409c3a](https://github.com/resola-ai/deca-apps/commit/4409c3aedac440b82dbae4dd6fdad3d4790ea4dc))
* **crm:** CRM - 254 - Add drag icon card box ([#2848](https://github.com/resola-ai/deca-apps/issues/2848)) ([7887258](https://github.com/resola-ai/deca-apps/commit/7887258489aad5d30eac8922bac53030cded63f2))
* **crm:** CRM - 254 - improve loading forms ([36b1d69](https://github.com/resola-ai/deca-apps/commit/36b1d69d63dacd2d64cde5fabf3011dc15b972d6))
* **crm:** CRM - 254 - Improve profile detail form box ([5dee343](https://github.com/resola-ai/deca-apps/commit/5dee343718e6ef25baf26046daa4dbc03e32b9d2))
* **crm:** CRM - 287 - Implement lock view ([3ec88ac](https://github.com/resola-ai/deca-apps/commit/3ec88ac550a29267ba263715293b8be47d8a26a3))
* **crm:** CRM - 287 - improve cell when lock view ([8a88e99](https://github.com/resola-ai/deca-apps/commit/8a88e9963e20ab2377f850b833148996ee71f4fe))
* **crm:** CRM - 291 - Improve lock view and clear ([dd65aa5](https://github.com/resola-ai/deca-apps/commit/dd65aa57c8ee289039650027a6e1bd5d38b13796))
* **crm:** CRM - 311 - Add edit record object in profile detail ([f057757](https://github.com/resola-ai/deca-apps/commit/f05775730f49b9752a275cbf4b0dd27b4069d056))
* **crm:** CRM - 312 - Add button redirect to linked record ([edd5803](https://github.com/resola-ai/deca-apps/commit/edd580358785035138af70b7ff9c2fd07f997e34))
* **crm:** CRM - 312 - update hover state ([b171e22](https://github.com/resola-ai/deca-apps/commit/b171e229400bce6a5b2df3d956096bc5be6975ad))
* **crm:** CRM - 312 - update service translate ([0f15390](https://github.com/resola-ai/deca-apps/commit/0f15390a8c080b35bb7282c91613779be1252b4f))
* **crm:** CRM - 322 - Improve box card feedback ([e4d01aa](https://github.com/resola-ai/deca-apps/commit/e4d01aa9324e48e481293b01db4769c28ccaecbd))
* **crm:** CRM - 322 - update height box cards ([f39f8cc](https://github.com/resola-ai/deca-apps/commit/f39f8cc72d79f16079a57524884c38d32754e438))
* **crm:** CRM - 322 -refetch object ([f569019](https://github.com/resola-ai/deca-apps/commit/f569019a6d169c8488da9c1916aa1dc8e697f03a))
* **crm:** CRM - 335 - improve hyperlink clicking ([0c93ed2](https://github.com/resola-ai/deca-apps/commit/0c93ed2f72fac0d808913594605f103e514cec7b))
* **crm:** CRM - fix cannot delete view ([#2913](https://github.com/resola-ai/deca-apps/issues/2913)) ([2c9fb71](https://github.com/resola-ai/deca-apps/commit/2c9fb719465fefc89520850a324e2cf5c7078c21))
* **crm:** CRM - Remove create default view ([dad07c7](https://github.com/resola-ai/deca-apps/commit/dad07c75ef4d42ef673ecc572acd0434e9320d00))
* **crm:** CRM - remove duplicate code ([a3204ea](https://github.com/resola-ai/deca-apps/commit/a3204eaaccb0138086a97ee778f232fdfaebd3ee))
* **crm:** CRM - update build dev ([#2940](https://github.com/resola-ai/deca-apps/issues/2940)) ([522d44e](https://github.com/resola-ai/deca-apps/commit/522d44e8d75ca24759893c723de04c3c8ac36464))
* **crm:** CRM- 291 - Add message when deleting lock object views ([711060f](https://github.com/resola-ai/deca-apps/commit/711060fc31a5655b5294d967365abb74729e2ae2))
* **crm:** CRM-321 integrate email/sms with API ([#2952](https://github.com/resola-ai/deca-apps/issues/2952)) ([22bcaf8](https://github.com/resola-ai/deca-apps/commit/22bcaf88ba8c8a2a0a083e74c42f6e31b4b38f50))
* **crm:** fix build failed ([34039fd](https://github.com/resola-ai/deca-apps/commit/34039fd394811abbdbfdd6bac120098b836333b1))
* **crm:** Fix build failed ([ac7f836](https://github.com/resola-ai/deca-apps/commit/ac7f836b94eca15c0d698d6f15599da08513efb7))
* **crm:** Improve padding ([abaecfe](https://github.com/resola-ai/deca-apps/commit/abaecfee309985697ebca9baa71b478592864654))
* **crm:** line activity template, switch view on duplicate ([#2947](https://github.com/resola-ai/deca-apps/issues/2947)) ([88994bb](https://github.com/resola-ai/deca-apps/commit/88994bb4eb70cf919f1de82e1caad58a9a4f0fd2))
* **crm:** setting improvement integrate ([8d9d28f](https://github.com/resola-ai/deca-apps/commit/8d9d28f6f098c36ed21ac7057e3e00d7497f7963))
* **crm:** update changelog for v0.2.1 ([b7c637a](https://github.com/resola-ai/deca-apps/commit/b7c637a3b2c3e5fb6a7413ef46cf93a06ebbbe76))
* **crm:** update env tolgee vars ([b82d450](https://github.com/resola-ai/deca-apps/commit/b82d4500e4c3f19a43fc37514593c9a09fa66afb))

### Bug Fixes

* **crm:** CRM - 291: Fix cannot clear data ([da6b81e](https://github.com/resola-ai/deca-apps/commit/da6b81e141aa89f730009188fdfaca8d4769099c))
* **crm:** CRM - 308 - Fix disable add row when view is locked ([8db894e](https://github.com/resola-ai/deca-apps/commit/8db894e60beac0f64ea857e6cf64ea110f04d45f))
* **crm:** CRM - 313 - Fix cannot unlock the view ([c8843c8](https://github.com/resola-ai/deca-apps/commit/c8843c865bf6a4eaa0273e6cbeb50bb00d5335f0))
* **crm:** CRM - 315 - remove background disable state ([1f7e040](https://github.com/resola-ai/deca-apps/commit/1f7e040add182820079d2ddfd2284331a71ceb78))
* **crm:** CRM - 317 - Fix crash using gg translate ([3d0bc3d](https://github.com/resola-ai/deca-apps/commit/3d0bc3d3430a80ad0995d39d87fb534b76ad6b98))
* **crm:** CRM - 325 - add scrolling to menu dropdown ([4b292fd](https://github.com/resola-ai/deca-apps/commit/4b292fd17d288844966d68ec1345c3b0906f5329))
* **crm:** CRM-316 CRM-328 make profile datetime show properly, MA activity template, minor bug fixes ([#2988](https://github.com/resola-ai/deca-apps/issues/2988)) ([5c9fb15](https://github.com/resola-ai/deca-apps/commit/5c9fb15c9a37aabff74df511a955f86a1b32859b))
* **crm:** Fix loading endlessly ([25bc21a](https://github.com/resola-ai/deca-apps/commit/25bc21a22d9b4a5cc603b550b9e27cfdf6c908c8))
* **crm:** Fix-291- add missing props and remove redundant code ([86308b3](https://github.com/resola-ai/deca-apps/commit/86308b3514ade48ef668376efa5b42c72f06c8d5))
* **crm:** ignore Segmenter type ([#2857](https://github.com/resola-ai/deca-apps/issues/2857)) ([bd3df4e](https://github.com/resola-ai/deca-apps/commit/bd3df4e26732e6f6467db5e434affe1836aa8c5d))
* **crm:** update CRM forms behavior ([6d400c7](https://github.com/resola-ai/deca-apps/commit/6d400c7a17dc4a59914858e16f5e2f2d6e05668b))
* **crm:** update image asset, minor UI improvement ([#2967](https://github.com/resola-ai/deca-apps/issues/2967)) ([f59cade](https://github.com/resola-ai/deca-apps/commit/f59cadedae3a367bd09962b590a35fe375032af8))
* **crm:** update to show Form objects ([a89d193](https://github.com/resola-ai/deca-apps/commit/a89d193640bc86d77d9834ee5a4456faafeb072e))


### [0.2.3](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.3) (2024-10-04)


### Features

* **crm:** CRM - fix cannot delete view ([#2913](https://github.com/resola-ai/deca-apps/issues/2913)) ([2c9fb71](https://github.com/resola-ai/deca-apps/commit/2c9fb719465fefc89520850a324e2cf5c7078c21))
* **crm:** CRM - remove duplicate code ([a3204ea](https://github.com/resola-ai/deca-apps/commit/a3204eaaccb0138086a97ee778f232fdfaebd3ee))

### Bug Fixes

* **crm:** CRM - 315 - remove background disable state ([1f7e040](https://github.com/resola-ai/deca-apps/commit/1f7e040add182820079d2ddfd2284331a71ceb78))
* **crm:** CRM - 317 - Fix crash using gg translate ([3d0bc3d](https://github.com/resola-ai/deca-apps/commit/3d0bc3d3430a80ad0995d39d87fb534b76ad6b98))
* **crm:** Fix-291- add missing props and remove redundant code ([86308b3](https://github.com/resola-ai/deca-apps/commit/86308b3514ade48ef668376efa5b42c72f06c8d5))


### [0.2.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.2) (2024-10-03)


### Features

* **crm:** CRM - 254 - Add drag icon card box ([#2848](https://github.com/resola-ai/deca-apps/issues/2848)) ([7887258](https://github.com/resola-ai/deca-apps/commit/7887258489aad5d30eac8922bac53030cded63f2))
* **crm:** CRM - 254 - improve loading forms ([36b1d69](https://github.com/resola-ai/deca-apps/commit/36b1d69d63dacd2d64cde5fabf3011dc15b972d6))
* **crm:** CRM - 254 - Improve profile detail form box ([5dee343](https://github.com/resola-ai/deca-apps/commit/5dee343718e6ef25baf26046daa4dbc03e32b9d2))
* **crm:** CRM - 287 - Implement lock view ([3ec88ac](https://github.com/resola-ai/deca-apps/commit/3ec88ac550a29267ba263715293b8be47d8a26a3))
* **crm:** CRM - 287 - improve cell when lock view ([8a88e99](https://github.com/resola-ai/deca-apps/commit/8a88e9963e20ab2377f850b833148996ee71f4fe))
* **crm:** CRM - 291 - Improve lock view and clear ([dd65aa5](https://github.com/resola-ai/deca-apps/commit/dd65aa57c8ee289039650027a6e1bd5d38b13796))
* **crm:** CRM- 291 - Add message when deleting lock object views ([711060f](https://github.com/resola-ai/deca-apps/commit/711060fc31a5655b5294d967365abb74729e2ae2))
* **crm:** Fix build failed ([ac7f836](https://github.com/resola-ai/deca-apps/commit/ac7f836b94eca15c0d698d6f15599da08513efb7))
* **crm:** Improve padding ([abaecfe](https://github.com/resola-ai/deca-apps/commit/abaecfee309985697ebca9baa71b478592864654))
* **crm:** setting improvement integrate ([8d9d28f](https://github.com/resola-ai/deca-apps/commit/8d9d28f6f098c36ed21ac7057e3e00d7497f7963))
* **crm:** update changelog for v0.2.1 ([b7c637a](https://github.com/resola-ai/deca-apps/commit/b7c637a3b2c3e5fb6a7413ef46cf93a06ebbbe76))

### Bug Fixes

* **crm:** CRM - 308 - Fix disable add row when view is locked ([8db894e](https://github.com/resola-ai/deca-apps/commit/8db894e60beac0f64ea857e6cf64ea110f04d45f))
* **crm:** CRM - 313 - Fix cannot unlock the view ([c8843c8](https://github.com/resola-ai/deca-apps/commit/c8843c865bf6a4eaa0273e6cbeb50bb00d5335f0))
* **crm:** Fix loading endlessly ([25bc21a](https://github.com/resola-ai/deca-apps/commit/25bc21a22d9b4a5cc603b550b9e27cfdf6c908c8))
* **crm:** ignore Segmenter type ([#2857](https://github.com/resola-ai/deca-apps/issues/2857)) ([bd3df4e](https://github.com/resola-ai/deca-apps/commit/bd3df4e26732e6f6467db5e434affe1836aa8c5d))
* **crm:** update CRM forms behavior ([6d400c7](https://github.com/resola-ai/deca-apps/commit/6d400c7a17dc4a59914858e16f5e2f2d6e05668b))
* **crm:** update to show Form objects ([a89d193](https://github.com/resola-ai/deca-apps/commit/a89d193640bc86d77d9834ee5a4456faafeb072e))


### [0.2.1](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.1) (2024-09-24)


### Features

* **crm:** CRM - 254 - Update new custom profile ([cbb14e1](https://github.com/resola-ai/deca-apps/commit/cbb14e1732890284191495325a2c26b92f99d3ed))
* **crm:** crm - 259 - remove setting page ([d3bfbee](https://github.com/resola-ai/deca-apps/commit/d3bfbee9e7dbd1e47a5379d02013c4aef855f84e))
* **crm:** CRM - 259 - Remove setting page ([87b191a](https://github.com/resola-ai/deca-apps/commit/87b191a8823c62aceb764f5ffc17fad80234f764))
* **crm:** CRM - 282 - fix some UI table ([a85b5e0](https://github.com/resola-ai/deca-apps/commit/a85b5e085974a738e2a9dd7c0c2af7eb8c47038d))
* **crm:** crm - 282 - icon color ([63e5236](https://github.com/resola-ai/deca-apps/commit/63e5236b750579581fa293144caf4eed90a3c17c))
* **crm:** crm - 285 - show tooltip when deleting the last object ([68723c8](https://github.com/resola-ai/deca-apps/commit/68723c816d9af45b76f25b1daab349dfb04cde1e))
* **crm:** CRM - 288 - Add Email & SMS to create object ([e682f8d](https://github.com/resola-ai/deca-apps/commit/e682f8de224e0abdbc030545a81e8b1c71620716))
* **crm:** CRM - 288 - Show hide email and message ([fbcbcbb](https://github.com/resola-ai/deca-apps/commit/fbcbcbbaa648f4aadf57fb78a848020165238e47))
* **crm:** CRM - 289 - Update primary for phone and email ([51d75b4](https://github.com/resola-ai/deca-apps/commit/51d75b46a525512244e0b6ff683fc88344a7851d))
* **crm:** crm - cmt out options crm ([dc45a8e](https://github.com/resola-ai/deca-apps/commit/dc45a8e02966d08b672f69445af1e47fd94ff37c))
* **crm:** CRM - improve expand box card ([c7b5a6e](https://github.com/resola-ai/deca-apps/commit/c7b5a6e00c7514230e221d907609e8d6391aa0b0))
* **crm:** CRM - Improve navigation ([49d4cc3](https://github.com/resola-ai/deca-apps/commit/49d4cc3d1a479d8fef9c7bbab8e8fba8124f6cb0))
* **crm:** CRM - improve relationship reloading ([836c218](https://github.com/resola-ai/deca-apps/commit/836c2185b6a1b39def9326f60dd82ba902c5fdfa))
* **crm:** fetch template from cdn, update activity UI ([8bbb284](https://github.com/resola-ai/deca-apps/commit/8bbb284f7239ad84ace26e227046c90f73ea4974))
* **crm:** Fix build failed ([121e9e9](https://github.com/resola-ai/deca-apps/commit/121e9e978f653b504a65ec3990ce4f3d72994a93))
* **crm:** Fix modal create is flickered ([b37f0e4](https://github.com/resola-ai/deca-apps/commit/b37f0e43b6be97ebb3b0f9d5da517edc36710710))
* **crm:** Improve dragging ([3b699d3](https://github.com/resola-ai/deca-apps/commit/3b699d3f011f92af2b309a69418349617e90dc1a))
* **crm:** improve link to record mutate objects ([ba96b2d](https://github.com/resola-ai/deca-apps/commit/ba96b2dfb7dd9d326227fcedcbe9d04546fce544))
* **crm:** introduce template for activities tab ([977b2be](https://github.com/resola-ai/deca-apps/commit/977b2be96a19c7e41f193c4a73819df794df20c0))
* **crm:** minor UI updates ([dc2fde9](https://github.com/resola-ai/deca-apps/commit/dc2fde9cd3c09c0448f56a6d08f340f0d94dcb7f))
* **crm:** online consultation template for CRM ([4fd6075](https://github.com/resola-ai/deca-apps/commit/4fd60758772576ea9993d7548691e2a8a50e1666))
* **crm:** online consultation template for CRM ([7f19181](https://github.com/resola-ai/deca-apps/commit/7f191814e3e20052374bdbf323d7c618769ca52a))
* **crm:** sync develop ([1de1b35](https://github.com/resola-ai/deca-apps/commit/1de1b35b85657f5921e6e67e5248fc70bdd3e9ce))
* **crm:** update changelog for crm v0.2.0 ([09fcbbd](https://github.com/resola-ai/deca-apps/commit/09fcbbd46539408f525f125cea29ea587c4d7803))
* **crm:** update Forms to use default render, update activities template ([b5ebb90](https://github.com/resola-ai/deca-apps/commit/b5ebb907536f5c139809658bd0b6ab100a1cf8e2))
* **crm:** update Forms to use default render, update activities template ([6dfcd3b](https://github.com/resola-ai/deca-apps/commit/6dfcd3bbd286e9e235ef98aa528d432d70e29f31))
* **crm:** Update modal text ([c58d0ff](https://github.com/resola-ai/deca-apps/commit/c58d0ffef0b91b6d7c08e2a526ddb513c09fdf07))

### Bug Fixes

* **crm:** clean up template code, minor UI fixes ([72eb616](https://github.com/resola-ai/deca-apps/commit/72eb6162ee5adf9254f9c92c1a37729de2515eb6))
* **crm:** Fix - 268 - fix select value ([f3a4220](https://github.com/resola-ai/deca-apps/commit/f3a422040386ab768e562ea4050a680708bc15d2))
* **crm:** fix loader behavior ([0f31afc](https://github.com/resola-ai/deca-apps/commit/0f31afc10dcd755ae675e0848eaa43e07ba9bbb8))
* **crm:** fix re-render on textSearch ([0d2c006](https://github.com/resola-ai/deca-apps/commit/0d2c006762ac15abdbda21c04aae6adea3e797ec))
* **crm:** minor UI fixes ([f8684da](https://github.com/resola-ai/deca-apps/commit/f8684da8f8350f71615a462d7d6a58589b855899))
* **crm:** update cdn variable ([8f449d5](https://github.com/resola-ai/deca-apps/commit/8f449d515c438ef52d03483a9bce4336db1a026f))
* **crm:** update cdn variable ([050902e](https://github.com/resola-ai/deca-apps/commit/050902e2199e9f236f829bd7078fd6be84af4190))

## [0.2.0](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.2.0) (2024-09-06)


### Features

* **crm:** add ja text ([1cb7548](https://github.com/resola-ai/deca-apps/commit/1cb7548f0a9c1d722abef256c686bb2734ff145a))
* **crm:** CRM - 184 - improve sidebar ([d7b7087](https://github.com/resola-ai/deca-apps/commit/d7b708702c62a16dc6e8d0b2fc5174c161c53261))
* **crm:** CRM - 184 - Update Customer Profile ([e349b16](https://github.com/resola-ai/deca-apps/commit/e349b1678391d653e79b34706cfcba40d152333d))
* **crm:** CRM - 227 - Update column resizing ([e8a026c](https://github.com/resola-ai/deca-apps/commit/e8a026c4d9a36484990b0245bfce245d72d86f96))
* **crm:** CRM - 227 - update column size ([695d6b3](https://github.com/resola-ai/deca-apps/commit/695d6b3592d9bf6bfda179e31254e40bab62b296))
* **crm:** CRM - 229 - Add UI form content ([13d0818](https://github.com/resola-ai/deca-apps/commit/13d0818574d718372bae390cd15f90f752e5b3c6))
* **crm:** CRM - 239 - Setting page General ([4dcb141](https://github.com/resola-ai/deca-apps/commit/4dcb14104afb698b401c8a4ba3ac591c71314679))
* **crm:** CRM - 261 - Move to first option column improvement ([f775c88](https://github.com/resola-ai/deca-apps/commit/f775c883d3844b92325f2fb30999100e1805fc5e))
* **crm:** CRM - 262 - Percent column improvement ([5f9984a](https://github.com/resola-ai/deca-apps/commit/5f9984a5a983d5b519ef98d6268589505ea23aaf))
* **crm:** CRM simple search ([b837de1](https://github.com/resola-ai/deca-apps/commit/b837de10e93390321f5b9908fac74d201d7f4836))
* **crm:** crm- 238 - app context improvement ([9a94879](https://github.com/resola-ai/deca-apps/commit/9a9487952941754bff42bdb7b7be76f65dbbc9ea))
* **crm:** CRM-224 implement DecaSearchBox component ([9d513e6](https://github.com/resola-ai/deca-apps/commit/9d513e60f3eedfe545c93f7c12941cf88fb02a08))
* **crm:** CRM-224 use DecaSearchBox component ([146f179](https://github.com/resola-ai/deca-apps/commit/146f179cbc43379fc9b553cfa8038aa4713d3600))
* **crm:** CRM-234 add DecaSwitch error styles ([2440155](https://github.com/resola-ai/deca-apps/commit/2440155a76bce4f21a55f8819e369903c052e48f))
* **crm:** CRM-234 create DecaSwitch component ([d76eccb](https://github.com/resola-ai/deca-apps/commit/d76eccb3e4bc778af230c8ed5db9fa559198743a))
* **crm:** CRM-234 update DecaSwitch styles ([9b0105e](https://github.com/resola-ai/deca-apps/commit/9b0105edef061c54cc410a0c34988f89881b60ac))
* **crm:** CRM-234 update DecaSwitch styles ([ec809a0](https://github.com/resola-ai/deca-apps/commit/ec809a08e4de551c910de1f87b3f7d440273f0b0))
* **crm:** CRM-235 create Checkbox component ([5309a33](https://github.com/resola-ai/deca-apps/commit/5309a33b1d3e56dde9e1851c65ff1114d111519d))
* **crm:** CRM-235 update Checkbox error styles ([df5b804](https://github.com/resola-ai/deca-apps/commit/df5b804c24b896f052fe63808e460ffab8e7a5c7))
* **crm:** CRM-240 create DecaRadio component ([a7cc931](https://github.com/resola-ai/deca-apps/commit/a7cc931bbdb1b1650c1460904e60a8ae6b075576))
* **crm:** CRM-246 add translations for table filter operators ([757fcf4](https://github.com/resola-ai/deca-apps/commit/757fcf46e799fa5ce3587d584084ba2814e2839f))
* **crm:** handle delete multiple rows ([e5c21f1](https://github.com/resola-ai/deca-apps/commit/e5c21f13755c3d25e672b92d8cdf31e28211b3fa))
* **crm:** improve loading state, fix bug when add/delete column ([8da1f2f](https://github.com/resola-ai/deca-apps/commit/8da1f2fc098267918b8532ed8a1e62c4e7a5b8b0))
* **crm:** Improve relation ship record ([35f8efe](https://github.com/resola-ai/deca-apps/commit/35f8efef9aadeab395ee9f3f9412859a3c68dffe))
* **crm:** profile should opened by accessing the url ([e09a761](https://github.com/resola-ai/deca-apps/commit/e09a761b279315f92a1d37641c17bae06a038da2))
* **crm:** replace form Mantine components with design system components ([8c75624](https://github.com/resola-ai/deca-apps/commit/8c75624a9fe087eb7a554e3cf3af347665e71c8a))
* **crm:** save textSearch to view, add more JA text ([02d9cbc](https://github.com/resola-ai/deca-apps/commit/02d9cbc5becc775d1b74eb1534b7c457a0ba0d08))
* **crm:** update changelog for v0.1.2 ([bee5ef4](https://github.com/resola-ai/deca-apps/commit/bee5ef4c0d16098a1a32e642c86ae7c6b5dee37e))
* **crm:** update CRM JA text ([432d604](https://github.com/resola-ai/deca-apps/commit/432d604c83e0a5fdbc944841cb31bbd4c758a60f))
* **crm:** update dependency ([e71888d](https://github.com/resola-ai/deca-apps/commit/e71888dfadcf17b62431380e37a2cc3b30696bd9))
* **crm:** update useData params ([ed192b2](https://github.com/resola-ai/deca-apps/commit/ed192b274d0e445b24e7fd56f05c3d44e61c3ab3))

### Bug Fixes

* **crm:** crm - 252 - fix cannot select date ([2cf91e2](https://github.com/resola-ai/deca-apps/commit/2cf91e2500cc1539400784f23db0e22c3b217c08))
* **crm:** CRM - 253 - Fix url route ([68a4bc5](https://github.com/resola-ai/deca-apps/commit/68a4bc500574455533c50e8a7b6f941abc6bcbab))
* **crm:** crm - 260 - fix missing format ([0ae01ba](https://github.com/resola-ai/deca-apps/commit/0ae01bac51eec81c2c4b5d4328bec286a04b42ed))
* **crm:** CRM - 260 - Fix missing format currency in detail ([76e147a](https://github.com/resola-ai/deca-apps/commit/76e147a60dbafedaa6a35ad374507b1f48c24f22))
* **crm:** CRM - 266 - Fix wrong format long date ([f6d0e13](https://github.com/resola-ai/deca-apps/commit/f6d0e1340d2797a2e2b3c97d655cdbfd3a5d807f))
* **crm:** CRM - 268 - Fix show error required field link to record ([2898321](https://github.com/resola-ai/deca-apps/commit/2898321d7a41c694897a86f6e21348c947b9e870))
* **crm:** CRM - 279 - Fix long text cannot saved ([1b72164](https://github.com/resola-ai/deca-apps/commit/1b721645a22709485b18503772c85f5740ea8d1f))
* **crm:** CRM-202 - Update Japanese language ([#2260](https://github.com/resola-ai/deca-apps/issues/2260)) ([c3a2182](https://github.com/resola-ai/deca-apps/commit/c3a2182a369455299d13a74d55107e238fa9e2ae))
* **crm:** CRM-202 - Update language and profile history ([#2494](https://github.com/resola-ai/deca-apps/issues/2494)) ([11f2f7c](https://github.com/resola-ai/deca-apps/commit/11f2f7c3426f5b705306d64c100e8598b40e0c08))
* **crm:** CRM-205 - Upadte display history for Percent field type ([#2386](https://github.com/resola-ai/deca-apps/issues/2386)) ([7770c27](https://github.com/resola-ai/deca-apps/commit/7770c276858587e11d606914155b3d20d44dcb3b))
* **crm:** CRM-221 - Add file upload, deletion record to History ([#2285](https://github.com/resola-ai/deca-apps/issues/2285)) ([a137bbb](https://github.com/resola-ai/deca-apps/commit/a137bbb2c2528f7ba4b82d3ba64267adbc2179f1))
* **crm:** CRM-234 duplicated key ([c977d64](https://github.com/resola-ai/deca-apps/commit/c977d640e0ee5710e3f3e0d6cc43dad5480ac289))
* **crm:** Fix - 269 - Fix update object not reflect immediately ([cde07de](https://github.com/resola-ai/deca-apps/commit/cde07deccb0d8b84ea05814f9e2cdc9b62ca5d00))
* **crm:** Fix - 270 - Fix missing format currency ([3e10026](https://github.com/resola-ai/deca-apps/commit/3e1002680f5bd075b51f27f79fda05fdc6acdc90))
* **crm:** Fix-236-Add default value when adding new row ([b521be1](https://github.com/resola-ai/deca-apps/commit/b521be10e47cc54ab2cc8ed2134007a0cb43f820))


### [0.1.2](https://github.com/resola-ai/deca-apps/compare/<EMAIL>@0.1.2) (2024-08-14)


### Features

* **crm:** correct dnd fields order on customview ([bf60131](https://github.com/resola-ai/deca-apps/commit/bf601311304af078c61d57bb703d4c9eb74c8cbd))
* **crm:** crm - 191 - improve link to record field ([fc37474](https://github.com/resola-ai/deca-apps/commit/fc374746aed2dde64eac68d8cce8888199560132))
* **crm:** CRM-208 add DecaToggle component placeholder ([54f26f8](https://github.com/resola-ai/deca-apps/commit/54f26f8bfc91ce627b2b6ec989ed779c41eab3aa))
* **crm:** CRM-212 implement proper language detection ([bd2bdf3](https://github.com/resola-ai/deca-apps/commit/bd2bdf3a9ef3b675e41c0344b625a7db138150b0))
* **crm:** minor UI bug fixes ([3f8ec57](https://github.com/resola-ai/deca-apps/commit/3f8ec574b87f4fc3eb7f677408624e1fb86c52e9))
* **crm:** sync develop ([6cc75aa](https://github.com/resola-ai/deca-apps/commit/6cc75aa509b456c142c0675217a24e0edc05a812))
* **crm:** UI improvements, make custom columns render properly ([9a0f7f4](https://github.com/resola-ai/deca-apps/commit/9a0f7f499c078a0919eb85e3b42f1fb24c0f647c))
* **crm:** UI improvements, make custom columns render properly ([533f190](https://github.com/resola-ai/deca-apps/commit/533f19019690a6649bebe9b9986c9374bb74b209))

### Bug Fixes

* **crm:** add Automatic Sorting option for table sort ([3e0289b](https://github.com/resola-ai/deca-apps/commit/3e0289be48d9fdd21dfe2d455ae6f84730b1be21))
* **crm:** CRM-208 add JP translation ([d70fb2e](https://github.com/resola-ai/deca-apps/commit/d70fb2ef30c1a610ff646a17b84c24b7385275b9))
* **crm:** CRM-208 exclude settings in table view type ([b946f7e](https://github.com/resola-ai/deca-apps/commit/b946f7e916c273b8632edce3117d65e25b37a182))
* **crm:** CRM-208 fix auto sorting behaviour ([432ae01](https://github.com/resola-ai/deca-apps/commit/432ae01731ec68f7fd79d45ef1e612a0d2660b33))
* **crm:** CRM-211 handle table field visibility change ([23fdaae](https://github.com/resola-ai/deca-apps/commit/23fdaaebac12395bd382d61f4c1e84b6e88b63a7))
* **crm:** CRM-211 rename column toggle handler ([a363c9b](https://github.com/resola-ai/deca-apps/commit/a363c9b5eaf08de7e4cbdcff13fd9b843e9a68cd))
* **crm:** CRM-211 typescript error ([8529108](https://github.com/resola-ai/deca-apps/commit/8529108a7d516591fae9d05218ac9e61abb89bcf))
* **crm:** Fix clear data ([08fdb5f](https://github.com/resola-ai/deca-apps/commit/08fdb5f22e1e6e7866ccd5f79570e2324643c58a))
* **crm:** Fix-161-Fix clearing data ([5cb04c1](https://github.com/resola-ai/deca-apps/commit/5cb04c1d43a9a2cf8736a0c4b4e0af3b98c39d60))
* **crm:** Fix-222-wrong auto value ([8b7c6d1](https://github.com/resola-ai/deca-apps/commit/8b7c6d171d7c677a6e8025f61e9adb5a4113aaf1))
* **crm:** render break ([7973501](https://github.com/resola-ai/deca-apps/commit/797350146d7dd8557b2cb0864c26a7cee7d2f221))

### 0.1.1 (2024-08-05)


### Features

* **crm:** [CRM-111] add common components ([4688d2a](https://github.com/resola-ai/deca-apps/commit/4688d2a25108c26a0e5be919f5975045b75b3d79))
* **crm:** [CRM-122] implement DecaDataValue component ([47442f0](https://github.com/resola-ai/deca-apps/commit/47442f0f1ebe3a7d62f408177ea1bf7732fa2954))
* **crm:** [CRM-122] update styles ([c4a88a6](https://github.com/resola-ai/deca-apps/commit/c4a88a6a7cf9460eae3f1ec147081261e369bbf6))
* **crm:** [CRM-123] integrate table filtering/sorting with context menu ([deb718e](https://github.com/resola-ai/deca-apps/commit/deb718e6f3c65c101cb6008dffd3f476453899e6))
* **crm:** [CRM-123] update table filter options generator ([1639441](https://github.com/resola-ai/deca-apps/commit/163944116d23da4a073288f6848d2a9a9091fc12))
* **crm:** [CRM-123] update table types ([905096f](https://github.com/resola-ai/deca-apps/commit/905096f49f6b4da3d037c9bb923a9e2d24cfdabd))
* **crm:** [TABLES-56] integrate table filter with data ([b4fa155](https://github.com/resola-ai/deca-apps/commit/b4fa15519b342cda9c81a3d94b86345068616fc9))
* **crm:** [TABLES-57] integrate table sort with data ([0292fd5](https://github.com/resola-ai/deca-apps/commit/0292fd584d2ad579c1e4b29973f9d43cf040816c))
* **crm:** add bug fixes to changelog ([8d4aff7](https://github.com/resola-ai/deca-apps/commit/8d4aff79f775944381da5ce7642eeb241143c18c))
* **crm:** add CRM changelog ([a787db7](https://github.com/resola-ai/deca-apps/commit/a787db7067fc593d3909f95d35f84dac5f06ac32))
* **crm:** add CRM release stuffs ([85cbd8a](https://github.com/resola-ai/deca-apps/commit/85cbd8aae773aba5ec7c36d07b2c67edadad6d62))
* **crm:** Add CustomFields, MenuView, SelectView components ([#1527](https://github.com/resola-ai/deca-apps/issues/1527)) ([7570860](https://github.com/resola-ai/deca-apps/commit/757086078a2f61a8d42d6f4fe67f97aa99d09f7e))
* **crm:** Add header menu context, continue improve table ([82bd215](https://github.com/resola-ai/deca-apps/commit/82bd2156872985a18fcd180e99ef8a01f3bfc402))
* **crm:** add indicator for filter and sort ([b53371e](https://github.com/resola-ai/deca-apps/commit/b53371e016552d71ab70bcfe0935cba9e6eab8c6))
* **crm:** add JA text to CRM and table ([2d5fc0c](https://github.com/resola-ai/deca-apps/commit/2d5fc0c10b378144dd198844c580843f70263e7e))
* **crm:** add loader to make sure data is loaded ([#1755](https://github.com/resola-ai/deca-apps/issues/1755)) ([34ea3fa](https://github.com/resola-ai/deca-apps/commit/34ea3fae3543d3be949c80e4656562e5dc0bd19d))
* **crm:** add QueryBuilder to table toolbar ([faa42f1](https://github.com/resola-ai/deca-apps/commit/faa42f191c13230c2f0c3cc11fd9a110778deb7e))
* **crm:** Add RowHeight, improve table ([d096ed1](https://github.com/resola-ai/deca-apps/commit/d096ed154ffbce7da9ed07613ffd824e30bc3d95))
* **crm:** Add single select form ([08f1981](https://github.com/resola-ai/deca-apps/commit/08f198187525b8e60b35cb849cf724d9b4efb004))
* **crm:** add TableFilter component ([58105c7](https://github.com/resola-ai/deca-apps/commit/58105c77abcdf7d194d0ebe39af004cda8a090a0))
* **crm:** add TableSort to table toolbar ([806ad3b](https://github.com/resola-ai/deca-apps/commit/806ad3b1734b85d1565797b16af27426b9722ef1))
* **crm:** add tag/status component, update table ([a03ae70](https://github.com/resola-ai/deca-apps/commit/a03ae70eb53157622dae718e66ce428b6430e198))
* **crm:** add translation for no column message ([1ce1bd8](https://github.com/resola-ai/deca-apps/commit/1ce1bd820628b3ecefbbb4f6bed5f10e8f1a5824))
* **crm:** Add vite builder back ([14f1b67](https://github.com/resola-ai/deca-apps/commit/14f1b67ec605bcd268206eb1d6a50a3020f939db))
* **crm:** Base Advance Search UI ([#186](https://github.com/resola-ai/deca-apps/issues/186)) ([e1cccba](https://github.com/resola-ai/deca-apps/commit/e1cccbacec73a4b23279d55762a6e3d1a88b1d48))
* **crm:** Centralize routes for tab navigation ([#194](https://github.com/resola-ai/deca-apps/issues/194)) ([4d1d977](https://github.com/resola-ai/deca-apps/commit/4d1d9770e13586cc4225b084b398644da36bd64c))
* **crm:** check undefined on multiselect ([b004a5a](https://github.com/resola-ai/deca-apps/commit/b004a5aaf5de113eb0ca1ae591e9281cc3cb9f31))
* **crm:** crm - 100 - update multi select field type ([a210f9c](https://github.com/resola-ai/deca-apps/commit/a210f9cb6944e72ca6f812cde712327c77fa3c68))
* **crm:** crm - 101 -167 - Image FieldType and TextSettings ([c987507](https://github.com/resola-ai/deca-apps/commit/c987507c0d1cc7a612273f348972e3931f838341))
* **crm:** crm - 116 -117 - update and delete column ([cd2fa8f](https://github.com/resola-ai/deca-apps/commit/cd2fa8fe12b297a51f17530a50ab88322b9fdfb4))
* **crm:** crm - 121 - improve cell section and update row menu ([9c6b550](https://github.com/resola-ai/deca-apps/commit/9c6b5509ab2818551fd3fc76dc81788317b2fa5b))
* **crm:** CRM - 124 -125 - add field type relationship ([ca3331c](https://github.com/resola-ai/deca-apps/commit/ca3331cf876047e210ae16399a48d3caaee98a27))
* **crm:** crm - 134 - Add Edit Cell MultiSelect ([c8c55f2](https://github.com/resola-ai/deca-apps/commit/c8c55f2277c985c3f3934737668361a8c8f9da9c))
* **crm:** crm - 134 - update multiple select ([d7480c5](https://github.com/resola-ai/deca-apps/commit/d7480c5e834c48ef766baf19a68e0be4d84684ce))
* **crm:** crm - 138 - update new record type relationship ([061e2fd](https://github.com/resola-ai/deca-apps/commit/061e2fd0ec5840151d5088ee0f3fda1f5287a104))
* **crm:** crm - 138 - update post data of relationship ([ebbbb79](https://github.com/resola-ai/deca-apps/commit/ebbbb7922811915ae51234c84222e2cfae8b3df8))
* **crm:** CRM - 151 - add params detail profile ([6dd0c1d](https://github.com/resola-ai/deca-apps/commit/6dd0c1da86371848d6f43f15d6ce1d653d3d657c))
* **crm:** crm - 163 -164 - update created and modified time Field Type ([ec7edf3](https://github.com/resola-ai/deca-apps/commit/ec7edf3443ac4971f870fb82f1905d8ec6c30858))
* **crm:** crm - 165 - phone number field type ([48e8c38](https://github.com/resola-ai/deca-apps/commit/48e8c38aaf8308103ebe44379f5a07b98d85aed2))
* **crm:** crm - 167 - update field type text ([885d2c8](https://github.com/resola-ai/deca-apps/commit/885d2c808b1ee86a69a30a6d18188851a86f2c8b))
* **crm:** crm - 167 - update primary text ([e49eb00](https://github.com/resola-ai/deca-apps/commit/e49eb00cf34dd9e6d37ca202e8a1117bf3a17f15))
* **crm:** crm - 170 - 171 add created by and modified by type ([43dc491](https://github.com/resola-ai/deca-apps/commit/43dc4910d7e143545a3739ec24b16e897adff2ba))
* **crm:** CRM - 185 - add order drag column ([4411286](https://github.com/resola-ai/deca-apps/commit/44112863cef31b650fdbeb8c1f306c2fcb8761e8))
* **crm:** crm - 191 - improve link to record field ([1828a51](https://github.com/resola-ai/deca-apps/commit/1828a519285ce61eaae536fe53f5511116a3ae64))
* **crm:** crm - 71 - Update sidebar ([651ae01](https://github.com/resola-ai/deca-apps/commit/651ae0198367c03ebfca56cf04467e9332008ab5))
* **crm:** CRM - 73 - Add form creating new Field ([2764010](https://github.com/resola-ai/deca-apps/commit/2764010e6d55332c8b947a6e1ee087adba4fdbdc))
* **crm:** crm - 80 - Add empty state ([e06683e](https://github.com/resola-ai/deca-apps/commit/e06683ec7a727ca60a069773610e6917338ebaa8))
* **crm:** CRM - 81 - 82 -83: Fix table row column created ([1d83760](https://github.com/resola-ai/deca-apps/commit/1d83760b6319872ab5cfa901bc7776f1c7a0f3c4))
* **crm:** CRM - 86 - Add hightlight cell ([9f4bc2d](https://github.com/resola-ai/deca-apps/commit/9f4bc2da61e3c5e0a6d67a936e2e61cd12b6f283))
* **crm:** CRM - 91 - update row with type Single Select ([2f89699](https://github.com/resola-ai/deca-apps/commit/2f896991e1da69854be90bf9a16e69cc6611d6b4))
* **crm:** CRM - 93 - 94 - Update new column with type url email ([ab16015](https://github.com/resola-ai/deca-apps/commit/ab16015f4e3b61ea3a6932a4d77a60ae1e04e30e))
* **crm:** crm - 95-96 - add new column with type number ([d0976f9](https://github.com/resola-ai/deca-apps/commit/d0976f9115fa8b49791e0cfd65bbf05b73d89558))
* **crm:** crm - 98 - update field type with currency ([0efa1f2](https://github.com/resola-ai/deca-apps/commit/0efa1f294ff6becb6ecaecc4d38289aa6c75c621))
* **crm:** crm - 99 - add percentage field type ([f016cf9](https://github.com/resola-ai/deca-apps/commit/f016cf94a8dfbd188c7f72144a3dd29e298991c6))
* **crm:** crm - mock data with new config columns ([e40a44f](https://github.com/resola-ai/deca-apps/commit/e40a44f976dfe82ca8c30fb45be0a34e6d3326f0))
* **crm:** crm - remove a row ([bb21593](https://github.com/resola-ai/deca-apps/commit/bb215936cdf1dad3ed9b44afc7bfe13ccefb864f))
* **crm:** crm - setup edit form field ([85fb28f](https://github.com/resola-ai/deca-apps/commit/85fb28f2c26a046ce62ddeb557b489b0414ca93e))
* **crm:** CRM - update table empty state ([9bb0a64](https://github.com/resola-ai/deca-apps/commit/9bb0a646e71259f2bd0395d50afc3579b9c490cf))
* **crm:** crm -103 - 104 - setup api and integrate with objects ([44fd0c3](https://github.com/resola-ai/deca-apps/commit/44fd0c375bb2f3bd5a9bf1baebdef32910085459))
* **crm:** crm -107 - profile detail layout ([30deec5](https://github.com/resola-ai/deca-apps/commit/30deec599c54852e158b68676b69c9e425475844))
* **crm:** crm -114 - update form edit field ([17cca4e](https://github.com/resola-ai/deca-apps/commit/17cca4e37422e1aa4fc0b7b100f910725fde2b93))
* **crm:** crm -146 -tags integration ([03b3d88](https://github.com/resola-ai/deca-apps/commit/03b3d88d81eba93b8453fc08059153548e94659c))
* **crm:** crm -183 - Fix delete column issues ([0dcedee](https://github.com/resola-ai/deca-apps/commit/0dcedee93c99e1d87fe818f8d0e77b8033d3dca5))
* **crm:** crm-106-integrate records with api ([556bfe1](https://github.com/resola-ai/deca-apps/commit/556bfe16e8221d77f6142f8fdb238e12e0d9e5f7))
* **crm:** CRM-111 add Identity common components ([e45257c](https://github.com/resola-ai/deca-apps/commit/e45257c7fc6d008335873b2d6d1c182cf3fcae02))
* **crm:** CRM-111 implement Profile Identities UI ([b603302](https://github.com/resola-ai/deca-apps/commit/b6033021f11262073fd91e6050eba0ece7e641a1))
* **crm:** CRM-112 implement Files UI for Profile ([0dbc0d7](https://github.com/resola-ai/deca-apps/commit/0dbc0d763ba2df906ab9014714de2c5e5d86ddab))
* **crm:** CRM-119 - [UI] [Profile] Customer Profile + Activities + History ([#1791](https://github.com/resola-ai/deca-apps/issues/1791)) ([264e344](https://github.com/resola-ai/deca-apps/commit/264e344916ae37730dd684a2d99fb7f620952277))
* **crm:** crm-126- update logo crm ([9b48269](https://github.com/resola-ai/deca-apps/commit/9b48269a4b9a84a3bb85b7dafc1b19b74331c982))
* **crm:** CRM-131 export DecaFileUpload from components dir ([fa54ab0](https://github.com/resola-ai/deca-apps/commit/fa54ab04a50b838e6fe6442f488bd39fc1c2659c))
* **crm:** CRM-131 implement FileUpload component ([090c85e](https://github.com/resola-ai/deca-apps/commit/090c85ec6b9ee43780e49c010a018ac4ac95dc9d))
* **crm:** CRM-132 implement ProgressBar component ([381f448](https://github.com/resola-ai/deca-apps/commit/381f448b14e2080248240ef5b39d761bee7e7f1b))
* **crm:** CRM-147 - [Profile] Integrate API on Activities ([#1886](https://github.com/resola-ai/deca-apps/issues/1886)) ([40d5188](https://github.com/resola-ai/deca-apps/commit/40d518820dd011f2fb7e752f35b11bbe0cbc459e))
* **crm:** CRM-148 integrate API on Identities ([5abac3e](https://github.com/resola-ai/deca-apps/commit/5abac3e959f0b742c46a5a2d7e047b615d62ee2f))
* **crm:** CRM-177 add required URL params checks ([95bc1be](https://github.com/resola-ai/deca-apps/commit/95bc1bed7c78b92b1f226995a2bc877081b14232))
* **crm:** CRM-177 allow only one file selected at a time ([0100e15](https://github.com/resola-ai/deca-apps/commit/0100e15c3a4dc71c1eb09bc380aff07d63904f0c))
* **crm:** CRM-177 integrate file upload with API ([a38a7ad](https://github.com/resola-ai/deca-apps/commit/a38a7ad453702b28d709f2c0100d88a2ce8064c9))
* **crm:** CRM-178 integrate file list with API ([6da6448](https://github.com/resola-ai/deca-apps/commit/6da64481e0adce7c41977785a3ea4591f9624a9f))
* **crm:** CRM-195 use DecaStatus for Identity ([1b29d3c](https://github.com/resola-ai/deca-apps/commit/1b29d3c92326915818aa4d7c407304a0bb72be12))
* **crm:** CRM-32 PR Preview ([75844c3](https://github.com/resola-ai/deca-apps/commit/75844c386038c0aa2896fb724f34a5d55a195c79))
* **crm:** CRM-32 PR Preview ([dd35e46](https://github.com/resola-ai/deca-apps/commit/dd35e46b2e41dfc36b01f17310374699e4aa1a79))
* **crm:** CRM-32 PR Preview ([51ba0f4](https://github.com/resola-ai/deca-apps/commit/51ba0f44fa2bf5c19e042d232ca77679edd394df))
* **crm:** crm-71 - Update sidebar menu ([90713a5](https://github.com/resola-ai/deca-apps/commit/90713a55aaad91b47f67e9d255869303fb185f25))
* **crm:** CRM-84 add context menu to table, improve table ([07d5b77](https://github.com/resola-ai/deca-apps/commit/07d5b77d40ef19dd3f9f6d5e32daf16ca61b5ac8))
* **crm:** crm-92- add new column with field type datetime ([fbbb6e3](https://github.com/resola-ai/deca-apps/commit/fbbb6e316b417a4d74b021d008e485336b3117c4))
* **crm:** Deca table component ([2c93147](https://github.com/resola-ai/deca-apps/commit/2c93147ec1f947882257267d2275423db92648d0))
* **crm:** Deca table component ([3047598](https://github.com/resola-ai/deca-apps/commit/3047598852f13e1d1495a0610fce1c146cefda4c))
* **crm:** drag and drog columns for custom fields ([2138cb8](https://github.com/resola-ai/deca-apps/commit/2138cb8ddaaefdffbd851644e9ea675867f42fe0))
* **crm:** Enable crm navigation hub header ([96c33d2](https://github.com/resola-ai/deca-apps/commit/96c33d2a06f86ae87b9227c6e189cc82dc3a1832))
* **crm:** fix build failed ([9e73806](https://github.com/resola-ai/deca-apps/commit/9e73806802094e6ac512a39aa8642e3eb3a47eb8))
* **crm:** Fix build failed ([2a0cdd4](https://github.com/resola-ai/deca-apps/commit/2a0cdd4bf784cf26990904de424a2e2d7235bbbd))
* **crm:** Fix css cell table ([5af3837](https://github.com/resola-ai/deca-apps/commit/5af38378b96901c83a97b3b1c8768b588743dd72))
* **crm:** fix import ([f96a9a1](https://github.com/resola-ai/deca-apps/commit/f96a9a1dc80ebede9f9eda86a06ed8af69f35f03))
* **crm:** fix layout sidebar break and fix row selection table ([f8cbeeb](https://github.com/resola-ai/deca-apps/commit/f8cbeebd9882d21bc1c984f8fcfe37429645125f))
* **crm:** Fix missing condition ([421f42e](https://github.com/resola-ai/deca-apps/commit/421f42e6720a6e62e19db72e77abaacc9ded1135))
* **crm:** Fix navigation customer profile ([0c91ade](https://github.com/resola-ai/deca-apps/commit/0c91ade88ccb4411adff9d76e36b9414b0f05378))
* **crm:** fix package-lock ([946b5df](https://github.com/resola-ai/deca-apps/commit/946b5df26662ce7071d26bf2084ecd56dc567216))
* **crm:** fix render types on profile page ([4a6160f](https://github.com/resola-ai/deca-apps/commit/4a6160fd9f090edaec7df7a07af36dc30a1f6bbf))
* **crm:** Fix storybook to run properly ([db21856](https://github.com/resola-ai/deca-apps/commit/db21856aaf624a857c983f4134d87a95e253bece))
* **crm:** Fix undefined value ([7beb7fc](https://github.com/resola-ai/deca-apps/commit/7beb7fc06acd0213c9ff4f285461ddbe471f17c1))
* **crm:** fully integrate table view with API ([cf7a6e2](https://github.com/resola-ai/deca-apps/commit/cf7a6e212e50a4d00e2d02bc0607d3662d666aeb))
* **crm:** hide table filter if no column has been added ([29aef61](https://github.com/resola-ai/deca-apps/commit/29aef61df098236e20d24267433f1ff85e942332))
* **crm:** hightlight filter columns, show records found ([1f4c6bd](https://github.com/resola-ai/deca-apps/commit/1f4c6bd46662c1eeff257f517f2fa651cba9e143))
* **crm:** Improve header context, minor types fixing ([8dfa1a8](https://github.com/resola-ai/deca-apps/commit/8dfa1a836cff395ebcbcb59880c14a80d78ba861))
* **crm:** improve scrolling custom profile ([48ab2ed](https://github.com/resola-ai/deca-apps/commit/48ab2ed9d099edd1cfd8ce8dba17d739580749a5))
* **crm:** improve table state, add condition for drag column ([69d1490](https://github.com/resola-ai/deca-apps/commit/69d1490730cd1eeac830327b3e4d1ab7ae43b30a))
* **crm:** Integrate API tags ([3422ae9](https://github.com/resola-ai/deca-apps/commit/3422ae9859c0839972cf2c96ad1dfd82688401d9))
* **crm:** integrate API to profile detail ([b4d6c16](https://github.com/resola-ai/deca-apps/commit/b4d6c167f1b2e217fcf1c6eaf846b36341da0e25))
* **crm:** Integrate Customized Fields with Table Data ([#1562](https://github.com/resola-ai/deca-apps/issues/1562)) ([1c3f54b](https://github.com/resola-ai/deca-apps/commit/1c3f54bb0d6be85fe48dc15110d7fd09e6c13140))
* **crm:** integrate View to API ([ecad793](https://github.com/resola-ai/deca-apps/commit/ecad793153bf365e2466762a306e64fd689afda7))
* **crm:** Move functionality to workspace context and update field types ([0747faf](https://github.com/resola-ai/deca-apps/commit/0747fafd5a8615527a3be251290b7b77d7d287fb))
* **crm:** mutate records on filter/sort ([362baa3](https://github.com/resola-ai/deca-apps/commit/362baa3f59614f6c5c66625754ec8ff739c3a4dc))
* **crm:** refactor table ([abbb46b](https://github.com/resola-ai/deca-apps/commit/abbb46bf673f4602c2cba59b4b8797f71853194a))
* **crm:** Refactor Tabs and Logo ([#261](https://github.com/resola-ai/deca-apps/issues/261)) ([5402f27](https://github.com/resola-ai/deca-apps/commit/5402f27fec87acf931ace417fad7bd16ae9f3381))
* **crm:** refetch records after updating view ([afb33ba](https://github.com/resola-ai/deca-apps/commit/afb33ba93d44d7ba3f609b8053993a0df78e1f0d))
* **crm:** Remove redundant code ([413849f](https://github.com/resola-ai/deca-apps/commit/413849f56a4b01ce39d6e43dff5dcb0b991689c9))
* **crm:** Remove unused ([9eaf4df](https://github.com/resola-ai/deca-apps/commit/9eaf4dfc1852d1c59bd1e9be7bd72f4312935c2e))
* **crm:** remove unused codes ([473da55](https://github.com/resola-ai/deca-apps/commit/473da55c3476856c64a9d802afe80652c9c05d64))
* **crm:** Remove unused codes ([ad87664](https://github.com/resola-ai/deca-apps/commit/ad87664fb70060b535fb3fb6c945579611f113f4))
* **crm:** remove unused components, fix table keyboard navigation ([8c7690f](https://github.com/resola-ai/deca-apps/commit/8c7690f54a311b1816956297bb8a3ba21e4627a7))
* **crm:** render more types on profile detail ([e39d333](https://github.com/resola-ai/deca-apps/commit/e39d333ebabc0df8e2a91a12c720d07ae25b8133))
* **crm:** resolve conflict ([ff9f142](https://github.com/resola-ai/deca-apps/commit/ff9f142b590b524a8b6a4ee115ddb87f6218bde6))
* **crm:** resolve conflict ([4a77d38](https://github.com/resola-ai/deca-apps/commit/4a77d38153e4991a0020ff24b767dd978fe2befa))
* **crm:** Set up Header and Tabs UI ([#125](https://github.com/resola-ai/deca-apps/issues/125)) ([604a34e](https://github.com/resola-ai/deca-apps/commit/604a34ecd5dca08d1154751cb4a169e867ba04f0))
* **crm:** sync branch develop ([2a3ab75](https://github.com/resola-ai/deca-apps/commit/2a3ab75dda8a200f14d27ed97d88c13ed4433244))
* **crm:** sync dev ([357f5a5](https://github.com/resola-ai/deca-apps/commit/357f5a5502cb22bb02123f0a68b2d16653b2ebbc))
* **crm:** sync develop ([6a6fa9c](https://github.com/resola-ai/deca-apps/commit/6a6fa9ce0f475166cb5a237b23f03b51e8e3c2e9))
* **crm:** sync develop ([10506e7](https://github.com/resola-ai/deca-apps/commit/10506e7369265991023218e708a6fd3dec3fa21a))
* **crm:** sync develop ([e493dc2](https://github.com/resola-ai/deca-apps/commit/e493dc288cb608dc8ac7385d043399ec0de199dd))
* **crm:** sync develop ([5b01e90](https://github.com/resola-ai/deca-apps/commit/5b01e9011e651f93c840918ad71cee210af470ce))
* **crm:** sync develop ([ed69e82](https://github.com/resola-ai/deca-apps/commit/ed69e8255466c436853b879d07cf1fafe9670102))
* **crm:** sync develop ([1f45087](https://github.com/resola-ai/deca-apps/commit/1f45087fa8b7c816ed216582bc87fdb45aefbc4d))
* **crm:** sync develop ([8363a3f](https://github.com/resola-ai/deca-apps/commit/8363a3fddcb0d57865995b494e186f0ba7655f4c))
* **crm:** sync develop ([0debf46](https://github.com/resola-ai/deca-apps/commit/0debf465dc80ca74c40c81d283d31056bf19e747))
* **crm:** table filtering and sorting using API ([29fae97](https://github.com/resola-ai/deca-apps/commit/29fae97290afc1797cd049c4bfa600231f1901f3))
* **crm:** TABLES-59 - Integrate Views, View detail ([#1581](https://github.com/resola-ai/deca-apps/issues/1581)) ([1538411](https://github.com/resola-ai/deca-apps/commit/1538411bd5905a13c1f1ebeef74b8816df24d565))
* **crm:** TABLES-60 - Integrate Views Detail (Rename, Duplicate, Clear, Delete) with Table Data ([#1592](https://github.com/resola-ai/deca-apps/issues/1592)) ([137de7f](https://github.com/resola-ai/deca-apps/commit/137de7f6b5e712027eb44c3caca8fc6fa42886df))
* **crm:** Tags section for Profile ([#1712](https://github.com/resola-ai/deca-apps/issues/1712)) ([695fd9f](https://github.com/resola-ai/deca-apps/commit/695fd9fbbcf32b7d225108385517a222f6695328))
* **crm:** UI bug fixes on toolbar and table cell ([0bbe8cc](https://github.com/resola-ai/deca-apps/commit/0bbe8cce4f0795dc7c4b0b7ccdd34fd639d01754))
* **crm:** Update add new col and row table ([0e2bc49](https://github.com/resola-ai/deca-apps/commit/0e2bc49f32092d1e97be50d65601a1ad8a32c5a0))
* **crm:** update api url ([3e462c5](https://github.com/resola-ai/deca-apps/commit/3e462c569f25599ef31f4848bcee60e99816debe))
* **crm:** Update context profile and fix small warning ([bd45cbc](https://github.com/resola-ai/deca-apps/commit/bd45cbc6614b9c27e7ca6810da708caa3ea57951))
* **crm:** update duplicate object and record update ([cb9dac6](https://github.com/resola-ai/deca-apps/commit/cb9dac68ea08b74cd5519bb25bba43041e774c06))
* **crm:** update fields ([b46368f](https://github.com/resola-ai/deca-apps/commit/b46368f6c2004697118fa4b6b48357745b0471b8))
* **crm:** update fields by fieldOrder ([6741669](https://github.com/resola-ai/deca-apps/commit/674166969e5306c0ac2b52ced1fff6f6a9a7812e))
* **crm:** update Icons, split code ([fc311a6](https://github.com/resola-ai/deca-apps/commit/fc311a6aa29e38afde98891f26c3ae1a96d364f7))
* **crm:** Update layout ([33b039d](https://github.com/resola-ai/deca-apps/commit/33b039d0e752c78d6c4422d5b9dafbfaf7fe1809))
* **crm:** update menu sidebar ([13464f2](https://github.com/resola-ai/deca-apps/commit/13464f24f50e3e91c7e27693b9c1bead9a0448cd))
* **crm:** Update missing icon ([7615630](https://github.com/resola-ai/deca-apps/commit/7615630e17efa3f7aec0571d38684c928bdb2dea))
* **crm:** Update package and navigation ([5b8f178](https://github.com/resola-ai/deca-apps/commit/5b8f1789817883cb707c5945187d32b47cd61f88))
* **crm:** update primary field ([d22ee0c](https://github.com/resola-ai/deca-apps/commit/d22ee0c834294d0aed68d724b757934a9164d1c1))
* **crm:** update primary field, minor improvements ([8c035da](https://github.com/resola-ai/deca-apps/commit/8c035dabcff13c6638cb30ae2d71f9571d3d9ddf))
* **crm:** update select behavior ([1a49ae5](https://github.com/resola-ai/deca-apps/commit/1a49ae5a8fc67e39b1ec3a058b15effe05c7a8ad))
* **crm:** update table context, refactor logic based on API structure ([1aef1da](https://github.com/resola-ai/deca-apps/commit/1aef1dad4b1ef7d553043d4eaeadc543c22ec34f))
* **crm:** update table filter to show correct states, improve profile layout ([8594f9b](https://github.com/resola-ai/deca-apps/commit/8594f9b34ed83ea65bb1ca4844a3b6e05bf33fc6))
* **crm:** Update table style ([91b65c1](https://github.com/resola-ai/deca-apps/commit/91b65c1e6217a394f1c6a346c05006f5d2a9e04f))
* **crm:** update type name ([6190285](https://github.com/resola-ai/deca-apps/commit/6190285114e30db2f909950610385d7cc191d4a7))
* **crm:** update using gap ([67024f4](https://github.com/resola-ai/deca-apps/commit/67024f48601da80d91562b8d4d082e1012876d19))
* **crm:** update Widget table ([1b410b6](https://github.com/resola-ai/deca-apps/commit/1b410b613067c1320cf1a0f6ecc92f76b130a219))
* **crm:** update workspace ([8fb9000](https://github.com/resola-ai/deca-apps/commit/8fb90006afce0f05796a122b52e02c71a034d68e))
* **crm:** update ws text ([a3e8d5b](https://github.com/resola-ai/deca-apps/commit/a3e8d5bbfed5ca6d59a70d9963d2c3b4c526e0c3))
* **crm:** Upgrade to SB v8 ([2f4a623](https://github.com/resola-ai/deca-apps/commit/2f4a623d1ca93f159d905363f5f622365e0ee2d3))
* **crm:** use column id ([bb9de6d](https://github.com/resola-ai/deca-apps/commit/bb9de6d79aa6e985a995d184f682ca1a8ff3567c))

### Bug Fixes

* **crm:** CB-1386 update i18n version ([3d06581](https://github.com/resola-ai/deca-apps/commit/3d06581e7ea28834c354aacb40d630c0507767eb))
* **crm:** Comment out 404 page ([f729b54](https://github.com/resola-ai/deca-apps/commit/f729b546430f6520bbffd253f932b66f529012cc))
* **crm:** comment unused ([d792370](https://github.com/resola-ai/deca-apps/commit/d792370d2886e4634cf2616a3d5f3defd5a155c2))
* **crm:** CRM - 159 - add insert option ([6d26554](https://github.com/resola-ai/deca-apps/commit/6d265545cf0c0b669b848476a0f35107eb2f6a65))
* **crm:** crm - 181 - Fix value undefined ([4ac15ad](https://github.com/resola-ai/deca-apps/commit/4ac15ad69a03e33a319ee4d4ae844c7f2e678550))
* **crm:** crm - 192 - fix select type ([55f9200](https://github.com/resola-ai/deca-apps/commit/55f920092b075a07e57ea4e9e25dbff7d0251e82))
* **crm:** CRM - 194 - Fix percentage value ([5aab231](https://github.com/resola-ai/deca-apps/commit/5aab231545c0c9fc889b0c4ad2adebc35701af52))
* **crm:** crm - 198 - Fix default value for type select ([9563a34](https://github.com/resola-ai/deca-apps/commit/9563a34be734fb1dc96eaa53fc416115fbcd79ee))
* **crm:** crm - 199 - change defaultValue to value ([060c168](https://github.com/resola-ai/deca-apps/commit/060c168fa93667ce106473ccda28a7c46128e30e))
* **crm:** CRM - 203: Update new record ([eeb3362](https://github.com/resola-ai/deca-apps/commit/eeb3362ec687e9fa4acd925f9626f77a5e2c9b24))
* **crm:** CRM - 204 - fix wrong text ([6a5fa20](https://github.com/resola-ai/deca-apps/commit/6a5fa2003c273ae8a513db79ba51a3e56b33fa74))
* **crm:** CRM-137 add No Result Found UI for table filter ([8e29eca](https://github.com/resola-ai/deca-apps/commit/8e29eca510fd33a519f9eda6b6b44cd8d8592513))
* **crm:** CRM-137 add Noto Sans JP font ([13f4942](https://github.com/resola-ai/deca-apps/commit/13f49424ee2285f267a77a6aa7c8c8b4b1ba0d03))
* **crm:** CRM-137 restrict empty image size to 240px ([52beecd](https://github.com/resola-ai/deca-apps/commit/52beecd47234b86e71aacbe3ac98c113dc371350))
* **crm:** CRM-139 install react-dom ([5f92544](https://github.com/resola-ai/deca-apps/commit/5f92544599d7487135f5398be6945b6db1050e46))
* **crm:** CRM-139 move filter result below table head ([3fe21c2](https://github.com/resola-ai/deca-apps/commit/3fe21c252d1c090e01163d6fd5531b32139a7cb9))
* **crm:** CRM-141 hide nested Add Group button ([144fb76](https://github.com/resola-ai/deca-apps/commit/144fb763c9f7dcbb0180d0ca87dc8f4ce6d37459))
* **crm:** CRM-141 only show icon for add rule button inside a rule group ([5507cf3](https://github.com/resola-ai/deca-apps/commit/5507cf30ef045c7ae6055c21681c659853c70c84))
* **crm:** CRM-150 - [UI] [Profile] Integrate API on History ([#2000](https://github.com/resola-ai/deca-apps/issues/2000)) ([09ed4f3](https://github.com/resola-ai/deca-apps/commit/09ed4f37e054b9ee83c81b0bb7aff12bb0ba2844))
* **crm:** CRM-150 - Update display history value ([#2162](https://github.com/resola-ai/deca-apps/issues/2162)) ([2c196c3](https://github.com/resola-ai/deca-apps/commit/2c196c3eb71968f8e9b875dfb23ea586a132a0a4))
* **crm:** CRM-175 - [UI] [Profile] Handle the topbar button actions ([#1984](https://github.com/resola-ai/deca-apps/issues/1984)) ([d8ce4a9](https://github.com/resola-ai/deca-apps/commit/d8ce4a9e28e55bfc1bd75143830a39b29f0c7415))
* **crm:** CRM-182 - Improve Sidebar height ([#2026](https://github.com/resola-ai/deca-apps/issues/2026)) ([d57c145](https://github.com/resola-ai/deca-apps/commit/d57c145e31994d0d9450dd1ce47affe3b13e38ea))
* **crm:** CRM-187 cleanup translations ([a9cb3fd](https://github.com/resola-ai/deca-apps/commit/a9cb3fdd98ca48416ab110e9755919db1d45636e))
* **crm:** CRM-187 fix Identity badges ([a4f0e23](https://github.com/resola-ai/deca-apps/commit/a4f0e23d3ca12d62801bb50abd188070eb93ef18))
* **crm:** CRM-193 - Missing Descriptions and Texts in Create field are not in correct format ([#2132](https://github.com/resola-ai/deca-apps/issues/2132)) ([525fef6](https://github.com/resola-ai/deca-apps/commit/525fef6fea98b71e5a6229d8a7e13f4f6efe1de8))
* **crm:** customer profile data grouping by date ([6a2cff5](https://github.com/resola-ai/deca-apps/commit/6a2cff59bc98962c8224d003842f2f5c0c9af3f0))
* **crm:** Fix - 162 - Fix wrong hover action ([b4f520a](https://github.com/resola-ai/deca-apps/commit/b4f520a0e144ada0987ea7f7935d32111f857432))
* **crm:** Fix - 199 - Fix missing default value ([a3790d8](https://github.com/resola-ai/deca-apps/commit/a3790d82a886d364af9cce3555b42240f245cef4))
* **crm:** Fix - 203 - Fix type config ([0ce07e7](https://github.com/resola-ai/deca-apps/commit/0ce07e74683c04104e4908883f21441ba9b034b7))
* **crm:** fix build error ([737a337](https://github.com/resola-ai/deca-apps/commit/737a3375c9f30321756511f9e003913ae44e1fa2))
* **crm:** fix build error ([6a4b8fd](https://github.com/resola-ai/deca-apps/commit/6a4b8fd2dbe3c3c39e0ad93e9cb57597b8cc003a))
* **crm:** Fix crash app ([79a537c](https://github.com/resola-ai/deca-apps/commit/79a537c69119bdddc94959b8d11731f9eb365b55))
* **crm:** Fix crash app ([e2b83f8](https://github.com/resola-ai/deca-apps/commit/e2b83f83036383327e5b2e272140339163e713b5))
* **crm:** Fix long text header ([c939dba](https://github.com/resola-ai/deca-apps/commit/c939dbae6072391c756315c98e5e7d440f211ac3))
* **crm:** Fix route ([208f067](https://github.com/resola-ai/deca-apps/commit/208f067b999e320eea23ba8383d88ef6e67d1b0c))
* **crm:** Fix routing issue ([2e4e0a7](https://github.com/resola-ai/deca-apps/commit/2e4e0a74ee0eff3a7234eb72748f1df662c30db8))
* **crm:** Fix size image ([2a641ec](https://github.com/resola-ai/deca-apps/commit/2a641eca4da09e10f48d8fc231992e58f50235e9))
* **crm:** Fix Tabs behaviors ([be6b483](https://github.com/resola-ai/deca-apps/commit/be6b48381bdd047fe0f86489953eef02856ec2e9))
* **crm:** make workshop great again ([603ea1b](https://github.com/resola-ai/deca-apps/commit/603ea1b5921244cbdaa2bc255a25683ce33dfdbe))
* **crm:** missing highlight sidebar ([3ad4df6](https://github.com/resola-ai/deca-apps/commit/3ad4df677406010ffba439c44f158b1674b1fc40))
* **crm:** Move 404 route into /crm ([ecee324](https://github.com/resola-ai/deca-apps/commit/ecee324cc91742f42f4b73623884abd1cdb4d20d))
* **crm:** only show table filter result when data is actually filtered ([4198da1](https://github.com/resola-ai/deca-apps/commit/4198da1b0c78945d7b280663044a3a8caa2bd798))
* **crm:** prevent AddNewColumn from being added to table filter ([b52c5b2](https://github.com/resola-ai/deca-apps/commit/b52c5b258a9772ae84677f42047b477f508d0752))
* **crm:** remove unused variable ([a6a2f3a](https://github.com/resola-ai/deca-apps/commit/a6a2f3a411fb9fafb254fec312892df8529a0dbf))
* **crm:** Should close modal when getting error ([0be024a](https://github.com/resola-ai/deca-apps/commit/0be024aedd2c2a5f203d56257b4c90e89a480533))
* **crm:** table filter count ([5daa9b9](https://github.com/resola-ai/deca-apps/commit/5daa9b9192bbe58cf03ab905a5cffadd7be788a4))
* **crm:** Tabs behavior ([684c092](https://github.com/resola-ai/deca-apps/commit/684c0925d25c8cacfe80d802daf5ffbfdeff6ae5))


## 0.1.0 (2024-08-02)

### Features
* **crm:** CRM FIRST RELEASE
* **crm:** [CRM-111] add common components ([4688d2a](https://github.com/resola-ai/deca-apps/commit/4688d2a25108c26a0e5be919f5975045b75b3d79))
* **crm:** [CRM-122] implement DecaDataValue component ([47442f0](https://github.com/resola-ai/deca-apps/commit/47442f0f1ebe3a7d62f408177ea1bf7732fa2954))
* **crm:** [CRM-122] update styles ([c4a88a6](https://github.com/resola-ai/deca-apps/commit/c4a88a6a7cf9460eae3f1ec147081261e369bbf6))
* **crm:** [CRM-123] integrate table filtering/sorting with context menu ([deb718e](https://github.com/resola-ai/deca-apps/commit/deb718e6f3c65c101cb6008dffd3f476453899e6))
* **crm:** [CRM-123] update table filter options generator ([1639441](https://github.com/resola-ai/deca-apps/commit/163944116d23da4a073288f6848d2a9a9091fc12))
* **crm:** [CRM-123] update table types ([905096f](https://github.com/resola-ai/deca-apps/commit/905096f49f6b4da3d037c9bb923a9e2d24cfdabd))
* **crm:** [TABLES-56] integrate table filter with data ([b4fa155](https://github.com/resola-ai/deca-apps/commit/b4fa15519b342cda9c81a3d94b86345068616fc9))
* **crm:** [TABLES-57] integrate table sort with data ([0292fd5](https://github.com/resola-ai/deca-apps/commit/0292fd584d2ad579c1e4b29973f9d43cf040816c))
* **crm:** add CRM release stuffs ([85cbd8a](https://github.com/resola-ai/deca-apps/commit/85cbd8aae773aba5ec7c36d07b2c67edadad6d62))
* **crm:** Add CustomFields, MenuView, SelectView components ([#1527](https://github.com/resola-ai/deca-apps/issues/1527)) ([7570860](https://github.com/resola-ai/deca-apps/commit/757086078a2f61a8d42d6f4fe67f97aa99d09f7e))
* **crm:** Add header menu context, continue improve table ([82bd215](https://github.com/resola-ai/deca-apps/commit/82bd2156872985a18fcd180e99ef8a01f3bfc402))
* **crm:** add indicator for filter and sort ([b53371e](https://github.com/resola-ai/deca-apps/commit/b53371e016552d71ab70bcfe0935cba9e6eab8c6))
* **crm:** add JA text to CRM and table ([2d5fc0c](https://github.com/resola-ai/deca-apps/commit/2d5fc0c10b378144dd198844c580843f70263e7e))
* **crm:** add loader to make sure data is loaded ([#1755](https://github.com/resola-ai/deca-apps/issues/1755)) ([34ea3fa](https://github.com/resola-ai/deca-apps/commit/34ea3fae3543d3be949c80e4656562e5dc0bd19d))
* **crm:** add QueryBuilder to table toolbar ([faa42f1](https://github.com/resola-ai/deca-apps/commit/faa42f191c13230c2f0c3cc11fd9a110778deb7e))
* **crm:** Add RowHeight, improve table ([d096ed1](https://github.com/resola-ai/deca-apps/commit/d096ed154ffbce7da9ed07613ffd824e30bc3d95))
* **crm:** Add single select form ([08f1981](https://github.com/resola-ai/deca-apps/commit/08f198187525b8e60b35cb849cf724d9b4efb004))
* **crm:** add TableFilter component ([58105c7](https://github.com/resola-ai/deca-apps/commit/58105c77abcdf7d194d0ebe39af004cda8a090a0))
* **crm:** add TableSort to table toolbar ([806ad3b](https://github.com/resola-ai/deca-apps/commit/806ad3b1734b85d1565797b16af27426b9722ef1))
* **crm:** add tag/status component, update table ([a03ae70](https://github.com/resola-ai/deca-apps/commit/a03ae70eb53157622dae718e66ce428b6430e198))
* **crm:** add translation for no column message ([1ce1bd8](https://github.com/resola-ai/deca-apps/commit/1ce1bd820628b3ecefbbb4f6bed5f10e8f1a5824))
* **crm:** Add vite builder back ([14f1b67](https://github.com/resola-ai/deca-apps/commit/14f1b67ec605bcd268206eb1d6a50a3020f939db))
* **crm:** Base Advance Search UI ([#186](https://github.com/resola-ai/deca-apps/issues/186)) ([e1cccba](https://github.com/resola-ai/deca-apps/commit/e1cccbacec73a4b23279d55762a6e3d1a88b1d48))
* **crm:** Centralize routes for tab navigation ([#194](https://github.com/resola-ai/deca-apps/issues/194)) ([4d1d977](https://github.com/resola-ai/deca-apps/commit/4d1d9770e13586cc4225b084b398644da36bd64c))
* **crm:** check undefined on multiselect ([b004a5a](https://github.com/resola-ai/deca-apps/commit/b004a5aaf5de113eb0ca1ae591e9281cc3cb9f31))
* **crm:** crm - 100 - update multi select field type ([a210f9c](https://github.com/resola-ai/deca-apps/commit/a210f9cb6944e72ca6f812cde712327c77fa3c68))
* **crm:** crm - 101 -167 - Image FieldType and TextSettings ([c987507](https://github.com/resola-ai/deca-apps/commit/c987507c0d1cc7a612273f348972e3931f838341))
* **crm:** crm - 116 -117 - update and delete column ([cd2fa8f](https://github.com/resola-ai/deca-apps/commit/cd2fa8fe12b297a51f17530a50ab88322b9fdfb4))
* **crm:** crm - 121 - improve cell section and update row menu ([9c6b550](https://github.com/resola-ai/deca-apps/commit/9c6b5509ab2818551fd3fc76dc81788317b2fa5b))
* **crm:** CRM - 124 -125 - add field type relationship ([ca3331c](https://github.com/resola-ai/deca-apps/commit/ca3331cf876047e210ae16399a48d3caaee98a27))
* **crm:** crm - 134 - Add Edit Cell MultiSelect ([c8c55f2](https://github.com/resola-ai/deca-apps/commit/c8c55f2277c985c3f3934737668361a8c8f9da9c))
* **crm:** crm - 134 - update multiple select ([d7480c5](https://github.com/resola-ai/deca-apps/commit/d7480c5e834c48ef766baf19a68e0be4d84684ce))
* **crm:** crm - 138 - update new record type relationship ([061e2fd](https://github.com/resola-ai/deca-apps/commit/061e2fd0ec5840151d5088ee0f3fda1f5287a104))
* **crm:** crm - 138 - update post data of relationship ([ebbbb79](https://github.com/resola-ai/deca-apps/commit/ebbbb7922811915ae51234c84222e2cfae8b3df8))
* **crm:** CRM - 151 - add params detail profile ([6dd0c1d](https://github.com/resola-ai/deca-apps/commit/6dd0c1da86371848d6f43f15d6ce1d653d3d657c))
* **crm:** crm - 163 -164 - update created and modified time Field Type ([ec7edf3](https://github.com/resola-ai/deca-apps/commit/ec7edf3443ac4971f870fb82f1905d8ec6c30858))
* **crm:** crm - 165 - phone number field type ([48e8c38](https://github.com/resola-ai/deca-apps/commit/48e8c38aaf8308103ebe44379f5a07b98d85aed2))
* **crm:** crm - 167 - update field type text ([885d2c8](https://github.com/resola-ai/deca-apps/commit/885d2c808b1ee86a69a30a6d18188851a86f2c8b))
* **crm:** crm - 167 - update primary text ([e49eb00](https://github.com/resola-ai/deca-apps/commit/e49eb00cf34dd9e6d37ca202e8a1117bf3a17f15))
* **crm:** crm - 170 - 171 add created by and modified by type ([43dc491](https://github.com/resola-ai/deca-apps/commit/43dc4910d7e143545a3739ec24b16e897adff2ba))
* **crm:** CRM - 185 - add order drag column ([4411286](https://github.com/resola-ai/deca-apps/commit/44112863cef31b650fdbeb8c1f306c2fcb8761e8))
* **crm:** crm - 191 - improve link to record field ([1828a51](https://github.com/resola-ai/deca-apps/commit/1828a519285ce61eaae536fe53f5511116a3ae64))
* **crm:** crm - 71 - Update sidebar ([651ae01](https://github.com/resola-ai/deca-apps/commit/651ae0198367c03ebfca56cf04467e9332008ab5))
* **crm:** CRM - 73 - Add form creating new Field ([2764010](https://github.com/resola-ai/deca-apps/commit/2764010e6d55332c8b947a6e1ee087adba4fdbdc))
* **crm:** crm - 80 - Add empty state ([e06683e](https://github.com/resola-ai/deca-apps/commit/e06683ec7a727ca60a069773610e6917338ebaa8))
* **crm:** CRM - 81 - 82 -83: Fix table row column created ([1d83760](https://github.com/resola-ai/deca-apps/commit/1d83760b6319872ab5cfa901bc7776f1c7a0f3c4))
* **crm:** CRM - 86 - Add hightlight cell ([9f4bc2d](https://github.com/resola-ai/deca-apps/commit/9f4bc2da61e3c5e0a6d67a936e2e61cd12b6f283))
* **crm:** CRM - 91 - update row with type Single Select ([2f89699](https://github.com/resola-ai/deca-apps/commit/2f896991e1da69854be90bf9a16e69cc6611d6b4))
* **crm:** CRM - 93 - 94 - Update new column with type url email ([ab16015](https://github.com/resola-ai/deca-apps/commit/ab16015f4e3b61ea3a6932a4d77a60ae1e04e30e))
* **crm:** crm - 95-96 - add new column with type number ([d0976f9](https://github.com/resola-ai/deca-apps/commit/d0976f9115fa8b49791e0cfd65bbf05b73d89558))
* **crm:** crm - 98 - update field type with currency ([0efa1f2](https://github.com/resola-ai/deca-apps/commit/0efa1f294ff6becb6ecaecc4d38289aa6c75c621))
* **crm:** crm - 99 - add percentage field type ([f016cf9](https://github.com/resola-ai/deca-apps/commit/f016cf94a8dfbd188c7f72144a3dd29e298991c6))
* **crm:** crm - mock data with new config columns ([e40a44f](https://github.com/resola-ai/deca-apps/commit/e40a44f976dfe82ca8c30fb45be0a34e6d3326f0))
* **crm:** crm - remove a row ([bb21593](https://github.com/resola-ai/deca-apps/commit/bb215936cdf1dad3ed9b44afc7bfe13ccefb864f))
* **crm:** crm - setup edit form field ([85fb28f](https://github.com/resola-ai/deca-apps/commit/85fb28f2c26a046ce62ddeb557b489b0414ca93e))
* **crm:** CRM - update table empty state ([9bb0a64](https://github.com/resola-ai/deca-apps/commit/9bb0a646e71259f2bd0395d50afc3579b9c490cf))
* **crm:** crm -103 - 104 - setup api and integrate with objects ([44fd0c3](https://github.com/resola-ai/deca-apps/commit/44fd0c375bb2f3bd5a9bf1baebdef32910085459))
* **crm:** crm -107 - profile detail layout ([30deec5](https://github.com/resola-ai/deca-apps/commit/30deec599c54852e158b68676b69c9e425475844))
* **crm:** crm -114 - update form edit field ([17cca4e](https://github.com/resola-ai/deca-apps/commit/17cca4e37422e1aa4fc0b7b100f910725fde2b93))
* **crm:** crm -146 -tags integration ([03b3d88](https://github.com/resola-ai/deca-apps/commit/03b3d88d81eba93b8453fc08059153548e94659c))
* **crm:** crm -183 - Fix delete column issues ([0dcedee](https://github.com/resola-ai/deca-apps/commit/0dcedee93c99e1d87fe818f8d0e77b8033d3dca5))
* **crm:** crm-106-integrate records with api ([556bfe1](https://github.com/resola-ai/deca-apps/commit/556bfe16e8221d77f6142f8fdb238e12e0d9e5f7))
* **crm:** CRM-111 add Identity common components ([e45257c](https://github.com/resola-ai/deca-apps/commit/e45257c7fc6d008335873b2d6d1c182cf3fcae02))
* **crm:** CRM-111 implement Profile Identities UI ([b603302](https://github.com/resola-ai/deca-apps/commit/b6033021f11262073fd91e6050eba0ece7e641a1))
* **crm:** CRM-112 implement Files UI for Profile ([0dbc0d7](https://github.com/resola-ai/deca-apps/commit/0dbc0d763ba2df906ab9014714de2c5e5d86ddab))
* **crm:** CRM-119 - [UI] [Profile] Customer Profile + Activities + History ([#1791](https://github.com/resola-ai/deca-apps/issues/1791)) ([264e344](https://github.com/resola-ai/deca-apps/commit/264e344916ae37730dd684a2d99fb7f620952277))
* **crm:** crm-126- update logo crm ([9b48269](https://github.com/resola-ai/deca-apps/commit/9b48269a4b9a84a3bb85b7dafc1b19b74331c982))
* **crm:** CRM-131 export DecaFileUpload from components dir ([fa54ab0](https://github.com/resola-ai/deca-apps/commit/fa54ab04a50b838e6fe6442f488bd39fc1c2659c))
* **crm:** CRM-131 implement FileUpload component ([090c85e](https://github.com/resola-ai/deca-apps/commit/090c85ec6b9ee43780e49c010a018ac4ac95dc9d))
* **crm:** CRM-132 implement ProgressBar component ([381f448](https://github.com/resola-ai/deca-apps/commit/381f448b14e2080248240ef5b39d761bee7e7f1b))
* **crm:** CRM-147 - [Profile] Integrate API on Activities ([#1886](https://github.com/resola-ai/deca-apps/issues/1886)) ([40d5188](https://github.com/resola-ai/deca-apps/commit/40d518820dd011f2fb7e752f35b11bbe0cbc459e))
* **crm:** CRM-148 integrate API on Identities ([5abac3e](https://github.com/resola-ai/deca-apps/commit/5abac3e959f0b742c46a5a2d7e047b615d62ee2f))
* **crm:** CRM-177 add required URL params checks ([95bc1be](https://github.com/resola-ai/deca-apps/commit/95bc1bed7c78b92b1f226995a2bc877081b14232))
* **crm:** CRM-177 allow only one file selected at a time ([0100e15](https://github.com/resola-ai/deca-apps/commit/0100e15c3a4dc71c1eb09bc380aff07d63904f0c))
* **crm:** CRM-177 integrate file upload with API ([a38a7ad](https://github.com/resola-ai/deca-apps/commit/a38a7ad453702b28d709f2c0100d88a2ce8064c9))
* **crm:** CRM-178 integrate file list with API ([6da6448](https://github.com/resola-ai/deca-apps/commit/6da64481e0adce7c41977785a3ea4591f9624a9f))
* **crm:** CRM-195 use DecaStatus for Identity ([1b29d3c](https://github.com/resola-ai/deca-apps/commit/1b29d3c92326915818aa4d7c407304a0bb72be12))
* **crm:** CRM-32 PR Preview ([75844c3](https://github.com/resola-ai/deca-apps/commit/75844c386038c0aa2896fb724f34a5d55a195c79))
* **crm:** CRM-32 PR Preview ([dd35e46](https://github.com/resola-ai/deca-apps/commit/dd35e46b2e41dfc36b01f17310374699e4aa1a79))
* **crm:** CRM-32 PR Preview ([51ba0f4](https://github.com/resola-ai/deca-apps/commit/51ba0f44fa2bf5c19e042d232ca77679edd394df))
* **crm:** crm-71 - Update sidebar menu ([90713a5](https://github.com/resola-ai/deca-apps/commit/90713a55aaad91b47f67e9d255869303fb185f25))
* **crm:** CRM-84 add context menu to table, improve table ([07d5b77](https://github.com/resola-ai/deca-apps/commit/07d5b77d40ef19dd3f9f6d5e32daf16ca61b5ac8))
* **crm:** crm-92- add new column with field type datetime ([fbbb6e3](https://github.com/resola-ai/deca-apps/commit/fbbb6e316b417a4d74b021d008e485336b3117c4))
* **crm:** Deca table component ([2c93147](https://github.com/resola-ai/deca-apps/commit/2c93147ec1f947882257267d2275423db92648d0))
* **crm:** Deca table component ([3047598](https://github.com/resola-ai/deca-apps/commit/3047598852f13e1d1495a0610fce1c146cefda4c))
* **crm:** drag and drog columns for custom fields ([2138cb8](https://github.com/resola-ai/deca-apps/commit/2138cb8ddaaefdffbd851644e9ea675867f42fe0))
* **crm:** Fix build failed ([2a0cdd4](https://github.com/resola-ai/deca-apps/commit/2a0cdd4bf784cf26990904de424a2e2d7235bbbd))
* **crm:** Fix css cell table ([5af3837](https://github.com/resola-ai/deca-apps/commit/5af38378b96901c83a97b3b1c8768b588743dd72))
* **crm:** fix import ([f96a9a1](https://github.com/resola-ai/deca-apps/commit/f96a9a1dc80ebede9f9eda86a06ed8af69f35f03))
* **crm:** fix layout sidebar break and fix row selection table ([f8cbeeb](https://github.com/resola-ai/deca-apps/commit/f8cbeebd9882d21bc1c984f8fcfe37429645125f))
* **crm:** Fix missing condition ([421f42e](https://github.com/resola-ai/deca-apps/commit/421f42e6720a6e62e19db72e77abaacc9ded1135))
* **crm:** Fix navigation customer profile ([0c91ade](https://github.com/resola-ai/deca-apps/commit/0c91ade88ccb4411adff9d76e36b9414b0f05378))
* **crm:** fix package-lock ([946b5df](https://github.com/resola-ai/deca-apps/commit/946b5df26662ce7071d26bf2084ecd56dc567216))
* **crm:** fix render types on profile page ([4a6160f](https://github.com/resola-ai/deca-apps/commit/4a6160fd9f090edaec7df7a07af36dc30a1f6bbf))
* **crm:** Fix storybook to run properly ([db21856](https://github.com/resola-ai/deca-apps/commit/db21856aaf624a857c983f4134d87a95e253bece))
* **crm:** Fix undefined value ([7beb7fc](https://github.com/resola-ai/deca-apps/commit/7beb7fc06acd0213c9ff4f285461ddbe471f17c1))
* **crm:** fully integrate table view with API ([cf7a6e2](https://github.com/resola-ai/deca-apps/commit/cf7a6e212e50a4d00e2d02bc0607d3662d666aeb))
* **crm:** hide table filter if no column has been added ([29aef61](https://github.com/resola-ai/deca-apps/commit/29aef61df098236e20d24267433f1ff85e942332))
* **crm:** hightlight filter columns, show records found ([1f4c6bd](https://github.com/resola-ai/deca-apps/commit/1f4c6bd46662c1eeff257f517f2fa651cba9e143))
* **crm:** Improve header context, minor types fixing ([8dfa1a8](https://github.com/resola-ai/deca-apps/commit/8dfa1a836cff395ebcbcb59880c14a80d78ba861))
* **crm:** improve scrolling custom profile ([48ab2ed](https://github.com/resola-ai/deca-apps/commit/48ab2ed9d099edd1cfd8ce8dba17d739580749a5))
* **crm:** improve table state, add condition for drag column ([69d1490](https://github.com/resola-ai/deca-apps/commit/69d1490730cd1eeac830327b3e4d1ab7ae43b30a))
* **crm:** Integrate API tags ([3422ae9](https://github.com/resola-ai/deca-apps/commit/3422ae9859c0839972cf2c96ad1dfd82688401d9))
* **crm:** integrate API to profile detail ([b4d6c16](https://github.com/resola-ai/deca-apps/commit/b4d6c167f1b2e217fcf1c6eaf846b36341da0e25))
* **crm:** Integrate Customized Fields with Table Data ([#1562](https://github.com/resola-ai/deca-apps/issues/1562)) ([1c3f54b](https://github.com/resola-ai/deca-apps/commit/1c3f54bb0d6be85fe48dc15110d7fd09e6c13140))
* **crm:** integrate View to API ([ecad793](https://github.com/resola-ai/deca-apps/commit/ecad793153bf365e2466762a306e64fd689afda7))
* **crm:** Move functionality to workspace context and update field types ([0747faf](https://github.com/resola-ai/deca-apps/commit/0747fafd5a8615527a3be251290b7b77d7d287fb))
* **crm:** mutate records on filter/sort ([362baa3](https://github.com/resola-ai/deca-apps/commit/362baa3f59614f6c5c66625754ec8ff739c3a4dc))
* **crm:** refactor table ([abbb46b](https://github.com/resola-ai/deca-apps/commit/abbb46bf673f4602c2cba59b4b8797f71853194a))
* **crm:** Refactor Tabs and Logo ([#261](https://github.com/resola-ai/deca-apps/issues/261)) ([5402f27](https://github.com/resola-ai/deca-apps/commit/5402f27fec87acf931ace417fad7bd16ae9f3381))
* **crm:** refetch records after updating view ([afb33ba](https://github.com/resola-ai/deca-apps/commit/afb33ba93d44d7ba3f609b8053993a0df78e1f0d))
* **crm:** Remove redundant code ([413849f](https://github.com/resola-ai/deca-apps/commit/413849f56a4b01ce39d6e43dff5dcb0b991689c9))
* **crm:** Remove unused ([9eaf4df](https://github.com/resola-ai/deca-apps/commit/9eaf4dfc1852d1c59bd1e9be7bd72f4312935c2e))
* **crm:** remove unused codes ([473da55](https://github.com/resola-ai/deca-apps/commit/473da55c3476856c64a9d802afe80652c9c05d64))
* **crm:** Remove unused codes ([ad87664](https://github.com/resola-ai/deca-apps/commit/ad87664fb70060b535fb3fb6c945579611f113f4))
* **crm:** remove unused components, fix table keyboard navigation ([8c7690f](https://github.com/resola-ai/deca-apps/commit/8c7690f54a311b1816956297bb8a3ba21e4627a7))
* **crm:** resolve conflict ([ff9f142](https://github.com/resola-ai/deca-apps/commit/ff9f142b590b524a8b6a4ee115ddb87f6218bde6))
* **crm:** resolve conflict ([4a77d38](https://github.com/resola-ai/deca-apps/commit/4a77d38153e4991a0020ff24b767dd978fe2befa))
* **crm:** Set up Header and Tabs UI ([#125](https://github.com/resola-ai/deca-apps/issues/125)) ([604a34e](https://github.com/resola-ai/deca-apps/commit/604a34ecd5dca08d1154751cb4a169e867ba04f0))
* **crm:** sync branch develop ([2a3ab75](https://github.com/resola-ai/deca-apps/commit/2a3ab75dda8a200f14d27ed97d88c13ed4433244))
* **crm:** sync dev ([357f5a5](https://github.com/resola-ai/deca-apps/commit/357f5a5502cb22bb02123f0a68b2d16653b2ebbc))
* **crm:** sync develop ([6a6fa9c](https://github.com/resola-ai/deca-apps/commit/6a6fa9ce0f475166cb5a237b23f03b51e8e3c2e9))
* **crm:** sync develop ([10506e7](https://github.com/resola-ai/deca-apps/commit/10506e7369265991023218e708a6fd3dec3fa21a))
* **crm:** sync develop ([e493dc2](https://github.com/resola-ai/deca-apps/commit/e493dc288cb608dc8ac7385d043399ec0de199dd))
* **crm:** sync develop ([5b01e90](https://github.com/resola-ai/deca-apps/commit/5b01e9011e651f93c840918ad71cee210af470ce))
* **crm:** sync develop ([ed69e82](https://github.com/resola-ai/deca-apps/commit/ed69e8255466c436853b879d07cf1fafe9670102))
* **crm:** sync develop ([1f45087](https://github.com/resola-ai/deca-apps/commit/1f45087fa8b7c816ed216582bc87fdb45aefbc4d))
* **crm:** sync develop ([8363a3f](https://github.com/resola-ai/deca-apps/commit/8363a3fddcb0d57865995b494e186f0ba7655f4c))
* **crm:** sync develop ([0debf46](https://github.com/resola-ai/deca-apps/commit/0debf465dc80ca74c40c81d283d31056bf19e747))
* **crm:** table filtering and sorting using API ([29fae97](https://github.com/resola-ai/deca-apps/commit/29fae97290afc1797cd049c4bfa600231f1901f3))
* **crm:** TABLES-59 - Integrate Views, View detail ([#1581](https://github.com/resola-ai/deca-apps/issues/1581)) ([1538411](https://github.com/resola-ai/deca-apps/commit/1538411bd5905a13c1f1ebeef74b8816df24d565))
* **crm:** TABLES-60 - Integrate Views Detail (Rename, Duplicate, Clear, Delete) with Table Data ([#1592](https://github.com/resola-ai/deca-apps/issues/1592)) ([137de7f](https://github.com/resola-ai/deca-apps/commit/137de7f6b5e712027eb44c3caca8fc6fa42886df))
* **crm:** Tags section for Profile ([#1712](https://github.com/resola-ai/deca-apps/issues/1712)) ([695fd9f](https://github.com/resola-ai/deca-apps/commit/695fd9fbbcf32b7d225108385517a222f6695328))
* **crm:** UI bug fixes on toolbar and table cell ([0bbe8cc](https://github.com/resola-ai/deca-apps/commit/0bbe8cce4f0795dc7c4b0b7ccdd34fd639d01754))
* **crm:** Update add new col and row table ([0e2bc49](https://github.com/resola-ai/deca-apps/commit/0e2bc49f32092d1e97be50d65601a1ad8a32c5a0))
* **crm:** update api url ([3e462c5](https://github.com/resola-ai/deca-apps/commit/3e462c569f25599ef31f4848bcee60e99816debe))
* **crm:** Update context profile and fix small warning ([bd45cbc](https://github.com/resola-ai/deca-apps/commit/bd45cbc6614b9c27e7ca6810da708caa3ea57951))
* **crm:** update duplicate object and record update ([cb9dac6](https://github.com/resola-ai/deca-apps/commit/cb9dac68ea08b74cd5519bb25bba43041e774c06))
* **crm:** update fields ([b46368f](https://github.com/resola-ai/deca-apps/commit/b46368f6c2004697118fa4b6b48357745b0471b8))
* **crm:** update fields by fieldOrder ([6741669](https://github.com/resola-ai/deca-apps/commit/674166969e5306c0ac2b52ced1fff6f6a9a7812e))
* **crm:** update Icons, split code ([fc311a6](https://github.com/resola-ai/deca-apps/commit/fc311a6aa29e38afde98891f26c3ae1a96d364f7))
* **crm:** Update layout ([33b039d](https://github.com/resola-ai/deca-apps/commit/33b039d0e752c78d6c4422d5b9dafbfaf7fe1809))
* **crm:** update menu sidebar ([13464f2](https://github.com/resola-ai/deca-apps/commit/13464f24f50e3e91c7e27693b9c1bead9a0448cd))
* **crm:** Update missing icon ([7615630](https://github.com/resola-ai/deca-apps/commit/7615630e17efa3f7aec0571d38684c928bdb2dea))
* **crm:** Update package and navigation ([5b8f178](https://github.com/resola-ai/deca-apps/commit/5b8f1789817883cb707c5945187d32b47cd61f88))
* **crm:** update primary field ([d22ee0c](https://github.com/resola-ai/deca-apps/commit/d22ee0c834294d0aed68d724b757934a9164d1c1))
* **crm:** update primary field, minor improvements ([8c035da](https://github.com/resola-ai/deca-apps/commit/8c035dabcff13c6638cb30ae2d71f9571d3d9ddf))
* **crm:** update select behavior ([1a49ae5](https://github.com/resola-ai/deca-apps/commit/1a49ae5a8fc67e39b1ec3a058b15effe05c7a8ad))
* **crm:** update table context, refactor logic based on API structure ([1aef1da](https://github.com/resola-ai/deca-apps/commit/1aef1dad4b1ef7d553043d4eaeadc543c22ec34f))
* **crm:** update table filter to show correct states, improve profile layout ([8594f9b](https://github.com/resola-ai/deca-apps/commit/8594f9b34ed83ea65bb1ca4844a3b6e05bf33fc6))
* **crm:** Update table style ([91b65c1](https://github.com/resola-ai/deca-apps/commit/91b65c1e6217a394f1c6a346c05006f5d2a9e04f))
* **crm:** update type name ([6190285](https://github.com/resola-ai/deca-apps/commit/6190285114e30db2f909950610385d7cc191d4a7))
* **crm:** update using gap ([67024f4](https://github.com/resola-ai/deca-apps/commit/67024f48601da80d91562b8d4d082e1012876d19))
* **crm:** update Widget table ([1b410b6](https://github.com/resola-ai/deca-apps/commit/1b410b613067c1320cf1a0f6ecc92f76b130a219))
* **crm:** update workspace ([8fb9000](https://github.com/resola-ai/deca-apps/commit/8fb90006afce0f05796a122b52e02c71a034d68e))
* **crm:** Upgrade to SB v8 ([2f4a623](https://github.com/resola-ai/deca-apps/commit/2f4a623d1ca93f159d905363f5f622365e0ee2d3))
* **crm:** use column id ([bb9de6d](https://github.com/resola-ai/deca-apps/commit/bb9de6d79aa6e985a995d184f682ca1a8ff3567c))

### Bug Fixes

* **crm:** CB-1386 update i18n version ([3d06581](https://github.com/resola-ai/deca-apps/commit/3d06581e7ea28834c354aacb40d630c0507767eb))
* **crm:** Comment out 404 page ([f729b54](https://github.com/resola-ai/deca-apps/commit/f729b546430f6520bbffd253f932b66f529012cc))
* **crm:** comment unused ([d792370](https://github.com/resola-ai/deca-apps/commit/d792370d2886e4634cf2616a3d5f3defd5a155c2))
* **crm:** CRM - 159 - add insert option ([6d26554](https://github.com/resola-ai/deca-apps/commit/6d265545cf0c0b669b848476a0f35107eb2f6a65))
* **crm:** crm - 181 - Fix value undefined ([4ac15ad](https://github.com/resola-ai/deca-apps/commit/4ac15ad69a03e33a319ee4d4ae844c7f2e678550))
* **crm:** crm - 192 - fix select type ([55f9200](https://github.com/resola-ai/deca-apps/commit/55f920092b075a07e57ea4e9e25dbff7d0251e82))
* **crm:** CRM - 194 - Fix percentage value ([5aab231](https://github.com/resola-ai/deca-apps/commit/5aab231545c0c9fc889b0c4ad2adebc35701af52))
* **crm:** crm - 199 - change defaultValue to value ([060c168](https://github.com/resola-ai/deca-apps/commit/060c168fa93667ce106473ccda28a7c46128e30e))
* **crm:** CRM - 203: Update new record ([eeb3362](https://github.com/resola-ai/deca-apps/commit/eeb3362ec687e9fa4acd925f9626f77a5e2c9b24))
* **crm:** CRM - 204 - fix wrong text ([6a5fa20](https://github.com/resola-ai/deca-apps/commit/6a5fa2003c273ae8a513db79ba51a3e56b33fa74))
* **crm:** CRM-137 add No Result Found UI for table filter ([8e29eca](https://github.com/resola-ai/deca-apps/commit/8e29eca510fd33a519f9eda6b6b44cd8d8592513))
* **crm:** CRM-137 add Noto Sans JP font ([13f4942](https://github.com/resola-ai/deca-apps/commit/13f49424ee2285f267a77a6aa7c8c8b4b1ba0d03))
* **crm:** CRM-137 restrict empty image size to 240px ([52beecd](https://github.com/resola-ai/deca-apps/commit/52beecd47234b86e71aacbe3ac98c113dc371350))
* **crm:** CRM-139 install react-dom ([5f92544](https://github.com/resola-ai/deca-apps/commit/5f92544599d7487135f5398be6945b6db1050e46))
* **crm:** CRM-139 move filter result below table head ([3fe21c2](https://github.com/resola-ai/deca-apps/commit/3fe21c252d1c090e01163d6fd5531b32139a7cb9))
* **crm:** CRM-141 hide nested Add Group button ([144fb76](https://github.com/resola-ai/deca-apps/commit/144fb763c9f7dcbb0180d0ca87dc8f4ce6d37459))
* **crm:** CRM-141 only show icon for add rule button inside a rule group ([5507cf3](https://github.com/resola-ai/deca-apps/commit/5507cf30ef045c7ae6055c21681c659853c70c84))
* **crm:** CRM-150 - [UI] [Profile] Integrate API on History ([#2000](https://github.com/resola-ai/deca-apps/issues/2000)) ([09ed4f3](https://github.com/resola-ai/deca-apps/commit/09ed4f37e054b9ee83c81b0bb7aff12bb0ba2844))
* **crm:** CRM-175 - [UI] [Profile] Handle the topbar button actions ([#1984](https://github.com/resola-ai/deca-apps/issues/1984)) ([d8ce4a9](https://github.com/resola-ai/deca-apps/commit/d8ce4a9e28e55bfc1bd75143830a39b29f0c7415))
* **crm:** CRM-182 - Improve Sidebar height ([#2026](https://github.com/resola-ai/deca-apps/issues/2026)) ([d57c145](https://github.com/resola-ai/deca-apps/commit/d57c145e31994d0d9450dd1ce47affe3b13e38ea))
* **crm:** CRM-187 cleanup translations ([a9cb3fd](https://github.com/resola-ai/deca-apps/commit/a9cb3fdd98ca48416ab110e9755919db1d45636e))
* **crm:** CRM-187 fix Identity badges ([a4f0e23](https://github.com/resola-ai/deca-apps/commit/a4f0e23d3ca12d62801bb50abd188070eb93ef18))
* **crm:** CRM-193 - Missing Descriptions and Texts in Create field are not in correct format ([#2132](https://github.com/resola-ai/deca-apps/issues/2132)) ([525fef6](https://github.com/resola-ai/deca-apps/commit/525fef6fea98b71e5a6229d8a7e13f4f6efe1de8))
* **crm:** customer profile data grouping by date ([6a2cff5](https://github.com/resola-ai/deca-apps/commit/6a2cff59bc98962c8224d003842f2f5c0c9af3f0))
* **crm:** Fix - 162 - Fix wrong hover action ([b4f520a](https://github.com/resola-ai/deca-apps/commit/b4f520a0e144ada0987ea7f7935d32111f857432))
* **crm:** Fix - 199 - Fix missing default value ([a3790d8](https://github.com/resola-ai/deca-apps/commit/a3790d82a886d364af9cce3555b42240f245cef4))
* **crm:** Fix - 203 - Fix type config ([0ce07e7](https://github.com/resola-ai/deca-apps/commit/0ce07e74683c04104e4908883f21441ba9b034b7))
* **crm:** fix build error ([737a337](https://github.com/resola-ai/deca-apps/commit/737a3375c9f30321756511f9e003913ae44e1fa2))
* **crm:** fix build error ([6a4b8fd](https://github.com/resola-ai/deca-apps/commit/6a4b8fd2dbe3c3c39e0ad93e9cb57597b8cc003a))
* **crm:** Fix crash app ([79a537c](https://github.com/resola-ai/deca-apps/commit/79a537c69119bdddc94959b8d11731f9eb365b55))
* **crm:** Fix crash app ([e2b83f8](https://github.com/resola-ai/deca-apps/commit/e2b83f83036383327e5b2e272140339163e713b5))
* **crm:** Fix long text header ([c939dba](https://github.com/resola-ai/deca-apps/commit/c939dbae6072391c756315c98e5e7d440f211ac3))
* **crm:** Fix route ([208f067](https://github.com/resola-ai/deca-apps/commit/208f067b999e320eea23ba8383d88ef6e67d1b0c))
* **crm:** Fix routing issue ([2e4e0a7](https://github.com/resola-ai/deca-apps/commit/2e4e0a74ee0eff3a7234eb72748f1df662c30db8))
* **crm:** Fix size image ([2a641ec](https://github.com/resola-ai/deca-apps/commit/2a641eca4da09e10f48d8fc231992e58f50235e9))
* **crm:** Fix Tabs behaviors ([be6b483](https://github.com/resola-ai/deca-apps/commit/be6b48381bdd047fe0f86489953eef02856ec2e9))
* **crm:** make workshop great again ([603ea1b](https://github.com/resola-ai/deca-apps/commit/603ea1b5921244cbdaa2bc255a25683ce33dfdbe))
* **crm:** missing highlight sidebar ([3ad4df6](https://github.com/resola-ai/deca-apps/commit/3ad4df677406010ffba439c44f158b1674b1fc40))
* **crm:** Move 404 route into /crm ([ecee324](https://github.com/resola-ai/deca-apps/commit/ecee324cc91742f42f4b73623884abd1cdb4d20d))
* **crm:** prevent AddNewColumn from being added to table filter ([b52c5b2](https://github.com/resola-ai/deca-apps/commit/b52c5b258a9772ae84677f42047b477f508d0752))
* **crm:** remove unused variable ([a6a2f3a](https://github.com/resola-ai/deca-apps/commit/a6a2f3a411fb9fafb254fec312892df8529a0dbf))
* **crm:** Should close modal when getting error ([0be024a](https://github.com/resola-ai/deca-apps/commit/0be024aedd2c2a5f203d56257b4c90e89a480533))
* **crm:** table filter count ([5daa9b9](https://github.com/resola-ai/deca-apps/commit/5daa9b9192bbe58cf03ab905a5cffadd7be788a4))
* **crm:** Tabs behavior ([684c092](https://github.com/resola-ai/deca-apps/commit/684c0925d25c8cacfe80d802daf5ffbfdeff6ae5))
