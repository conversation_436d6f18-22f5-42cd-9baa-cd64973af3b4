import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { FlowListContextProvider, useFlowListContext } from './index';
import { FlowApi, type FlowListSuccessResponse } from '@/services/api/flow';
import type { Flow } from '@/models/flow';
import { FlowNodeType } from '@/models/flow';
import { LayoutType } from '@/types';
import { MemoryRouter, useParams, useNavigate } from 'react-router-dom'; // Import necessary parts
import React from 'react';
import { ulid } from 'ulid';

// Mock router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useParams: vi.fn(() => ({ workspaceId: '123' })), // Mock useParams
    useNavigate: vi.fn(), // Mock useNavigate
  };
});

// Mock API
vi.mock('@/services/api/flow', () => ({
  FlowApi: {
    getList: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
}));

// Mock ulid
vi.mock('ulid', () => ({
  ulid: vi.fn().mockImplementation(() => 'mocked-ulid-id'),
}));

// Mock useTranslate
vi.mock('@tolgee/react', () => ({
  useTranslate: vi.fn(() => ({
    t: (key: string) => key,
  })),
}));

// Mock useHandleApiError
vi.mock('@/hooks/useHandleApiError', () => ({
  useHandleApiError: () => ({
    handleApiError: vi.fn(),
  }),
}));

describe('FlowListContext', () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <MemoryRouter>
      <FlowListContextProvider>{children}</FlowListContextProvider>
    </MemoryRouter>
  );

  const mockFlow: Flow = {
    id: '1',
    name: 'Test Flow',
    description: 'Test Description',
    nodes: {},
    status: 'enabled',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    triggers: {},
  };

  const mockFlowListResponse = {
    data: [mockFlow],
    nextCursor: null,
    prevCursor: null,
    hasMore: false,
    hasPrev: false,
    currentPage: 1,
  } as unknown as FlowListSuccessResponse;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.mocked(FlowApi.getList).mockResolvedValue(mockFlowListResponse);
    vi.mocked(useParams).mockReturnValue({ workspaceId: '123' }); // Ensure useParams is mocked correctly
    vi.mocked(useNavigate).mockReturnValue(vi.fn());
    // Mock ulid to return different values for trigger and node IDs
    vi.mocked(ulid).mockReturnValueOnce('trigger-id').mockReturnValueOnce('node-id');
  });

  it('provides flow list context with default values', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    expect(result.current.flowList).toEqual([]);
    expect(result.current.limit).toBe(10);
    expect(result.current.search).toBe('');
    expect(result.current.filter).toBe('model');
    expect(result.current.layout).toBe(LayoutType.GRID);
    expect(result.current.isLoading).toBe(true);
    expect(result.current.openedCreateModal).toBe(false);
    expect(result.current.openedWaitingModal).toBe(false);
    expect(result.current.openedTemplateModal).toBe(false);
    expect(result.current.openedEditModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(false);
    expect(result.current.cursorObject).toEqual({
      nextCursor: undefined,
      prevCursor: undefined,
      currentPage: 1,
    });
  });

  it('fetches flow list successfully', async () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      await result.current.refetchList();
    });

    expect(FlowApi.getList).toHaveBeenCalledWith('123', 10, '', '');
    expect(result.current.flowList).toEqual([mockFlow]);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.cursorObject).toEqual({
      nextCursor: null,
      prevCursor: null,
      currentPage: 1,
    });
  });

  it('handles getList error', async () => {
    const error = new Error('Failed to fetch flows');
    vi.mocked(FlowApi.getList).mockRejectedValueOnce(error);

    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    // we need to set flowList to [] before refetchList
    act(() => {
      result.current.setFlowList([]);
    });
    await act(async () => {
      try {
        await result.current.refetchList();
      } catch (e) {
        // Catch the error here to prevent it from being thrown globally
      }
    });

    expect(FlowApi.getList).toHaveBeenCalledWith('123', 10, '', '');
    expect(result.current.flowList).toEqual([]);
    expect(result.current.isLoading).toBe(false);
  });

  it('updates limit when setLimit is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setLimit(20);
    });

    expect(result.current.limit).toBe(20);
  });

  it('updates layout when setLayout is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setLayout(LayoutType.LIST);
    });

    expect(result.current.layout).toBe(LayoutType.LIST);
    expect(result.current.isFullWidth).toBe(true);
  });

  it('updates search when setSearch is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setSearch('test');
    });

    expect(result.current.search).toBe('test');
  });

  it('updates flowList when setFlowList is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setFlowList([mockFlow]);
    });

    expect(result.current.flowList).toEqual([mockFlow]);
  });

  it('updates cursor when handleSetCursor is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleSetCursor('next-cursor');
    });

    expect(result.current.cursor).toBe('next-cursor');
  });

  it('updates filter when handleFilter is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleFilter('newFilter');
    });

    expect(result.current.filter).toBe('newFilter');
  });

  it('updates search when handleOnChangeSearch is called', async () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      result.current.handleOnChangeSearch('newSearch');
      await new Promise((resolve) => setTimeout(resolve, 150));
    });

    expect(result.current.search).toBe('newSearch');
  });

  it('updates openedCreateModal when setOpenedCreateModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedCreateModal(true);
    });

    expect(result.current.openedCreateModal).toBe(true);
  });

  it('updates openedWaitingModal when setOpenedWaitingModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedWaitingModal(true);
    });

    expect(result.current.openedWaitingModal).toBe(true);
  });

  it('updates openedTemplateModal when setOpenedTemplateModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedTemplateModal(true);
    });

    expect(result.current.openedTemplateModal).toBe(true);
  });

  it('updates openedEditModal when setOpenedEditModal is called', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedEditModal(true);
    });

    expect(result.current.openedEditModal).toBe(true);
  });

  it('handles create flow from scratch', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleCreateFromScratch();
    });

    expect(result.current.openedCreateModal).toBe(true);
    expect(result.current.isCreatingFromTemplate).toBe(false);
  });

  it('handles close create flow modal', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.setOpenedCreateModal(true);
      result.current.handleCloseCreateFlowModal();
    });

    expect(result.current.openedCreateModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(false);
  });

  it('handles import from file', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleImportFromFile();
    });
    // No state change, just console log
  });

  it('handles create from template', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleCreateFromTemplate();
    });

    expect(result.current.openedTemplateModal).toBe(true);
  });

  it('handles select template to create', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleSelectTemplateToCreate(mockFlow);
    });

    expect(result.current.openedCreateModal).toBe(true);
    expect(result.current.openedTemplateModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(true);
  });

  it('creates a flow successfully and updates flowList', async () => {
    const mockCreatedFlow = {
      ...mockFlow,
      id: 'new-flow-id',
      name: 'New Flow',
      description: 'New Description',
    };

    vi.mocked(FlowApi.create).mockResolvedValueOnce(mockCreatedFlow);
    const mockNavigate = vi.fn();
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);

    // Reset ulid mock for this specific test
    vi.mocked(ulid).mockReturnValueOnce('trigger-id').mockReturnValueOnce('node-id');

    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    // Set initial flow list
    act(() => {
      result.current.setFlowList([mockFlow]);
    });

    await act(async () => {
      await result.current.handleCreateFlow({ title: 'New Flow', description: 'New Description' });
    });

    // Verify the API was called with correct parameters
    expect(FlowApi.create).toHaveBeenCalledWith('123', {
      name: 'New Flow',
      description: 'New Description',
      triggers: {
        'trigger-id': {
          id: 'trigger-id',
          name: FlowNodeType.NewTrigger,
          displayName: 'defaultNameTriggerNode',
          description: '',
          settings: {},
          next: 'node-id',
        },
      },
      nodes: {
        'node-id': {
          id: 'node-id',
          name: FlowNodeType.EmptyNode,
          displayName: 'defaultNameActionNode',
          description: '',
          settings: {},
          next: '',
          prev: 'trigger-id',
        },
      },
      status: 'enabled',
    });

    // Verify navigation occurred
    expect(mockNavigate).toHaveBeenCalledWith('/studio/123/flows/new-flow-id');
  });

  it('creates a flow from template and opens waiting modal', async () => {
    const mockCreatedFlow = {
      ...mockFlow,
      id: 'template-flow-id',
      name: 'Template Flow',
      description: 'Template Description',
    };

    vi.mocked(FlowApi.create).mockResolvedValueOnce(mockCreatedFlow);
    const mockNavigate = vi.fn();
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);

    // Reset ulid mock for this specific test
    vi.mocked(ulid)
      .mockReturnValueOnce('trigger-id-template')
      .mockReturnValueOnce('node-id-template');

    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    // Set up creating from template state
    act(() => {
      result.current.handleSelectTemplateToCreate(mockFlow);
    });

    expect(result.current.isCreatingFromTemplate).toBe(true);
    expect(result.current.openedCreateModal).toBe(true);
    expect(result.current.openedWaitingModal).toBe(false); // Initially false

    await act(async () => {
      await result.current.handleCreateFlow({
        title: 'Template Flow',
        description: 'Template Description',
      });
    });

    // Verify waiting modal was opened when creating from template
    // Note: In the current implementation, waiting modal stays open after successful creation
    expect(result.current.openedWaitingModal).toBe(true);

    // Verify navigation occurred
    expect(mockNavigate).toHaveBeenCalledWith('/studio/123/flows/template-flow-id');
  });

  it('updates flowList reactively when new data is available', async () => {
    const initialFlowList = [mockFlow];
    const newFlow = {
      ...mockFlow,
      id: '2',
      name: 'New Flow',
      description: 'New Flow Description',
    };
    const updatedFlowListResponse = {
      ...mockFlowListResponse,
      data: [mockFlow, newFlow],
    };

    // Initially return the first flow list
    vi.mocked(FlowApi.getList).mockResolvedValueOnce(mockFlowListResponse);

    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    // Wait for initial load
    await act(async () => {
      await result.current.refetchList();
    });

    expect(result.current.flowList).toEqual(initialFlowList);

    // Mock API to return updated flow list
    vi.mocked(FlowApi.getList).mockResolvedValueOnce(updatedFlowListResponse);

    // Refetch to simulate reactive data update
    await act(async () => {
      await result.current.refetchList();
    });

    // Verify flowList is updated with new data
    expect(result.current.flowList).toEqual([mockFlow, newFlow]);
    expect(result.current.flowList).toHaveLength(2);
  });

  it('handles empty flow list data reactively', async () => {
    const emptyFlowListResponse = {
      ...mockFlowListResponse,
      data: [],
    };

    vi.mocked(FlowApi.getList).mockResolvedValueOnce(emptyFlowListResponse);

    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    // Set initial flow list with data
    act(() => {
      result.current.setFlowList([mockFlow]);
    });

    expect(result.current.flowList).toEqual([mockFlow]);

    // Refetch to simulate empty data response
    await act(async () => {
      await result.current.refetchList();
    });

    // Verify flowList is updated to empty array
    expect(result.current.flowList).toEqual([]);
    expect(result.current.flowList).toHaveLength(0);
  });

  it('handles createFlow error', async () => {
    vi.mocked(FlowApi.create).mockRejectedValueOnce(new Error('Failed to create flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      await result.current.handleCreateFlow({ title: 'New Flow', description: 'New Description' });
    });

    expect(result.current.openedCreateModal).toBe(false);
    expect(result.current.openedWaitingModal).toBe(false);
    expect(result.current.isCreatingFromTemplate).toBe(false);
  });

  it('updates a flow successfully', async () => {
    vi.mocked(FlowApi.update).mockResolvedValueOnce(mockFlow);
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleEditFLow(mockFlow);
    });

    expect(FlowApi.update).toHaveBeenCalledWith('123', mockFlow);
    expect(result.current.flowList).toEqual([mockFlow]);
    expect(result.current.openedEditModal).toBe(false);
  });

  it('handles updateFlow error', async () => {
    vi.mocked(FlowApi.update).mockRejectedValueOnce(new Error('Failed to update flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleEditFLow(mockFlow);
    });

    expect(FlowApi.update).toHaveBeenCalledWith('123', mockFlow);
    expect(result.current.openedEditModal).toBe(false);
  });

  it('duplicates a flow successfully', async () => {
    const mockDuplicatedFlow = { ...mockFlow, id: 'new-flow-id', name: 'Test Flow (Copy)' };
    vi.mocked(FlowApi.create).mockResolvedValueOnce(mockDuplicatedFlow);
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDuplicateFlow(mockFlow);
    });

    expect(FlowApi.create).toHaveBeenCalledWith('123', {
      name: 'Test Flow (Copy)',
      description: 'Test Description',
      nodes: {},
      triggers: {},
      status: 'enabled',
    });
    expect(result.current.flowList).toEqual([mockFlow, mockDuplicatedFlow]);
  });

  it('handles duplicateFlow error', async () => {
    vi.mocked(FlowApi.create).mockRejectedValueOnce(new Error('Failed to duplicate flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDuplicateFlow(mockFlow);
    });

    expect(FlowApi.create).toHaveBeenCalledWith('123', {
      name: 'Test Flow (Copy)',
      description: 'Test Description',
      nodes: {},
      triggers: {},
      status: 'enabled',
    });
    expect(result.current.flowList).toEqual([mockFlow]);
  });

  it('deletes a flow successfully', async () => {
    vi.mocked(FlowApi.delete).mockResolvedValueOnce(undefined);
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDeleteFlow(mockFlow);
    });

    expect(FlowApi.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.flowList).toEqual([]);
  });

  it('handles deleteFlow error', async () => {
    vi.mocked(FlowApi.delete).mockRejectedValueOnce(new Error('Failed to delete flow'));
    const { result } = renderHook(() => useFlowListContext(), { wrapper });
    act(() => {
      result.current.setFlowList([mockFlow]);
    });
    await act(async () => {
      await result.current.handleDeleteFlow(mockFlow);
    });

    expect(FlowApi.delete).toHaveBeenCalledWith('123', '1');
    expect(result.current.flowList).toEqual([mockFlow]);
  });

  it('navigates after successful flow creation', async () => {
    const mockNavigate = vi.fn();
    vi.mocked(useNavigate).mockReturnValue(mockNavigate);
    vi.mocked(FlowApi.create).mockResolvedValueOnce({ ...mockFlow, id: 'new-flow-id' });

    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    await act(async () => {
      await result.current.handleCreateFlow({ title: 'New Flow', description: 'New Description' });
    });

    expect(mockNavigate).toHaveBeenCalledWith('/studio/123/flows/new-flow-id');
  });

  it('throws error when useFlowListContext is used outside provider', () => {
    expect(() => {
      renderHook(() => useFlowListContext());
    }).toThrow('useFlowListContext must be used within a FlowListContextProvider');
  });

  it('handles filter with null value', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleFilter(null);
    });

    // Filter should not change when null is passed (stays at default 'model')
    expect(result.current.filter).toBe('model');
  });

  it('handles filter with empty string', () => {
    const { result } = renderHook(() => useFlowListContext(), { wrapper });

    act(() => {
      result.current.handleFilter('');
    });

    // Filter should not change when empty string is passed (stays at default 'model')
    expect(result.current.filter).toBe('model');
  });
});
