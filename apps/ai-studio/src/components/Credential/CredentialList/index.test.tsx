import { screen, fireEvent, waitFor } from '@testing-library/react';
import { CredentialList } from './index';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { vi } from 'vitest';
import { MemoryRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import { AppContextProvider } from '@/contexts/AppContext';
import { mockCredentials } from '@/mockdata/credential';

mockLibraries();

vi.mock('@resola-ai/ui', async () => {
  const actual = await vi.importActual('@resola-ai/ui');
  return {
    ...actual,
    CatalogNodeIcon: ({ name }: { name: string }) => (
      <span data-testid={`icon-${name}`}>{name}</span>
    ),
  };
});

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [new URLSearchParams({ lang: 'en' })],
  };
});

const mockOnEdit = vi.fn();
const mockOnDelete = vi.fn();
const mockOnTest = vi.fn();
const mockOnReconnect = vi.fn();
const mockOnSelect = vi.fn();

const renderWithRouter = (ui: React.ReactElement) => {
  return renderWithMantine(
    <MemoryRouter>
      <AppContextProvider>{ui}</AppContextProvider>
    </MemoryRouter>
  );
};

describe('CredentialList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the list of credentials', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    expect(screen.getByText(mockCredentials[0].name)).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[1].name)).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[2].name)).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[0].description ?? '')).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[1].description ?? '')).toBeInTheDocument();
    expect(screen.getByText(mockCredentials[2].description ?? '')).toBeInTheDocument();
  });

  it('renders empty state when no credentials are provided', () => {
    renderWithRouter(
      <CredentialList
        credentials={[]}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const gridLayout = screen.getByTestId('grid-layout');
    expect(gridLayout).toBeInTheDocument();
    expect(gridLayout.children.length).toBe(0);
  });

  it('displays correct icons for different providers', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials.slice(0, 3)}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    expect(screen.getByTestId('icon-openai')).toBeInTheDocument();
    expect(screen.getByTestId('icon-google')).toBeInTheDocument();
    expect(screen.getByTestId('icon-slack')).toBeInTheDocument();
  });

  it('handles edit action', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Open the actions menu
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();

    const user = userEvent.setup();
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    expect(editAction).toBeInTheDocument();

    await user.click(editAction);
    // Check if edit modal is opened
    const modal = await screen.findByText('modalEdit.title');
    expect(modal).toBeInTheDocument();

    // Fill in the edit form
    const nameInput = await screen.findByTestId('title-input');
    const descriptionInput = await screen.findByTestId('description-input');

    fireEvent.change(nameInput, { target: { value: 'Updated Name' } });
    fireEvent.change(descriptionInput, { target: { value: 'Updated Description' } });

    // Submit the form
    const confirmButton = screen.getByTestId('confirm-edit-modal');
    expect(confirmButton).toBeInTheDocument();

    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith({
        ...mockCredentials[0],
        name: 'Updated Name',
        description: 'Updated Description',
      });
      expect(screen.queryByText('modalEdit.title')).not.toBeVisible();
    });
  });

  it('handles edit action when onEdit is not provided', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    // Modal should still open
    const modal = await screen.findByText('modalEdit.title');
    expect(modal).toBeInTheDocument();

    // Fill and submit form
    const nameInput = await screen.findByTestId('title-input');
    const confirmButton = screen.getByTestId('confirm-edit-modal');

    fireEvent.change(nameInput, { target: { value: 'Updated Name' } });
    await user.click(confirmButton);

    // Should close modal without calling onEdit
    await waitFor(() => {
      expect(screen.queryByText('modalEdit.title')).not.toBeVisible();
    });
    expect(mockOnEdit).not.toHaveBeenCalled();
  });

  it('Close edit modal when cancel button is clicked', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    const cancelButton = await screen.findByText('common.button.cancel');
    await user.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText('modalEdit.title')).toBeNull();
    });
  });

  it('show delete modal when delete button is clicked', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    await waitFor(() => {
      expect(mockOnDelete).toHaveBeenCalledWith(mockCredentials[0], expect.any(Function));
    });
  });

  it('handles delete action when onDelete is not provided', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    // Should not call onDelete
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('handles test action', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Open the actions menu
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    fireEvent.click(menuButton);

    const user = userEvent.setup();
    // Click the test button in the menu
    const testButton = await screen.findByText('action.test');
    await user.click(testButton);

    await waitFor(() => {
      expect(mockOnTest).toHaveBeenCalledWith(mockCredentials[0], expect.any(Function));
    });
  });

  it('handles test action when onTest is not provided', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const testButton = await screen.findByText('action.test');
    await user.click(testButton);

    // Should not call onTest
    expect(mockOnTest).not.toHaveBeenCalled();
  });

  it('handles reconnect action', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Open the actions menu
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();

    const user = userEvent.setup();
    await user.click(menuButton);

    // Click the reconnect button in the menu
    const reconnectButton = await screen.findByText('action.reconnect');
    expect(reconnectButton).toBeInTheDocument();

    await user.click(reconnectButton);

    await waitFor(() => {
      expect(mockOnReconnect).toHaveBeenCalledWith(mockCredentials[0], expect.any(Function));
    });
  });

  it('handles reconnect action when onReconnect is not provided', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const reconnectButton = await screen.findByText('action.reconnect');
    await user.click(reconnectButton);

    // Should not call onReconnect
    expect(mockOnReconnect).not.toHaveBeenCalled();
  });

  it('handles credential selection when onSelect is provided', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
        onSelect={mockOnSelect}
      />
    );

    const user = userEvent.setup();
    const cards = screen.getAllByTestId(`aic-card`);

    await user.click(cards[0]);

    expect(mockOnSelect).toHaveBeenCalledWith(mockCredentials[0]);
  });

  it('does not call onSelect when not provided', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const cards = screen.getAllByTestId(`aic-card`);

    await user.click(cards[0]);

    expect(mockOnSelect).not.toHaveBeenCalled();
  });

  it('renders with full width layout when isFullWidth is true', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        isFullWidth={true}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const gridLayout = screen.getByTestId('grid-layout');
    const children = gridLayout.children;
    Array.from(children).forEach((child) => {
      expect(child.getAttribute('data-testid')).toBe('aic-card-full-width');
    });
  });

  it('renders with default layout when isFullWidth is false', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        isFullWidth={false}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const gridLayout = screen.getByTestId('grid-layout');
    const children = gridLayout.children;
    Array.from(children).forEach((child) => {
      expect(child.getAttribute('data-testid')).toBe('aic-card');
    });
  });

  it('renders with default layout when isFullWidth is not provided', () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const gridLayout = screen.getByTestId('grid-layout');
    const children = gridLayout.children;
    Array.from(children).forEach((child) => {
      expect(child.getAttribute('data-testid')).toBe('aic-card');
    });
  });

  it('handles edit modal with empty initial values', async () => {
    const credentialWithoutDescription = {
      ...mockCredentials[0],
      description: undefined,
    };

    renderWithRouter(
      <CredentialList
        credentials={[credentialWithoutDescription]}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    // Check that modal opens with empty description
    const descriptionInput = await screen.findByTestId('description-input');
    expect(descriptionInput).toHaveValue('');

    const confirmButton = screen.getByTestId('confirm-edit-modal');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith({
        ...credentialWithoutDescription,
        name: credentialWithoutDescription.name,
        description: '',
      });
    });
  });

  it('handles multiple credential cards with different providers', () => {
    const mixedCredentials = [
      mockCredentials[0], // openai
      mockCredentials[1], // google
      mockCredentials[2], // slack
    ];

    renderWithRouter(
      <CredentialList
        credentials={mixedCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    // Verify all cards are rendered
    mixedCredentials.forEach((credential) => {
      expect(screen.getByText(credential.name)).toBeInTheDocument();
    });

    // Verify correct icons are displayed
    expect(screen.getByTestId('icon-openai')).toBeInTheDocument();
    expect(screen.getByTestId('icon-google')).toBeInTheDocument();
    expect(screen.getByTestId('icon-slack')).toBeInTheDocument();
  });

  it('handles edit modal form validation with empty title', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    // Clear the name field to test validation
    const nameInput = await screen.findByTestId('title-input');
    await user.clear(nameInput);

    const confirmButton = screen.getByTestId('confirm-edit-modal');
    await user.click(confirmButton);

    // The EditModal component should prevent submission with empty title
    // and show validation error instead of calling onEdit
    await waitFor(() => {
      expect(mockOnEdit).not.toHaveBeenCalled();
      // Check for validation error message
      expect(screen.getByText('common.error.required')).toBeInTheDocument();
    });
  });

  it('handles edit modal form validation with valid title', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    // Fill in a valid title
    const nameInput = await screen.findByTestId('title-input');
    await user.clear(nameInput);
    await user.type(nameInput, 'Valid Title');

    const confirmButton = screen.getByTestId('confirm-edit-modal');
    await user.click(confirmButton);

    // Should submit successfully with valid title
    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith({
        ...mockCredentials[0],
        name: 'Valid Title',
        description: mockCredentials[0].description,
      });
    });
  });

  it('handles edit modal with special characters in name and description', async () => {
    renderWithRouter(
      <CredentialList
        credentials={mockCredentials}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
        onTest={mockOnTest}
        onReconnect={mockOnReconnect}
      />
    );

    const user = userEvent.setup();
    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('action.edit');
    await user.click(editAction);

    const nameInput = await screen.findByTestId('title-input');
    const descriptionInput = await screen.findByTestId('description-input');

    const specialName = 'Test & Credential <script>alert("xss")</script>';
    const specialDescription = 'Description with "quotes" and \'apostrophes\'';

    fireEvent.change(nameInput, { target: { value: specialName } });
    fireEvent.change(descriptionInput, { target: { value: specialDescription } });

    const confirmButton = screen.getByTestId('confirm-edit-modal');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockOnEdit).toHaveBeenCalledWith({
        ...mockCredentials[0],
        name: specialName,
        description: specialDescription,
      });
    });
  });
});
