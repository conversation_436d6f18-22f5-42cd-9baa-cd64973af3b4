import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { ProviderList } from './index';
import { mockLibraries, renderWithMantine } from '@/utils/test';
import { MemoryRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import { AppContextProvider } from '@/contexts/AppContext';
import { mockTools } from '@/mockdata/credential';

mockLibraries();

// Mock useSearchParams
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [new URLSearchParams({ lang: 'en' })],
  };
});

const mockOnSelect = vi.fn();

// Mock CredentialContext
vi.mock('@/contexts/CredentialContext', () => ({
  useCredentialContext: () => ({
    tools: mockTools,
  }),
  CredentialContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

describe('ProviderList', () => {
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    vi.clearAllMocks();
    user = userEvent.setup();
  });

  // Helper function to render component
  const renderComponent = (onSelect = mockOnSelect, isOpen = false) => {
    return renderWithMantine(
      <MemoryRouter>
        <AppContextProvider>
          <ProviderList onSelect={onSelect} isOpen={isOpen} />
        </AppContextProvider>
      </MemoryRouter>
    );
  };

  describe('Basic Rendering', () => {
    const renderingTests = [
      {
        name: 'renders the list of tools correctly',
        props: {},
        expectations: () => {
          expect(screen.getByText('Slack')).toBeInTheDocument();
          expect(screen.getByText('Google')).toBeInTheDocument();
          expect(screen.getByText('OpenAI')).toBeInTheDocument();
          expect(screen.getByText('Slack API')).toBeInTheDocument();
          expect(screen.getByText('Google API')).toBeInTheDocument();
          expect(screen.getByText('OpenAI API')).toBeInTheDocument();
        },
      },
      {
        name: 'renders search bar with correct placeholder',
        props: {},
        expectations: () => {
          const searchInput = screen.getByPlaceholderText('tool.list.search');
          expect(searchInput).toBeInTheDocument();
        },
      },
      {
        name: 'renders grid layout with correct test id',
        props: {},
        expectations: () => {
          expect(screen.getByTestId('grid-layout')).toBeInTheDocument();
        },
      },
      {
        name: 'renders all tools by default',
        props: {},
        expectations: () => {
          const cards = screen.getAllByTestId('aic-card');
          expect(cards.length).toBeGreaterThan(0);
        },
      },
    ];

    renderingTests.forEach(({ name, expectations }) => {
      it(name, () => {
        renderComponent();
        expectations();
      });
    });
  });

  describe('Search Functionality', () => {
    it('filters tools by search term', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'Slack');

      await waitFor(() => {
        expect(screen.getByText('Slack')).toBeInTheDocument();
        expect(screen.queryByText('Google')).not.toBeInTheDocument();
        expect(screen.queryByText('OpenAI')).not.toBeInTheDocument();
      });
    });

    it('filters tools by partial search term', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'Goo');

      await waitFor(() => {
        expect(screen.getByText('Google')).toBeInTheDocument();
        expect(screen.queryByText('Slack')).not.toBeInTheDocument();
        expect(screen.queryByText('OpenAI')).not.toBeInTheDocument();
      });
    });

    it('filters tools case-insensitively', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'slack');

      await waitFor(() => {
        expect(screen.getByText('Slack')).toBeInTheDocument();
        expect(screen.queryByText('Google')).not.toBeInTheDocument();
      });
    });

    it('shows no results when search term does not match', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'NonExistentTool');

      await waitFor(() => {
        expect(screen.queryByText('Slack')).not.toBeInTheDocument();
        expect(screen.queryByText('Google')).not.toBeInTheDocument();
        expect(screen.queryByText('OpenAI')).not.toBeInTheDocument();
      });
    });

    it('clears search results when search input is cleared', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'Slack');

      await waitFor(() => {
        expect(screen.getByText('Slack')).toBeInTheDocument();
        expect(screen.queryByText('Google')).not.toBeInTheDocument();
      });

      await user.clear(searchInput);

      await waitFor(() => {
        expect(screen.getByText('Slack')).toBeInTheDocument();
        expect(screen.getByText('Google')).toBeInTheDocument();
        expect(screen.getByText('OpenAI')).toBeInTheDocument();
      });
    });
  });

  describe('Tool Selection', () => {
    it('calls onSelect when a tool is clicked', async () => {
      renderComponent();

      const slackCard = screen.getByText('Slack').closest('div[data-testid="aic-card"]');
      if (slackCard) {
        await user.click(slackCard);
      }

      await waitFor(() => {
        expect(mockOnSelect).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'slack',
            displayName: 'Slack',
          })
        );
      });
    });

    it('calls onSelect with correct tool data for different tools', async () => {
      renderComponent();

      const googleCard = screen.getByText('Google').closest('div[data-testid="aic-card"]');
      if (googleCard) {
        await user.click(googleCard);
      }

      await waitFor(() => {
        expect(mockOnSelect).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'google',
            displayName: 'Google',
          })
        );
      });
    });

    it('handles multiple tool selections', async () => {
      renderComponent();

      const slackCard = screen.getByText('Slack').closest('div[data-testid="aic-card"]');
      const googleCard = screen.getByText('Google').closest('div[data-testid="aic-card"]');

      if (slackCard) {
        await user.click(slackCard);
      }
      if (googleCard) {
        await user.click(googleCard);
      }

      await waitFor(() => {
        expect(mockOnSelect).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Focus Management', () => {
    it('focuses search bar when isOpen is true', async () => {
      renderComponent(mockOnSelect, true);

      const searchInput = screen.getByPlaceholderText('tool.list.search');

      await waitFor(() => {
        expect(searchInput).toHaveFocus();
      });
    });

    it('does not focus search bar when isOpen is false', async () => {
      renderComponent(mockOnSelect, false);

      const searchInput = screen.getByPlaceholderText('tool.list.search');

      await waitFor(() => {
        expect(searchInput).not.toHaveFocus();
      });
    });
  });

  describe('Search State Management', () => {
    it('updates search value when typing', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'test');

      expect(searchInput).toHaveValue('test');
    });

    it('clears search value when cleared', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      await user.type(searchInput, 'test');
      await user.clear(searchInput);

      expect(searchInput).toHaveValue('');
    });

    it('filters tools based on search value changes', async () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');

      // Type search term
      await user.type(searchInput, 'Slack');
      await waitFor(() => {
        expect(screen.getByText('Slack')).toBeInTheDocument();
        expect(screen.queryByText('Google')).not.toBeInTheDocument();
      });

      // Clear and type different search term
      await user.clear(searchInput);
      await user.type(searchInput, 'Google');
      await waitFor(() => {
        expect(screen.getByText('Google')).toBeInTheDocument();
        expect(screen.queryByText('Slack')).not.toBeInTheDocument();
      });
    });
  });

  describe('Component Integration', () => {
    it('renders SearchBar component with correct props', () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');
      expect(searchInput).toBeInTheDocument();
    });

    it('renders GridLayout component with correct props', () => {
      renderComponent();

      const gridLayout = screen.getByTestId('grid-layout');
      expect(gridLayout).toBeInTheDocument();
    });

    it('renders AICard components for each tool', () => {
      renderComponent();

      const cards = screen.getAllByTestId('aic-card');
      expect(cards.length).toBeGreaterThan(0);
    });

    it('passes correct props to AICard components', () => {
      renderComponent();

      // Check that cards have the expected structure
      const cards = screen.getAllByTestId('aic-card');
      cards.forEach((card) => {
        expect(card).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has accessible search input', () => {
      renderComponent();

      const searchInput = screen.getByPlaceholderText('tool.list.search');

      expect(searchInput).toBeInTheDocument();
    });

    it('has clickable tool cards', async () => {
      renderComponent();

      const slackCard = screen.getByText('Slack').closest('div[data-testid="aic-card"]');
      expect(slackCard).toBeInTheDocument();

      if (slackCard) {
        await user.click(slackCard);
        expect(mockOnSelect).toHaveBeenCalled();
      }
    });
  });
});
