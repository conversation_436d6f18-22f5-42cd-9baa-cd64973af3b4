import { INodeAttributeConfig, IStudioNodeAttributeSuccessResponse } from '@/models';
import { executeRequest } from '../utils/helper';
import { axiosService } from '@resola-ai/services-shared';

export const NodeAttributesAPI = {
  getConfig: async (
    wsId: string,
    node: string,
    attribute: string,
    payload: Record<string, any>
  ) => {
    return executeRequest<IStudioNodeAttributeSuccessResponse<INodeAttributeConfig>>(() =>
      axiosService.instance.post(`${wsId}/nodes/attributes/config`, {
        node,
        attribute,
        payload,
      })
    );
  },
};
