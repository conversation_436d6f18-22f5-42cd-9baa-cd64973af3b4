import { Anchor } from '@mantine/core';
import { fireEvent, screen } from '@testing-library/react';
import type React from 'react';

// Import test utilities FIRST so that their vi.mock declarations
// are applied before we import anything from `react-router-dom`.
import {
  mockNavigate,
  mockSearchParamsState,
  renderWithMantine,
  renderWithProviders,
  setupCommonComponentRenderWithMocks,
  updateMockSearchParams,
} from '../unitTest';

// Now import mocked react-router-dom hooks (which will be properly mocked)
import { useNavigate, useSearchParams } from 'react-router-dom';

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

/**
 * MOCK DATA / CONSTANTS
 * ---------------------------------------------------------------------------
 */
const NEW_SEARCH_PARAMS = new URLSearchParams('foo=bar');
const NAVIGATE_PATH = '/next-page';

/**
 * TEST COMPONENTS
 * ---------------------------------------------------------------------------
 */
const SearchParamReader: React.FC = () => {
  const [params] = useSearchParams();
  return <div data-testid='params-value'>{params.get('foo') ?? ''}</div>;
};

const NavigateButton: React.FC = () => {
  const navigate = useNavigate();
  return (
    <button type='button' data-testid='navigate-btn' onClick={() => navigate(NAVIGATE_PATH)}>
      Go
    </button>
  );
};

/**
 * TEST SUITE
 * ---------------------------------------------------------------------------
 */

describe('unitTest utilities', () => {
  beforeEach(() => {
    // Reset all mocks & restore default search params before every test
    vi.resetAllMocks();
    updateMockSearchParams(new URLSearchParams());
  });

  afterEach(() => {
    // Ensure no unexpected calls linger between tests
    vi.clearAllMocks();
  });

  describe('updateMockSearchParams()', () => {
    it('should update mockSearchParamsState and be reflected in the useSearchParams hook', () => {
      updateMockSearchParams(NEW_SEARCH_PARAMS);

      // Sanity-check mockSearchParamsState is updated synchronously
      expect(mockSearchParamsState.value).toBe(NEW_SEARCH_PARAMS);

      renderWithProviders(<SearchParamReader />);
      expect(screen.getByTestId('params-value')).toHaveTextContent('bar');
    });
  });

  describe('mockNavigate()', () => {
    it('should be invoked when the returned navigate function is used', () => {
      renderWithProviders(<NavigateButton />);

      fireEvent.click(screen.getByTestId('navigate-btn'));
      expect(mockNavigate).toHaveBeenCalledTimes(1);
      expect(mockNavigate).toHaveBeenCalledWith(NAVIGATE_PATH);
    });
  });

  describe('render helpers', () => {
    it('renderWithMantine should correctly render Mantine wrapped components', () => {
      renderWithMantine(<Anchor data-testid='mantine-anchor'>Link</Anchor>);
      expect(screen.getByTestId('mantine-anchor')).toBeInTheDocument();
    });

    it('setupCommonComponentRenderWithMocks should return a functional render helper', () => {
      const { renderCommonComponent } = setupCommonComponentRenderWithMocks();
      renderCommonComponent(<div data-testid='common-render'>Content</div>);
      expect(screen.getByTestId('common-render')).toBeInTheDocument();
    });
  });
});
