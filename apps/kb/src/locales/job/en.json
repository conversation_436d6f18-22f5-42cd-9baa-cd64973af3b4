{"title": "Generation Jobs", "detailPageTitle": "Job Details", "description": "Here are all your article generation jobs. You can generate articles multiple times and navigate to other pages. Your progress will be saved, and you can return to check anytime.", "createdBy": "Created by", "backToJobs": "Go back to Generation Job list", "button": {"saveToKnowledgeBase": "Save to Knowledge Base", "downloadGeneratedArticles": "Download", "viewJobDetails": "View Job Details", "generatedArticlesCSV": "Comma Separated Values (.csv)", "generatedArticlesExcel": "Microsoft Excel (.xlsx)"}, "articleSavingModal": {"title": "Save Article", "description": "You can select a Knowledge Base from the list below or create a new one to save the article.", "successTitle": "Article Saved", "successMessage": "The article has been saved to the Knowledge Base.", "confirmSavingTitle": "Save Article", "confirmSavingMessage": "Are you sure you want to save these articles to \"{toKBName}\"? \nThis action cannot be undone."}, "emptyJobs": {"title": "No generation jobs found", "message": "You can create a new generation job by clicking the AI Generator button."}, "emptyJobArticles": {"title": "No articles found", "message": "Seems your generation articles are empty. Please check your generation job."}, "statuses": {"queued": "Queued", "running": "Running", "succeeded": "Succeeded", "failed": "Failed", "aborted": "Aborted", "retry": "Retry"}, "jobType": {"articleGeneration": "Generate Article", "contentGeneration": "Generate Content", "articleDownload": "Export Articles"}, "errors": {"defaultTitle": "Generation Error", "defaultMessage": "An error occurred while generating the article", "failed": {"title": "Generation Failed", "message": "The generation job has failed. Please try again."}, "aborted": {"title": "Generation Aborted", "message": "The generation job has been aborted. Please try again."}}, "retryJob": {"fallbackMessage": "Failed to retry job", "fallbackTitle": "Retry Job Failed", "successMessage": "The generation job has been retried.", "successTitle": "Retry Job Success"}, "deleteJob": {"confirmModal": {"title": "Delete Job", "message": "Are you sure you want to delete this job?"}, "notifications": {"success": {"title": "Success", "message": "The generation job has been deleted."}, "error": {"title": "Error", "message": "The generation job has failed to delete."}}}, "activity": {"title": "Activity", "types": {"saveToKb": "Saved in <KBName></KBName>", "edit": "Modified"}}, "jobSummary": {"label": {"creator": "Person Who Created", "status": "Job Status", "promptUsed": "Prompt Used", "contentSources": "Content Sources", "downloadExport": "Download Export"}, "viewGeneratedArticles": "View the Generated Articles", "noContentSources": "No content sources", "exportResult": "Export result", "noExportResultFound": "No export result found"}, "sourceDocument": {"label": "Source Document", "untitled": "Untitled Document"}, "downloadGenArticles": {"fallbackMessage": "Failed to download generated articles", "fallbackTitle": "Download Generated Articles Failed", "successMessage": "Generated articles downloaded successfully", "successTitle": "Download Generated Articles Success"}, "runJobButton": {"title": "Run New Job", "generationJob": "Run a new generation job", "exportJob": "Run a new export job"}}