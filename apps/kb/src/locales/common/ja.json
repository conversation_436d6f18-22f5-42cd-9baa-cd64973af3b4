{"logout": "ログアウト", "backToKBList": "ナレッジベース一覧へ戻る", "backToKBDetail": "{kbName} へ戻る", "backBtn": "前へ", "nextBtn": "次へ", "search": "検索", "searchPlaceholder": "検索フレーズを入力してEnterキーを押してください...", "selectAll": "すべて選択", "deselectAll": "すべて選択解除", "selection": {"parentFolders": "フォルダツリー", "childrenFolders": "フォルダ", "knowledgeBases": "ナレッジベース"}, "noBtn": "いいえ", "yesBtn": "はい", "okBtn": "OK", "closeBtn": "閉じる", "undoBtn": "元に戻す", "editBtn": "編集", "confirmModalTitleDefault": "キャンセルしますか?", "created": "作成", "emptyMessage": "データがありません", "at": "作成日", "unknown": "不明", "selected": "選択済み", "warnings": {"maxSelectionReached": "一度にナレッジベースに選択して保存できる記事は最大{max}件までです"}, "timeAgo": {"month": "{count}ヶ月前", "day": "{count}日前", "hour": "{count}時間前", "minute": "{count}分前", "justNow": "数秒前", "since": "以来 {date}"}, "errors": {"saveArticleError": "記事を保存できませんでした", "requiredField": "{fieldName}は必須項目です", "longContent": "記事内容が{maxLength}文字を超えています", "disallowedSpaceAndComma": "スペースおよびカンマは使用できません", "somethingWentWrong": {"title": "エラー", "message": "記事生成中にエラーが発生しました"}}, "form": {"name": "名前", "namePlaceholder": "名前を入力", "nameRequired": "名前は必須項目です", "nameTooLong": "名前が{maxLength}文字を超えています", "description": "説明", "descriptionPlaceholder": "説明を入力", "descriptionTooLong": "説明が{maxLength}文字を超えています", "save": "保存", "cancel": "キャンセル", "pressEnterToSave": "変更を保存するにはEnterキーを押してください"}, "notifications": {"success": {"title": "成功", "description": "リクエストが正常に処理されました"}, "error": {"title": "エラー", "description": "エラーが発生しました。"}}, "actions": {"cancel": "キャンセル", "create": "作成", "save": "保存", "edit": "編集", "delete": "削除", "move": "移動", "generate": "生成", "reset": "リセット", "apply": "適用", "doSave": "保存する", "retry": "再試行", "view": "閲覧", "unlink": "リンク解除", "link": "関連つける", "copy": "コピー", "close": "閉じる", "copied": "コピーしました", "createShortcut": "ショートカットを作成"}, "notFound": {"goToHome": "ホームに戻る", "fromUrl": "あなたの見つからないコンテンツは<FromUrl />からアクセスされました", "folderTitle": "フォルダーが見つかりません", "folderDescription": "あなたが探しているフォルダーは存在しないか、削除されています。", "kbTitle": "ナレッジベースが見つかりません", "kbDescription": "あなたが探しているナレッジベースは存在しないか、削除されています。", "articleTitle": "記事が見つかりません", "articleDescription": "あなたが探している記事は存在しないか、削除されています。", "documentTitle": "ドキュメントが見つかりません", "documentDescription": "あなたが探しているドキュメントは存在しないか、削除されています。", "jobTitle": "ジョブが見つかりません", "jobDescription": "あなたが探しているジョブは存在しないか、削除されています。", "templateTitle": "テンプレートが見つかりません", "templateDescription": "あなたが探しているテンプレートは存在しないか、削除されています。", "pageTitle": "ページが見つかりません", "pageDescription": "あなたが探しているページは存在しないか、削除されています。"}, "apiErrors": {"400": {"title": "不正なリクエスト", "message": "リクエストが無効であるか、提供できません。リクエストの本文が不正であるか、クエリ文字列が長すぎる可能性があります。"}, "401": {"title": "認証が必要", "message": "リクエストはユーザー認証が必要です。"}, "403": {"title": "禁止されたリクエスト", "message": "リクエストは理解されていますが、拒否されたか、アクセスが許可されていません。"}, "404": {"title": "見つかりません", "message": "要求されたリソースは見つかりませんでしたが、将来再利用可能になる可能性があります。"}, "500": {"title": "サーバーエラー", "message": "サーバーは内部エラーまたは設定ミスにより、リクエストを完了できませんでした。"}, "409": {"title": "競合", "message": "リクエストは、リソースの現在の状態との競合により完了できませんでした。"}}, "table": {"selectionInfo": "{selected}件選択中 / {total}件"}}