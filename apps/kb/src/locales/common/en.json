{"logout": "Logout", "backToKBList": "Go back to the Knowledge Base List", "backToKBDetail": "Go back to the {kbName}", "backBtn": "Back", "nextBtn": "Next", "search": "Search", "searchPlaceholder": "Type your search phrase and hit Enter...", "selectAll": "Select All", "deselectAll": "Deselect All", "selection": {"parentFolders": "Folders Tree", "childrenFolders": "Folders", "knowledgeBases": "Knowledge Bases"}, "noBtn": "No", "yesBtn": "Yes", "okBtn": "OK", "closeBtn": "Close", "editBtn": "Edit", "undoBtn": "Undo", "confirmModalTitleDefault": "Are you sure you want to cancel?", "created": "Created", "emptyMessage": "No data available", "at": "at", "unknown": "Unknown", "selected": "Selected", "warnings": {"maxSelectionReached": "You can only select and save up to {max} articles to the knowledge base at a time"}, "timeAgo": {"month": "{count, plural, one {# month ago} other {# months ago}}", "day": "{count, plural, one {# day ago} other {# days ago}}", "hour": "{count, plural, one {# hour ago} other {# hours ago}}", "minute": "{count, plural, one {# minute ago} other {# minutes ago}}", "justNow": "just now", "since": "since {date}"}, "errors": {"saveArticleError": "Couldn't save the article", "requiredField": "{fieldName} is required", "longContent": "The content should have at most {maxLength} characters", "disallowedSpaceAndComma": "Spaces and commas cannot be used.", "somethingWentWrong": {"title": "Error", "message": "Something went wrong while generating the article"}}, "form": {"name": "Name", "namePlaceholder": "Enter the name", "nameRequired": "Name is required", "nameTooLong": "Name should have at most {maxLength} characters", "description": "Description", "descriptionPlaceholder": "Enter the description", "descriptionTooLong": "Description should have at most {maxLength} characters", "save": "Save", "cancel": "Cancel", "pressEnterToSave": "Press Enter to save your changes"}, "notifications": {"success": {"title": "Success", "description": "Your request has been processed successfully."}, "error": {"title": "Error", "description": "An error has occurred."}}, "actions": {"cancel": "Cancel", "create": "Create", "save": "Save", "edit": "Edit", "delete": "Delete", "move": "Move", "generate": "Generate", "reset": "Reset", "apply": "Apply", "doSave": "Save", "retry": "Retry", "view": "View", "link": "Link", "unlink": "Unlink", "copy": "Copy", "close": "Close", "copied": "<PERSON>pied", "createShortcut": "Create Shortcut"}, "notFound": {"goToHome": "Go to Home", "fromUrl": "Your not found content was accessed from <FromUrl />", "folderTitle": "Folder Not Found", "folderDescription": "The folder you are looking for does not exist or has been deleted.", "kbTitle": "Knowledge Base Not Found", "kbDescription": "The knowledge base you are looking for does not exist or has been deleted.", "articleTitle": "Article Not Found", "articleDescription": "The article you are looking for does not exist or has been deleted.", "documentTitle": "Document Not Found", "documentDescription": "The document you are looking for does not exist or has been deleted.", "jobTitle": "Job Not Found", "jobDescription": "The job you are looking for does not exist or has been completed.", "templateTitle": "Template Not Found", "templateDescription": "The template you are looking for does not exist or has been deleted.", "pageTitle": "Page Not Found", "pageDescription": "The page you are looking for does not exist or has been deleted."}, "apiErrors": {"400": {"title": "Bad Request", "message": "The request was invalid or cannot be served. The request body may be malformed or the query string may be too long."}, "401": {"title": "Unauthorized", "message": "The request requires user authentication."}, "403": {"title": "Forbidden", "message": "The request is understood, but it has been refused or access is not allowed."}, "404": {"title": "Not Found", "message": "The requested resource could not be found but may be available again in the future."}, "500": {"title": "Internal Server Error", "message": "The server encountered an internal error or misconfiguration and was unable to complete the request."}, "409": {"title": "Conflict", "message": "The request could not be completed due to a conflict with the current state of the resource."}}, "table": {"selectionInfo": "{selected} items selected / {total} items"}}