import { <PERSON><PERSON>Wrapper } from '@/utils/unitTest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { ArticlePublishDropdown, PublishStatus } from './ArticlePublishDropdown';

// Mock the ScheduleModal component
vi.mock('../KBScheduleModal/ScheduleModal', () => ({
  default: vi.fn(({ opened, onClose, onSchedule, title, description, scheduleText }) =>
    opened ? (
      <div data-testid='schedule-modal'>
        <div data-testid='modal-title'>{title}</div>
        <div data-testid='modal-description'>{description}</div>
        <button type='button' data-testid='modal-close' onClick={onClose}>
          Close
        </button>
        <button
          type='button'
          data-testid='modal-schedule'
          onClick={() => onSchedule?.(new Date('2024-12-31T10:00:00Z'))}
        >
          {scheduleText}
        </button>
      </div>
    ) : null
  ),
}));

// Mock @tolgee/react
vi.mock('@tolgee/react', () => ({
  useTranslate: (_namespace?: string) => ({
    t: (key: string, defaultValue?: string) => {
      const translations: Record<string, string> = {
        'publishStatus.draft': 'Draft',
        'publishStatus.published': 'Published',
        'publishStatus.schedulePublish': 'Schedule Publish',
        'publishStatus.scheduleUnpublish': 'Schedule Un-publish',
        'articlePublishActions.draft': 'Draft',
        'articlePublishActions.published': 'Published',
        'articlePublishActions.schedulePublish': 'Schedule Publish',
        'articlePublishActions.scheduleUnpublish': 'Schedule Un-publish',
        'schedulePublish.title': 'Schedule Publish',
        'schedulePublish.description':
          'Choose the date and time you want this article to be published. It will automatically go live at the scheduled time.',
        'schedulePublish.button': 'Schedule Publish',
        'scheduleUnpublish.title': 'Schedule Un-publish',
        'scheduleUnpublish.description':
          'Select the date and time to automatically un-publish this article. After that time, it will no longer be visible and will return to Draft status.',
        'scheduleUnpublish.button': 'Schedule Un-publish',
      };
      return translations[key] || defaultValue || key;
    },
  }),
}));

// Test constants
const MOCK_ON_STATUS_CHANGE = vi.fn();
const MOCK_ON_SCHEDULE = vi.fn();

// Default props
const DEFAULT_PROPS = {
  status: PublishStatus.DRAFT,
  onStatusChange: MOCK_ON_STATUS_CHANGE,
  onSchedule: MOCK_ON_SCHEDULE,
};

describe('ArticlePublishDropdown', () => {
  beforeEach(() => {
    vi.resetAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render with draft status', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.DRAFT} />
        </MantineWrapper>
      );

      expect(screen.getByText('publishStatus.draft')).toBeInTheDocument();
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should render with published status', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.PUBLISHED} />
        </MantineWrapper>
      );

      expect(screen.getByText('publishStatus.published')).toBeInTheDocument();
    });

    it('should render with schedule publish status', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.SCHEDULE_PUBLISH} />
        </MantineWrapper>
      );

      expect(screen.getByText('publishStatus.schedulePublish')).toBeInTheDocument();
    });

    it('should render with schedule unpublish status', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.SCHEDULE_UNPUBLISH} />
        </MantineWrapper>
      );

      expect(screen.getByText('publishStatus.scheduleUnpublish')).toBeInTheDocument();
    });
  });

  describe('Dropdown Menu Interaction', () => {
    it('should open dropdown menu when clicked', async () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      fireEvent.click(dropdownButton);

      await waitFor(() => {
        const menuItems = screen.getAllByRole('menuitem');
        expect(menuItems).toHaveLength(2); // Only Draft and Published
        expect(
          screen.getByRole('menuitem', { name: 'articlePublishActions.draft' })
        ).toBeInTheDocument();
        expect(
          screen.getByRole('menuitem', { name: 'articlePublishActions.published' })
        ).toBeInTheDocument();
      });
    });

    it('should call onStatusChange when selecting draft', async () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.PUBLISHED} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      fireEvent.click(dropdownButton);

      await waitFor(() => {
        expect(
          screen.getByRole('menuitem', { name: 'articlePublishActions.draft' })
        ).toBeInTheDocument();
      });

      // Find and click the Draft menu item
      const draftMenuItem = screen.getByRole('menuitem', { name: 'articlePublishActions.draft' });
      fireEvent.click(draftMenuItem);

      expect(MOCK_ON_STATUS_CHANGE).toHaveBeenCalledWith(PublishStatus.DRAFT);
    });

    it('should call onStatusChange when selecting published', async () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.DRAFT} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      fireEvent.click(dropdownButton);

      await waitFor(() => {
        expect(
          screen.getByRole('menuitem', { name: 'articlePublishActions.published' })
        ).toBeInTheDocument();
      });

      // Find and click the Published menu item
      const publishedMenuItem = screen.getByRole('menuitem', {
        name: 'articlePublishActions.published',
      });
      fireEvent.click(publishedMenuItem);

      expect(MOCK_ON_STATUS_CHANGE).toHaveBeenCalledWith(PublishStatus.PUBLISHED);
    });
  });

  describe('Schedule Status Display', () => {
    it('should display schedule publish status correctly', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.SCHEDULE_PUBLISH} />
        </MantineWrapper>
      );

      expect(screen.getByText('publishStatus.schedulePublish')).toBeInTheDocument();
    });

    it('should display schedule unpublish status correctly', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.SCHEDULE_UNPUBLISH} />
        </MantineWrapper>
      );

      expect(screen.getByText('publishStatus.scheduleUnpublish')).toBeInTheDocument();
    });
  });

  describe('Disabled State', () => {
    it('should disable dropdown when disabled prop is true', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} disabled={true} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      expect(dropdownButton).toBeDisabled();
    });

    it('should not open dropdown when disabled', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} disabled={true} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      fireEvent.click(dropdownButton);

      // Should not show dropdown options
      expect(screen.queryByRole('menuitem')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should not call onStatusChange when selecting current status', async () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} status={PublishStatus.DRAFT} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      fireEvent.click(dropdownButton);

      await waitFor(() => {
        // Wait for the dropdown menu to be populated with items
        const menuItems = screen.getAllByRole('menuitem');
        expect(menuItems).toHaveLength(2); // Only Draft and Published
      });

      // Click on Draft menu item (which is the current status)
      const draftMenuItem = screen.getByRole('menuitem', { name: 'articlePublishActions.draft' });
      fireEvent.click(draftMenuItem);

      expect(MOCK_ON_STATUS_CHANGE).not.toHaveBeenCalled();
    });

    it('should handle missing onSchedule callback gracefully', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown
            status={PublishStatus.DRAFT}
            onStatusChange={MOCK_ON_STATUS_CHANGE}
          />
        </MantineWrapper>
      );

      // Component should render without errors even without onSchedule prop
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} className='custom-class' />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      expect(dropdownButton).toHaveClass('custom-class');
    });
  });

  describe('Accessibility', () => {
    it('should have proper button role', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} />
        </MantineWrapper>
      );

      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('should have proper button type', () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      expect(dropdownButton).toHaveAttribute('type', 'button');
    });

    it('should have proper aria attributes when opened', async () => {
      render(
        <MantineWrapper>
          <ArticlePublishDropdown {...DEFAULT_PROPS} />
        </MantineWrapper>
      );

      const dropdownButton = screen.getByRole('button');
      fireEvent.click(dropdownButton);

      await waitFor(() => {
        expect(dropdownButton).toHaveAttribute('aria-expanded', 'true');
        expect(dropdownButton).toHaveAttribute('aria-haspopup', 'menu');
      });
    });
  });
});
