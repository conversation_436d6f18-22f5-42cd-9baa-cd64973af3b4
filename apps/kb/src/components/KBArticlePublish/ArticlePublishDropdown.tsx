import { Menu, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useState } from 'react';
import ScheduleModal from '../KBScheduleModal/ScheduleModal';

// Define the publish status options
export enum PublishStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  SCHEDULE_PUBLISH = 'schedule_publish',
  SCHEDULE_UNPUBLISH = 'schedule_unpublish',
}

export interface ArticlePublishDropdownProps {
  /** Current publish status */
  status: PublishStatus;
  /** Callback when status changes */
  onStatusChange: (status: PublishStatus) => void;
  /** Callback when schedule is set (status, datetime) */
  onSchedule?: (status: PublishStatus, dateTime: Date) => void;
  /** Whether the dropdown is disabled */
  disabled?: boolean;
  /** Additional CSS class */
  className?: string;
}

const useStyles = createStyles((theme) => ({
  dropdownTarget: {
    display: 'flex',
    alignItems: 'center',
    gap: rem(6),
    padding: `${rem(4)} ${rem(12)}`,
    borderRadius: rem(16),
    border: 'none',
    cursor: 'pointer',
    fontSize: rem(12),
    fontWeight: 500,
    textTransform: 'none',
    transition: 'all 0.2s ease',
    minWidth: rem(70),
    height: rem(38),

    '&:hover': {
      opacity: 0.8,
    },

    '&:disabled': {
      cursor: 'not-allowed',
      opacity: 0.5,
    },
  },

  draft: {
    backgroundColor: theme.colors.decaYellow[0],
    color: theme.colors.decaYellow[9],

    '&:hover': {
      backgroundColor: theme.colors.decaYellow[1],
    },
  },

  published: {
    backgroundColor: theme.colors.decaGreen[0],
    color: theme.colors.decaGreen[9],

    '&:hover': {
      backgroundColor: theme.colors.decaGreen[1],
    },
  },

  schedulePublish: {
    backgroundColor: theme.colors.decaBlue[0],
    color: theme.colors.decaBlue[9],

    '&:hover': {
      backgroundColor: theme.colors.decaBlue[1],
    },
  },

  scheduleUnpublish: {
    backgroundColor: theme.colors.decaViolet[0],
    color: theme.colors.decaViolet[9],

    '&:hover': {
      backgroundColor: theme.colors.decaViolet[1],
    },
  },

  chevron: {
    width: rem(14),
    height: rem(14),
    transition: 'transform 0.2s ease',
  },

  chevronRotated: {
    transform: 'rotate(180deg)',
  },

  dropdown: {
    backgroundColor: theme.white,
    boxShadow: '0px 3px 12px rgba(13, 17, 54, 0.06)',
    minWidth: rem(210),
    color: '#333333',
    border: `1px solid ${theme.colors.decaLight[1]}`,
    borderRadius: rem(4),
  },

  menuItem: {
    padding: `${rem(8)} ${rem(12)}`,
    fontSize: rem(14),
    fontWeight: 500,
    color: '#333333',
    display: 'flex',
    alignItems: 'center',

    '&:hover': {
      backgroundColor: '#EEEEF1',
      borderRadius: rem(4),
      color: '#333333',
    },
  },
}));

/**
 * Get the display text for a publish status
 */
const getStatusText = (status: PublishStatus, t: (key: string) => string): string => {
  switch (status) {
    case PublishStatus.DRAFT:
      return t('publishStatus.draft');
    case PublishStatus.PUBLISHED:
      return t('publishStatus.published');
    case PublishStatus.SCHEDULE_PUBLISH:
      return t('publishStatus.schedulePublish');
    case PublishStatus.SCHEDULE_UNPUBLISH:
      return t('publishStatus.scheduleUnpublish');
    default:
      return t('publishStatus.draft');
  }
};

/**
 * Get the CSS class for a publish status
 */
const getStatusClass = (status: PublishStatus, classes: any): string => {
  switch (status) {
    case PublishStatus.DRAFT:
      return classes.draft;
    case PublishStatus.PUBLISHED:
      return classes.published;
    case PublishStatus.SCHEDULE_PUBLISH:
      return classes.schedulePublish;
    case PublishStatus.SCHEDULE_UNPUBLISH:
      return classes.scheduleUnpublish;
    default:
      return classes.draft;
  }
};

/**
 * ArticlePublishDropdown component displays the current publish status as a badge-like dropdown
 * with options to change the status: Draft, Publish, Schedule Publish, Schedule Unpublish
 */
export const ArticlePublishDropdown: React.FC<ArticlePublishDropdownProps> = ({
  status,
  onStatusChange,
  onSchedule,
  disabled = false,
  className = '',
}) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('article');

  // State for schedule modal
  const [scheduleModalOpened, setScheduleModalOpened] = useState(false);
  const [scheduleType, setScheduleType] = useState<PublishStatus | null>(null);

  const handleStatusChange = useCallback(
    (newStatus: PublishStatus) => {
      if (newStatus !== status) {
        // For schedule actions, open modal instead of direct status change
        if (
          newStatus === PublishStatus.SCHEDULE_PUBLISH ||
          newStatus === PublishStatus.SCHEDULE_UNPUBLISH
        ) {
          setScheduleType(newStatus);
          setScheduleModalOpened(true);
        } else {
          onStatusChange(newStatus);
        }
      }
    },
    [status, onStatusChange]
  );

  // Handle schedule modal close
  const handleScheduleModalClose = useCallback(() => {
    setScheduleModalOpened(false);
    setScheduleType(null);
  }, []);

  // Handle schedule confirmation
  const handleScheduleConfirm = useCallback(
    (dateTime: Date) => {
      if (scheduleType && onSchedule) {
        onSchedule(scheduleType, dateTime);
      }
      handleScheduleModalClose();
    },
    [scheduleType, onSchedule, handleScheduleModalClose]
  );

  // Define available status options
  const statusOptions = [
    { value: PublishStatus.DRAFT, label: t('articlePublishActions.draft') },
    { value: PublishStatus.PUBLISHED, label: t('articlePublishActions.published') },
  ];

  // Get modal props based on schedule type
  const getModalProps = () => {
    if (scheduleType === PublishStatus.SCHEDULE_PUBLISH) {
      return {
        title: t('schedulePublish.title', 'Schedule Publish'),
        description: t(
          'schedulePublish.description',
          'Choose the date and time you want this article to be published. It will automatically go live at the scheduled time.'
        ),
        scheduleText: t('schedulePublish.button', 'Schedule Publish'),
      };
    }
    if (scheduleType === PublishStatus.SCHEDULE_UNPUBLISH) {
      return {
        title: t('scheduleUnpublish.title', 'Schedule Un-publish'),
        description: t(
          'scheduleUnpublish.description',
          'Select the date and time to automatically un-publish this article. After that time, it will no longer be visible and will return to Draft status.'
        ),
        scheduleText: t('scheduleUnpublish.button', 'Schedule Un-publish'),
      };
    }
    return {};
  };

  return (
    <>
      <Menu
        position='bottom-start'
        shadow='sm'
        classNames={{
          dropdown: classes.dropdown,
          item: classes.menuItem,
        }}
        disabled={disabled}
      >
        <Menu.Target>
          <button
            className={cx(classes.dropdownTarget, getStatusClass(status, classes), className)}
            disabled={disabled}
            type='button'
          >
            <span>{getStatusText(status, t)}</span>
            <IconChevronDown className={classes.chevron} />
          </button>
        </Menu.Target>

        <Menu.Dropdown>
          {statusOptions.map((option) => (
            <Menu.Item key={option.value} onClick={() => handleStatusChange(option.value)}>
              {option.label}
            </Menu.Item>
          ))}
        </Menu.Dropdown>
      </Menu>

      {/* Schedule Modal */}
      <ScheduleModal
        opened={scheduleModalOpened}
        onClose={handleScheduleModalClose}
        onSchedule={handleScheduleConfirm}
        minDate={new Date()}
        {...getModalProps()}
      />
    </>
  );
};

export default ArticlePublishDropdown;
