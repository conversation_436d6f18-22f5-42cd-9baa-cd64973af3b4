import { MantineWrapper } from '@/utils/unitTest';
import { render, screen } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import TimeAgo from './index';

// Mock Tolgee - use simple implementation that matches the codebase pattern
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, options?: { count?: number; date?: string }) => {
      // Return the key as-is for testing, similar to other tests in codebase
      if (options?.count !== undefined) {
        return `${key}:${options.count}`;
      }
      if (options?.date !== undefined) {
        return `${key}:${options.date}`;
      }
      return key;
    },
  }),
}));

describe('TimeAgo Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render with default className', () => {
      render(<TimeAgo dateTime={new Date()} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
      expect(element).toHaveClass('mantine-Text-root');
    });

    it('should apply custom className', () => {
      const customClass = 'custom-class';
      render(<TimeAgo dateTime={new Date()} className={customClass} />, {
        wrapper: MantineWrapper,
      });

      const element = screen.getByText(/timeAgo/);
      expect(element).toHaveClass(customClass);
      expect(element).toHaveClass('mantine-Text-root');
    });
  });

  describe('Date Input Handling', () => {
    it('should handle Date objects', () => {
      const date = new Date('2024-01-01T00:00:00.000Z');
      render(<TimeAgo dateTime={date} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });

    it('should handle date strings', () => {
      const dateString = '2024-01-01T00:00:00.000Z';
      render(<TimeAgo dateTime={dateString} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });

    it('should handle invalid date strings gracefully', () => {
      const invalidDate = 'invalid-date';
      render(<TimeAgo dateTime={invalidDate} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });

    it('should handle null/undefined gracefully', () => {
      render(<TimeAgo dateTime={null as any} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });
  });

  describe('Time Difference Calculation', () => {
    it('should display just now for recent times', () => {
      const now = new Date();
      const recent = new Date(now.getTime() - 30 * 1000); // 30 seconds ago

      render(<TimeAgo dateTime={recent} />, { wrapper: MantineWrapper });

      const element = screen.getByText('timeAgo.justNow');
      expect(element).toBeInTheDocument();
    });

    it('should display minutes for times within an hour', () => {
      const now = new Date();
      const minutesAgo = new Date(now.getTime() - 15 * 60 * 1000); // 15 minutes ago

      render(<TimeAgo dateTime={minutesAgo} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo\.minute/);
      expect(element).toBeInTheDocument();
    });

    it('should display hours for times within a day', () => {
      const now = new Date();
      const hoursAgo = new Date(now.getTime() - 3 * 60 * 60 * 1000); // 3 hours ago

      render(<TimeAgo dateTime={hoursAgo} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo\.hour/);
      expect(element).toBeInTheDocument();
    });

    it('should display days for times within a month', () => {
      const now = new Date();
      const daysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000); // 3 days ago

      render(<TimeAgo dateTime={daysAgo} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo\.day/);
      expect(element).toBeInTheDocument();
    });

    it('should display months for times within a year', () => {
      const now = new Date();
      const monthsAgo = new Date(now.getTime() - 2 * 30 * 24 * 60 * 60 * 1000); // ~2 months ago

      render(<TimeAgo dateTime={monthsAgo} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo\.month/);
      expect(element).toBeInTheDocument();
    });

    it('should display formatted date for times over a year', () => {
      const now = new Date();
      const yearsAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000); // 1 year ago

      render(<TimeAgo dateTime={yearsAgo} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo\.since/);
      expect(element).toBeInTheDocument();
    });
  });

  describe('Component Performance', () => {
    it('should not crash with extreme dates', () => {
      const veryOldDate = new Date('1900-01-01T00:00:00.000Z');
      const veryFutureDate = new Date('2100-01-01T00:00:00.000Z');

      expect(() => {
        render(<TimeAgo dateTime={veryOldDate} />, { wrapper: MantineWrapper });
      }).not.toThrow();

      expect(() => {
        render(<TimeAgo dateTime={veryFutureDate} />, { wrapper: MantineWrapper });
      }).not.toThrow();
    });

    it('should handle rapid re-renders', () => {
      const { rerender } = render(<TimeAgo dateTime={new Date()} />, { wrapper: MantineWrapper });

      // Rapid re-renders should not throw errors
      for (let i = 0; i < 5; i++) {
        rerender(<TimeAgo dateTime={new Date()} />);
      }

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });
  });

  describe('Translation Integration', () => {
    it('should use the correct translation keys', () => {
      const testCases = [
        { time: new Date(), expectedKey: 'timeAgo.justNow' },
        { time: new Date(Date.now() - 15 * 60 * 1000), expectedPattern: /timeAgo\.minute/ },
        { time: new Date(Date.now() - 3 * 60 * 60 * 1000), expectedPattern: /timeAgo\.hour/ },
        { time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), expectedPattern: /timeAgo\.day/ },
      ];

      testCases.forEach(({ time, expectedKey, expectedPattern }) => {
        const { unmount } = render(<TimeAgo dateTime={time} />, { wrapper: MantineWrapper });

        if (expectedKey) {
          const element = screen.getByText(expectedKey);
          expect(element).toBeInTheDocument();
        } else if (expectedPattern) {
          const element = screen.getByText(expectedPattern);
          expect(element).toBeInTheDocument();
        }

        unmount();
      });
    });
  });

  describe('Edge Cases', () => {
    it('should handle timezone differences', () => {
      const utcDate = new Date('2024-01-01T00:00:00.000Z');

      render(<TimeAgo dateTime={utcDate} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });

    it('should handle daylight saving time transitions', () => {
      // Test around DST transition dates
      const dstDate = new Date('2024-03-10T07:00:00.000Z'); // Common DST date

      render(<TimeAgo dateTime={dstDate} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });

    it('should handle leap year dates', () => {
      const leapYearDate = new Date('2024-02-29T00:00:00.000Z');

      render(<TimeAgo dateTime={leapYearDate} />, { wrapper: MantineWrapper });

      const element = screen.getByText(/timeAgo/);
      expect(element).toBeInTheDocument();
    });
  });
});
