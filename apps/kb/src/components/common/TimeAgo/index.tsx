import { Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo } from 'react';

const useStyles = createStyles((theme) => ({
  root: {
    fontSize: rem(12),
    lineHeight: rem(12),
    color: theme.colors.decaGrey[2],
  },
}));

interface TimeAgoProps {
  /** The date/time to display relative to now */
  dateTime: Date | string;
  /** Additional CSS class name */
  className?: string;
}

/**
 * TimeAgo component displays a human-readable time difference from now
 * Performance optimizations:
 * - Memoized time calculation to avoid unnecessary recalculations
 * - Optimized date parsing and validation
 * - Stable translation function reference
 */
const TimeAgo: React.FC<TimeAgoProps> = ({ dateTime, className = '' }) => {
  const { t } = useTranslate('common');
  const { classes, cx } = useStyles();

  // Memoize date parsing to avoid repeated parsing
  const parsedDate = useMemo(() => {
    try {
      const date = new Date(dateTime);
      return Number.isNaN(date.getTime()) ? null : date;
    } catch {
      return null;
    }
  }, [dateTime]);

  // Memoize the time calculation function for better performance
  const calculateTimeAgo = useCallback((date: Date, translationFn: typeof t) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
    const diffInMonths = Math.floor(diffInDays / 30);

    // If time is in the future or less than 1 minute ago, show "just now"
    if (diffInMinutes < 1) {
      return translationFn('timeAgo.justNow');
    }

    // If less than 1 hour ago, show minutes
    if (diffInHours < 1) {
      return translationFn('timeAgo.minute', { count: diffInMinutes });
    }

    // If less than 1 day ago, show hours
    if (diffInDays < 1) {
      return translationFn('timeAgo.hour', { count: diffInHours });
    }

    // If less than 30 days ago, show days
    if (diffInDays < 30) {
      return translationFn('timeAgo.day', { count: diffInDays });
    }

    // If less than 12 months ago, show months
    if (diffInMonths < 12) {
      return translationFn('timeAgo.month', { count: diffInMonths });
    }

    // If more than 12 months ago, show the actual date
    return translationFn('timeAgo.since', { date: date.toLocaleDateString() });
  }, []);

  // Memoize the final time ago text calculation
  const timeAgoText = useMemo(() => {
    if (!parsedDate) {
      return t('timeAgo.justNow'); // Fallback for invalid dates
    }

    return calculateTimeAgo(parsedDate, t);
  }, [parsedDate, calculateTimeAgo, t]);

  return <Text className={cx(classes.root, className)}>{timeAgoText}</Text>;
};

export default TimeAgo;
