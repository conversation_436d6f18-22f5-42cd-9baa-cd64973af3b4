import { use<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/hooks';
import { GenArticleAPI } from '@/services/api/v2/genArticle';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider, emotionTransform } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { fireEvent, render, screen, waitFor, within } from '@testing-library/react';
// Removed unused import: JobArticleDownloadFormat
import type React from 'react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { JobArticleDownload } from './JobArticleDownload';

// Mock Tolgee hooks before importing components
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual<object>('@tolgee/react');

  return {
    ...actual,
    TolgeeProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
    useTranslate: () => ({
      t: (key: string, options?: any) => {
        // Handle namespaced keys by stripping the namespace prefix
        let translationKey = key;
        if (key.includes(':')) {
          translationKey = key.split(':')[1];
        }

        // Map translation keys to expected values
        const translations: Record<string, string> = {
          'button.downloadGeneratedArticles': 'Download Articles',
          'button.generatedArticlesCSV': 'Download CSV',
          'button.generatedArticlesExcel': 'Download Excel',
        };

        const result = translations[translationKey] || translationKey;

        if (options && typeof options === 'object') {
          let translatedResult = result;
          Object.entries(options).forEach(([optionKey, value]) => {
            translatedResult = translatedResult.replace(`{{${optionKey}}}`, String(value));
          });
          return translatedResult;
        }
        return result;
      },
      i18n: { language: 'en' },
    }),
    useTolgee: () => ({
      t: (key: string) => key,
      addPlugin: vi.fn(),
      use: vi.fn(),
      init: vi.fn(),
      changeLanguage: vi.fn(),
      getLanguage: vi.fn(() => 'en'),
      isLoaded: vi.fn(() => true),
      isLoading: vi.fn(() => false),
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
      addActiveNs: vi.fn(),
    }),
    T: ({ keyName, params }: { keyName: string; params?: any }) => {
      if (params && typeof params === 'object') {
        return (
          <span>
            {keyName}
            {Object.entries(params).map(([key, value]) => (
              <span key={key}>{String(value)}</span>
            ))}
          </span>
        );
      }
      return <span>{keyName}</span>;
    },
  };
});

// Constants and mock data
const MOCK_JOB_ID = 'job-123';
const MOCK_FILENAME = 'articles-job-123';
const MOCK_BLOB = new Blob(['test data'], { type: 'text/csv' });
const MOCK_RESPONSE = {
  data: MOCK_BLOB,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {} as any,
};
const MOCK_URL = 'blob:mock-url';

// Mock dependencies
vi.mock('@/services/api/v2/genArticle', () => ({
  GenArticleAPI: {
    download: vi.fn().mockResolvedValue({
      data: new Blob(['test data'], { type: 'text/csv' }),
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {} as any,
    }),
  },
}));

// Mock hooks from @/hooks
vi.mock('@/hooks', () => ({
  useApiHandler: vi.fn(),
  useFileUpload: vi.fn().mockReturnValue({
    uploadFiles: vi.fn(),
    uploaders: [],
    validateFiles: vi.fn(),
  }),
}));

// Mock UploaderContextProvider
vi.mock('@/contexts/UploaderContext', () => ({
  UploaderContextProvider: ({ children }) => children,
}));

// Mock URL.createObjectURL and document.createElement
const mockCreateObjectURL = vi.fn().mockReturnValue(MOCK_URL);
const mockRevokeObjectURL = vi.fn();
const mockClick = vi.fn();
const mockRemove = vi.fn();
const mockAnchorElement = {
  href: '',
  download: '',
  click: mockClick,
  remove: mockRemove,
};

// Store original createElement method for later restoration
let originalCreateElement: typeof document.createElement;

// Setup global mocks
beforeEach(() => {
  global.URL.createObjectURL = mockCreateObjectURL;
  global.URL.revokeObjectURL = mockRevokeObjectURL;

  // Reset mockAnchorElement for each test
  mockAnchorElement.href = '';
  mockAnchorElement.download = '';

  // Reset click and remove mocks
  mockClick.mockClear();
  mockRemove.mockClear();
  mockCreateObjectURL.mockClear();
  mockRevokeObjectURL.mockClear();

  // Store original createElement method
  originalCreateElement = document.createElement;

  document.createElement = vi.fn().mockImplementation((tagName) => {
    if (tagName === 'a') {
      return mockAnchorElement;
    }
    return originalCreateElement.call(document, tagName);
  });

  // Mock useApiHandler hook
  vi.mocked(useApiHandler).mockReturnValue({
    handleApiRequest: vi.fn().mockImplementation((apiCall) => apiCall()),
    API_RESPONSE_STATUS: {
      SUCCESS: 'success',
      ERROR: 'error',
    },
  });

  // Reset GenArticleAPI.download mock
  vi.mocked(GenArticleAPI.download).mockResolvedValue(MOCK_RESPONSE);

  // Reset mockOnDownload mock
  mockOnDownload.mockClear();
});

afterEach(() => {
  vi.resetAllMocks();
  mockOnDownload.mockClear();
  document.createElement = originalCreateElement; // Restore original
});

// Create a test wrapper with Mantine Provider
const TestWrapper = ({ children }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>{children}</MantineEmotionProvider>
  </MantineProvider>
);

// Mock onDownload function for testing
const mockOnDownload = vi.fn().mockImplementation((id, _jobTitle, format) => {
  if (!id) return;

  return GenArticleAPI.download(id, format, { responseType: 'blob' })
    .then((response) => {
      // Use the mocked URL directly to ensure consistency
      // URL.createObjectURL is already mocked to return MOCK_URL
      const url = URL.createObjectURL(response.data);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${MOCK_FILENAME}.${format === 'csv' ? 'csv' : 'xlsx'}`;
      a.click();
      a.remove();
      URL.revokeObjectURL(MOCK_URL);
    })
    .catch((error) => {
      console.error('Error downloading:', error);
    });
});

// Helper to get dropdown elements
const clickCSVOption = async () => {
  await waitFor(() => {
    const dropdownPortal = document.querySelector('[data-portal="true"]');
    expect(dropdownPortal).not.toBeNull();
  });

  const dropdownPortal = document.querySelector('[data-portal="true"]');
  // Ensure the dropdown has content before trying to find the option
  await waitFor(() => {
    expect(dropdownPortal?.textContent).toContain('Download CSV');
  });

  const csvOption = within(dropdownPortal as HTMLElement).getByText('Download CSV');
  fireEvent.click(csvOption);
};

const clickExcelOption = async () => {
  await waitFor(() => {
    const dropdownPortal = document.querySelector('[data-portal="true"]');
    expect(dropdownPortal).not.toBeNull();
  });

  const dropdownPortal = document.querySelector('[data-portal="true"]');
  // Ensure the dropdown has content before trying to find the option
  await waitFor(() => {
    expect(dropdownPortal?.textContent).toContain('Download Excel');
  });

  const excelOption = within(dropdownPortal as HTMLElement).getByText('Download Excel');
  fireEvent.click(excelOption);
};

describe('JobArticleDownload', () => {
  it('renders the download buttons correctly', async () => {
    render(
      <JobArticleDownload jobId={MOCK_JOB_ID} jobTitle='Test Job' onDownload={mockOnDownload} />,
      { wrapper: TestWrapper }
    );

    expect(screen.getByText('Download Articles')).toBeInTheDocument();
    // Click to open the menu
    fireEvent.click(screen.getByText('Download Articles'));

    // The dropdown items are rendered in a portal, wait for them to appear
    await waitFor(() => {
      const dropdownPortal = document.querySelector('[data-portal="true"]');
      expect(dropdownPortal).not.toBeNull();

      // Use getByText instead of getByRole for more reliable selection
      const csvOption = within(dropdownPortal as HTMLElement).getByText('Download CSV');
      const excelOption = within(dropdownPortal as HTMLElement).getByText('Download Excel');

      expect(csvOption).toBeInTheDocument();
      expect(excelOption).toBeInTheDocument();
    });
  });

  it('downloads CSV file when CSV button is clicked', async () => {
    // Setup
    vi.mocked(GenArticleAPI.download).mockResolvedValueOnce(MOCK_RESPONSE);

    // Render
    render(
      <JobArticleDownload jobId={MOCK_JOB_ID} jobTitle='Test Job' onDownload={mockOnDownload} />,
      { wrapper: TestWrapper }
    );

    // Open menu first
    fireEvent.click(screen.getByText('Download Articles'));

    // Act - Click CSV option
    await clickCSVOption();

    // Assert
    await waitFor(() => {
      expect(mockOnDownload).toHaveBeenCalledWith(MOCK_JOB_ID, 'Test Job', 'csv');
    });
  });

  it('downloads Excel file when Excel button is clicked', async () => {
    // Setup
    vi.mocked(GenArticleAPI.download).mockResolvedValueOnce(MOCK_RESPONSE);

    // Render
    render(
      <JobArticleDownload jobId={MOCK_JOB_ID} jobTitle='Test Job' onDownload={mockOnDownload} />,
      { wrapper: TestWrapper }
    );

    // Open menu first
    fireEvent.click(screen.getByText('Download Articles'));

    // Act - Click Excel option
    await clickExcelOption();

    // Assert
    await waitFor(() => {
      expect(mockOnDownload).toHaveBeenCalledWith(MOCK_JOB_ID, 'Test Job', 'excel');
    });
  });

  it('handles download errors gracefully', async () => {
    // Setup
    const mockError = new Error('Download failed');
    vi.mocked(GenArticleAPI.download).mockRejectedValueOnce(mockError);
    const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    // Render
    render(
      <JobArticleDownload jobId={MOCK_JOB_ID} jobTitle='Test Job' onDownload={mockOnDownload} />,
      { wrapper: TestWrapper }
    );

    // Open menu first
    fireEvent.click(screen.getByText('Download Articles'));

    // Act
    await clickCSVOption();

    // Assert
    await waitFor(() => {
      expect(mockOnDownload).toHaveBeenCalledWith(MOCK_JOB_ID, 'Test Job', 'csv');
    });

    // Clean up
    consoleErrorSpy.mockRestore();
  });

  it('calls onDownload with correct parameters for different formats', async () => {
    // Render
    render(
      <JobArticleDownload jobId={MOCK_JOB_ID} jobTitle='Test Job' onDownload={mockOnDownload} />,
      { wrapper: TestWrapper }
    );

    // Open menu first
    fireEvent.click(screen.getByText('Download Articles'));

    // Act - Download CSV
    await clickCSVOption();

    // Assert
    await waitFor(() => {
      expect(mockOnDownload).toHaveBeenCalledWith(MOCK_JOB_ID, 'Test Job', 'csv');
    });

    // Reset mocks
    mockOnDownload.mockClear();

    // Open menu again
    fireEvent.click(screen.getByText('Download Articles'));

    // Act - Download Excel
    await clickExcelOption();

    // Assert
    await waitFor(() => {
      expect(mockOnDownload).toHaveBeenCalledWith(MOCK_JOB_ID, 'Test Job', 'excel');
    });
  });

  it('disables buttons when jobId is not provided', () => {
    // Create a mock onDownload function for this test
    const mockOnDownload = vi.fn();

    // Render without jobId but with onDownload prop
    render(<JobArticleDownload jobId='' jobTitle='Test Job' onDownload={mockOnDownload} />, {
      wrapper: TestWrapper,
    });

    // Assert button is disabled - check if button is disabled directly
    const button = screen.getByText('Download Articles').closest('button');
    expect(button).toHaveAttribute('disabled');
  });
});
