import { Box, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useCallback, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import { useExportJob } from '@/components/KBExport';
import { AccessIcon, FileStatusIcon, IllustrationIcon, KBMenu } from '@/components/common';
import { FOLDER_CARD_MENU_WIDTH, KB_CARD_MENU_WIDTH } from '@/constants/kb';
import { DocumentAPI } from '@/services/api/v2/document';
import {
  type DocumentFile,
  type Explorer,
  FILE_UPLOAD_STATUS,
  type Folder,
  type KnowledgeBase,
} from '@/types';
import { isDocument, isFolder } from '@/utils/tree';

interface GridCardProps {
  item: Folder | Explorer;
  onEdit: (item: Folder | Explorer) => void;
  onDelete: (item: Folder | Explorer) => void;
  onDownload: (item: Folder | Explorer) => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    position: 'relative',
  },
  item: {
    minWidth: rem(200),
    width: '100%',
    height: rem(220),
    color: theme.colors.decaNavy[5],
    backgroundColor: theme.colors.decaLight[0],
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    gap: rem(8),
    border: `1px solid ${theme.colors.decaLight[2]}`,
    borderRadius: rem(10),
    padding: `0 ${rem(20)}`,
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  menu: {
    position: 'absolute',
    top: rem(10),
    right: rem(10),
  },
  name: {
    wordBreak: 'break-word',
  },
  scope: {
    position: 'absolute',
    top: rem(10),
    left: rem(10),
    display: 'flex',
    alignItems: 'center',
    gap: rem(8),
  },
  iconBox: {
    width: rem(32),
    height: rem(32),
    borderRadius: rem('50%'),
    backgroundColor: theme.colors.decaLight[1],
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    '& svg': {
      color: theme.colors.decaGrey[3],
      width: rem(18),
      height: rem(18),
    },
  },
  illustrationIcon: {
    maxWidth: rem(130),
    maxHeight: rem(90),
    borderRadius: rem(10),
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
    '& img': {
      width: 'auto',
      height: 'auto',
      maxWidth: '100%',
      maxHeight: '100%',
    },
  },
}));

const GridCard: React.FC<GridCardProps> = ({ item, onEdit, onDelete, onDownload }) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['home', 'export']);
  const [currentImageUrl, setCurrentImageUrl] = useState<string | undefined>();
  const [isLoading, setIsLoading] = useState(false);
  const { exportJob } = useExportJob();

  const { type, name, contentType, uploadStatus, linkTo, accessLevel, imageUrl, documentId } =
    useMemo(() => {
      if (isFolder(item)) {
        const folder = item as Folder;
        return {
          type: 'folder' as const,
          name: folder.name,
          linkTo: `/kb/folder/${folder.id}`,
        };
      }

      if (isDocument(item)) {
        const document = item as DocumentFile;
        return {
          type: document.type,
          name: document.metadata?.name,
          contentType: document.metadata?.contentType,
          uploadStatus: document.metadata?.uploadStatus,
          accessLevel: document.accessLevel,
          linkTo: `/kb/document/${document.id}`,
          imageUrl: document.metadata?.downloadUrl,
          documentId: document.id,
        };
      }

      const knowledgeBase = item as KnowledgeBase;
      return {
        type: knowledgeBase.baseType,
        name: knowledgeBase.name,
        accessLevel: knowledgeBase.accessLevel,
        linkTo: `/kb/${knowledgeBase.id}`,
      };
    }, [item]);

  // Set initial image URL
  useMemo(() => {
    if (imageUrl) {
      setCurrentImageUrl(imageUrl);
    }
  }, [imageUrl]);

  const handleImageError = async () => {
    if (!documentId || isLoading) return;

    setIsLoading(true);
    try {
      const response = await DocumentAPI.getLink(documentId);
      if (response?.downloadUrl) {
        setCurrentImageUrl(response.downloadUrl);
      }
    } catch (error) {
      console.error('Failed to refresh image URL:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = useMemo(() => {
    if (isDocument(item)) {
      const document = item as DocumentFile;
      if (document.metadata?.uploadStatus === FILE_UPLOAD_STATUS.uploaded) {
        return () => onDownload(item);
      }
    }
    return undefined;
  }, [item]);

  const handleEdit = useMemo(() => {
    if (!isDocument(item)) {
      return () => onEdit(item);
    }
    return undefined;
  }, [item]);

  const handleDelete = useMemo(() => {
    return () => onDelete(item);
  }, [item]);

  const handleExport = useCallback(async () => {
    if (isFolder(item)) {
      const folder = item as Folder;
      await exportJob({
        folderPaths: [folder.id],
        kbIds: [],
      });
    } else if (!isDocument(item)) {
      // It's a knowledge base
      const knowledgeBase = item as KnowledgeBase;
      await exportJob({
        folderPaths: [],
        kbIds: [knowledgeBase.id],
      });
    }
  }, [item, exportJob]);

  const exportTitle = useMemo(() => {
    if (isFolder(item)) {
      return t('menu.exportFolder', { ns: 'export' });
    }
    if (!isDocument(item)) {
      return t('menu.exportKnowledgeBase', { ns: 'export' });
    }
    return undefined;
  }, [item, t]);

  const shouldShowExport = !isDocument(item);

  const menuWidth = useMemo(
    () => (isFolder(item) ? FOLDER_CARD_MENU_WIDTH : KB_CARD_MENU_WIDTH),
    [item]
  );

  return (
    <Box className={classes.container}>
      <Link to={linkTo} style={{ textDecoration: 'none' }}>
        <Box className={classes.item}>
          <Box data-testid='illustration-icon' className={classes.illustrationIcon}>
            <IllustrationIcon
              type={type}
              contentType={contentType}
              imageUrl={currentImageUrl}
              onError={handleImageError}
            />
          </Box>
          <Text className={classes.name} ta='center' lineClamp={2}>
            {name}
          </Text>
        </Box>
      </Link>
      <Box className={classes.scope}>
        {!isFolder(item) && (
          <Box className={classes.iconBox}>
            <AccessIcon accessLevel={accessLevel} />
          </Box>
        )}
        {isDocument(item) && <FileStatusIcon status={uploadStatus} />}
      </Box>
      <Box className={classes.menu}>
        <KBMenu
          onEdit={handleEdit}
          onDelete={handleDelete}
          onDownload={handleDownload}
          onExport={shouldShowExport ? handleExport : undefined}
          exportTitle={exportTitle}
          menuWidth={menuWidth}
        />
      </Box>
    </Box>
  );
};

export default GridCard;
