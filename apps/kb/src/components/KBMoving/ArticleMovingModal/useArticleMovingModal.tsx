import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import { useCallback } from 'react';

import { KBDetailForm } from '@/components/common';
import { ROOT_PATH } from '@/constants/folder';
import { useAppContext } from '@/contexts/AppContext';
import { KBSelectionContextProvider } from '@/contexts/KBSelectionContext';
import { useApiHandler, useModalManager, useNotifications } from '@/hooks';
import { ArticleAPI, KbAPI } from '@/services/api/v2';
import type { Article } from '@/types';
import ArticleMovingModalContent from './ArticleMovingModalContent';

interface UseArticleMovingModalProps {
  afterMoving?: () => void;
}

export const useArticleMovingModal = ({ afterMoving }: UseArticleMovingModalProps) => {
  const { t } = useTranslate(['article', 'kb']);
  const { modalClasses, createModal } = useModalManager();
  const { notifyMessage } = useNotifications(t);
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { handleApiRequest } = useApiHandler();

  /**
   * Move Article to another Knowledge Base
   * @param {Article} article
   * @param {string} newBaseId
   */
  const moveArticle = useCallback(
    async (article: Article, newBaseId: string) => {
      const { id: articleId } = article;

      await handleApiRequest(
        ArticleAPI.update(articleId, {
          ...article,
          relatedArticles:
            article.relatedArticles?.map((relatedArticle) => relatedArticle.id) || [],
          baseId: newBaseId,
          status: 'published',
        }),
        {
          fallbackMessage: t('articleMoving.failedMessage'),
          fallbackTitle: t('articleMoving.failedTitle'),
          successMessage: t('articleMoving.successMessage'),
          successTitle: t('articleMoving.successTitle'),
          successCallback: () => {
            modals.closeAll();
            closeConfirmModal();
            afterMoving?.();
          },
        }
      );
    },
    [afterMoving, t]
  );

  /**
   * Confirm and Move Article to another Knowledge Base
   * @param {Article} article
   * @param {string} newBaseId
   */
  const confirmAndMoveArticle = useCallback(async (article: Article, newBaseId: string) => {
    if (!newBaseId) return;

    const { data: newBase } = await KbAPI.get(newBaseId);

    if (newBase) {
      const moveHandler = async () => {
        await moveArticle(article, newBaseId);
      };

      const toKBName = newBase.data?.name || t('unknownKBName', { ns: 'kb' });
      openConfirmModal({
        onConfirm: moveHandler,
        title: t('articleMoving.confirmMovingMessage', { toKBName } as any, { ns: 'article' }),
        confirmText: t('move', { ns: 'kb' }),
        cancelText: t('cancel', { ns: 'kb' }),
        options: {
          className: modalClasses.confirmModal,
          modalSize: '465px',
        },
      });
    }
  }, []);

  /**
   * Article Moving Modal
   * @type {ModalProps}
   */
  const buildArticleMovingModal = useCallback(
    (currentArticle: Article, currentFolderId: string = ROOT_PATH, currentBaseId = '') =>
      createModal({
        title: t('articleMoving.title'),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: () => modals.closeAll(),
        children: (
          <KBSelectionContextProvider>
            <ArticleMovingModalContent
              currentArticle={currentArticle}
              currentFolderId={currentFolderId}
              currentBaseId={currentBaseId}
              onMoveAsync={async (newBaseId: string) => {
                await confirmAndMoveArticle(currentArticle, newBaseId);
              }}
              onClose={() => modals.closeAll()}
              onCreateNewKB={(parentFolderId: string) =>
                modals.open(buildCreateKBModal(currentArticle, parentFolderId))
              }
            />
          </KBSelectionContextProvider>
        ),
      }),
    [moveArticle, modalClasses, t]
  );

  /**
   * Create Knowledge Base Modal
   * @type {ModalProps}
   */
  const buildCreateKBModal = useCallback(
    (currentArticle: Article, parentFolderId: string) => {
      return createModal({
        title: t('modal.createKnowledgeBase', { ns: 'kb' }),
        classNames: {
          content: modalClasses.largeModal,
        },
        onClose: () => modals.open(buildArticleMovingModal(currentArticle, parentFolderId)),
        children: (
          <KBDetailForm
            onCancel={() => modals.open(buildArticleMovingModal(currentArticle, parentFolderId))}
            onSubmitted={(response: any) => {
              if (response?.status === 'success') {
                notifyMessage(
                  t('createKB.successTitle', { ns: 'kb' }),
                  t('createKB.successMessage', { ns: 'kb' })
                );
                modals.open(
                  buildArticleMovingModal(currentArticle, parentFolderId, response.data.id)
                );
              }
            }}
            parentDirId={parentFolderId}
          />
        ),
      });
    },
    [modalClasses, t]
  );

  return {
    openArticleMovingModal: (article: Article, parentFolderId: string = ROOT_PATH, baseId = '') => {
      modals.open(buildArticleMovingModal(article, parentFolderId, baseId));
    },
  };
};
