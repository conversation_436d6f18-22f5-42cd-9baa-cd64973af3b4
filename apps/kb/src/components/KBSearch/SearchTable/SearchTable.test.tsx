import { fireEvent, render, screen, within } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, test, vi } from 'vitest';

// Mock useTranslate before importing components that use it
const mockT = vi.fn((key) => key);
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({ t: mockT }),
}));

// Import global mockNavigate from utils/unitTest to ensure we reference the same instance used in component
import { mockNavigate } from '@/utils/unitTest';
const mockDownloadDocument = vi.fn();

// Mock useDocumentContext
vi.mock('@/contexts', () => ({
  useDocumentContext: () => ({
    downloadDocument: mockDownloadDocument,
  }),
  UploaderContextProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Now import the types and other dependencies
import {
  type Article,
  type DocumentFile,
  FILE_UPLOAD_STATUS,
  type Folder,
  type KnowledgeBase,
  type SearchResult,
  SearchTypeEnums,
} from '@/types';
import { AllTheProviders } from '@/utils/unitTest';

// Mock other dependencies
const mockOnUpdated = vi.fn();
const mockArticleViewerRef = {
  setData: vi.fn(),
  open: vi.fn(),
} as any;

vi.mock('@/components/common', () => ({
  GridTable: ({ columns, rows }: { columns: any[]; rows: any[] }) => (
    <div data-testid='grid-table'>
      <div className='css-1jke4yk'>
        <div>
          <div className='css-1hs2x4o m_410352e9 mantine-Grid-root'>
            <div className='m_dee7bd2f mantine-Grid-inner'>
              {columns.map((col) => (
                <div key={col.key} className='m_96bdd299 mantine-Grid-col css-1kj80dn'>
                  {col.title}
                </div>
              ))}
            </div>
          </div>
        </div>
        <div>
          {rows.map((row) => {
            const { data, props } = row;
            return (
              <div
                key={data.id}
                data-testid={`row-${data.id}`}
                className='css-rhaspg m_410352e9 mantine-Grid-root'
                style={props?.style}
                onClick={(e) => {
                  e.preventDefault();
                  if ((e.target as any)?.href) return;
                  if (props?.onClick) {
                    props.onClick(e);
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    if (props?.onClick) {
                      props.onClick(e as unknown as React.MouseEvent);
                    }
                  }
                }}
              >
                <div className='m_dee7bd2f mantine-Grid-inner'>
                  <div className='m_96bdd299 mantine-Grid-col css-1ti5o3q'>{data.name}</div>
                  <div
                    className='m_96bdd299 mantine-Grid-col css-1ti5o3q'
                    data-testid={`cell-${data.id}-lastUpdated`}
                  >
                    {data.lastUpdated}
                  </div>
                  <div
                    className='m_96bdd299 mantine-Grid-col css-1ti5o3q'
                    data-testid={`cell-${data.id}-createdAt`}
                  >
                    {data.createdAt}
                  </div>
                  <div className='m_96bdd299 mantine-Grid-col css-1ti5o3q'>{data.actions}</div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  ),
}));

vi.mock('@/components/KBArticleCollection/ArticleFeedback', () => ({
  ArticleFeedback: ({ article }: { article: Article }) => (
    <div data-testid={`feedback-${article.id}`}>Feedback</div>
  ),
}));

vi.mock('@/components/KBArticleCollection/ArticleStatus', () => ({
  default: ({ status }: { status: string }) => (
    <div data-testid={`status-${status}`}>Status: {status}</div>
  ),
}));

vi.mock('@/components/KBArticleCollection/ArticleViewer', () => {
  const React = require('react');

  const ArticleViewerMock = React.forwardRef((props: any, ref) => {
    React.useImperativeHandle(ref, () => mockArticleViewerRef);
    return (
      <div data-testid='article-viewer'>
        <button type='button' data-testid='viewer-update' onClick={props.onUpdated}>
          Update
        </button>
      </div>
    );
  });

  return {
    __esModule: true,
    default: ArticleViewerMock,
  };
});

vi.mock('@/utils/tree', () => ({
  isDocument: (data: any): data is DocumentFile => data?.type === SearchTypeEnums.document,
}));

vi.mock('../SearchRowActions', () => ({
  default: ({ onOpen, onDownload }: { onOpen: () => void; onDownload?: () => void }) => (
    <div data-testid='search-row-actions'>
      <button
        type='button'
        data-testid='action-open'
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          onOpen();
        }}
      >
        Open
      </button>
      {onDownload && (
        <button
          type='button'
          data-testid='action-download'
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onDownload();
          }}
        >
          Download
        </button>
      )}
    </div>
  ),
}));

vi.mock('../SearchRowInfomation', () => ({
  default: ({ searchResult }: { searchResult: SearchResult }) => (
    <div data-testid={`info-${searchResult.data.id}`}>
      {(searchResult.data as any).name || (searchResult.data as any).title}
    </div>
  ),
}));

vi.mock('@resola-ai/ui/utils/dateTime', () => ({
  formatDateTime: (date: string | Date) => {
    if (!date) return '';
    return new Date(date).toISOString();
  },
}));

vi.mock('@mantine/core', () => ({
  Flex: ({ children, align, justify, w, gap, pr }: any) => (
    <div
      className='mantine-Flex-root'
      style={{
        alignItems: align,
        justifyContent: justify,
        width: w === '100%' ? '100%' : undefined,
        gap,
        paddingRight: pr,
      }}
    >
      {children}
    </div>
  ),
}));

// Mock data
const MOCK_ARTICLE: Article = {
  id: 'article-1',
  baseId: 'kb-1',
  title: 'Test Article',
  status: 'published',
  updatedAt: new Date('2023-01-01T00:00:00.000Z'),
  createdAt: new Date('2023-01-01T00:00:00.000Z'),
  content: '',
  contentRaw: '',
  keywords: [],
  relatedArticles: [],
  customData: [],
  createdBy: {} as any,
  isShortcut: false,
};

const MOCK_ARTICLE_NO_DATES: Article = {
  ...MOCK_ARTICLE,
  id: 'article-2',
  updatedAt: undefined as unknown as Date,
  createdAt: undefined as unknown as Date,
};

const MOCK_DOCUMENT = {
  id: 'doc-1',
  orgId: 'org-1',
  folderId: 'folder-1',
  type: SearchTypeEnums.document,
  name: 'Test Document',
  metadata: {
    name: 'Test Document',
    contentLength: 1024,
    contentType: 'application/pdf',
    uploadStatus: FILE_UPLOAD_STATUS.uploaded,
  },
  createdAt: '2023-01-02T00:00:00.000Z',
  updatedAt: '2023-01-02T00:00:00.000Z',
} as unknown as DocumentFile;

const MOCK_DOCUMENT_UPLOADING = {
  ...MOCK_DOCUMENT,
  id: 'doc-2',
  metadata: {
    ...MOCK_DOCUMENT.metadata,
    uploadStatus: FILE_UPLOAD_STATUS.unknown,
  },
} as unknown as DocumentFile;

const MOCK_BASE: KnowledgeBase = {
  id: 'kb-1',
  name: 'Test Knowledge Base',
  description: '',
  baseType: 'document' as any,
  type: 'document' as any,
  createdAt: new Date('2023-01-03T00:00:00.000Z'),
  updatedAt: new Date('2023-01-03T00:00:00.000Z'),
};

const MOCK_FOLDER: Folder = {
  id: 'folder-1',
  path: '/Test Folder',
  name: 'Test Folder',
  count: 0,
  childFolderCount: 0,
  childKbCount: 0,
  createdAt: new Date('2023-01-04T00:00:00.000Z'),
  updatedAt: new Date('2023-01-04T00:00:00.000Z'),
  parentDirId: undefined,
};

const MOCK_SEARCH_RESULTS: SearchResult[] = [
  { type: SearchTypeEnums.article, data: MOCK_ARTICLE },
  { type: SearchTypeEnums.document, data: MOCK_DOCUMENT },
  { type: SearchTypeEnums.base, data: MOCK_BASE },
  { type: SearchTypeEnums.folder, data: MOCK_FOLDER },
];

// Import the component under test
import SearchTable from './index';

describe('SearchTable', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockNavigate.mockClear();
    mockT.mockClear();
    mockDownloadDocument.mockClear();
    mockArticleViewerRef.setData.mockClear();
    mockArticleViewerRef.open.mockClear();
  });

  const renderComponent = (results = MOCK_SEARCH_RESULTS) => {
    return render(
      <AllTheProviders>
        <SearchTable searchResults={results} onUpdated={mockOnUpdated} />
      </AllTheProviders>
    );
  };

  test('renders the table with correct columns and rows', () => {
    renderComponent();
    expect(screen.getByTestId('grid-table')).toBeInTheDocument();

    // Check column headers
    expect(screen.getByText('articleCollection.name')).toBeInTheDocument();
    expect(screen.getByText('articleCollection.lastUpdated')).toBeInTheDocument();
    expect(screen.getByText('articleCollection.createdAt')).toBeInTheDocument();

    // Check rows
    expect(screen.getByTestId('row-article-1')).toBeInTheDocument();
    expect(screen.getByTestId('row-doc-1')).toBeInTheDocument();
    expect(screen.getByTestId('row-kb-1')).toBeInTheDocument();
    expect(screen.getByTestId('row-folder-1')).toBeInTheDocument();
  });

  test('renders article status and feedback for article items only', () => {
    renderComponent();

    // Article row should have status and feedback
    expect(screen.getByTestId('status-published')).toBeInTheDocument();
    expect(screen.getByTestId('feedback-article-1')).toBeInTheDocument();

    // Document row should not have status or feedback
    expect(screen.queryByTestId('status-uploaded')).not.toBeInTheDocument();
    expect(screen.queryByTestId('feedback-doc-1')).not.toBeInTheDocument();
  });

  describe('Article Viewer Interactions', () => {
    test('clicking an article row opens the article viewer', () => {
      renderComponent();
      const articleRow = screen.getByTestId('row-article-1');
      fireEvent.click(articleRow);

      expect(mockArticleViewerRef.setData).toHaveBeenCalledWith({
        articleId: 'article-1',
        baseId: 'kb-1',
      });
      expect(mockArticleViewerRef.open).toHaveBeenCalled();
    });

    test('does not open article viewer when ref is null', () => {
      const originalSetData = mockArticleViewerRef.setData;
      const originalOpen = mockArticleViewerRef.open;
      mockArticleViewerRef.setData = null;
      mockArticleViewerRef.open = null;

      renderComponent();
      const articleRow = screen.getByTestId('row-article-1');
      fireEvent.click(articleRow);

      expect(mockArticleViewerRef.setData).toBeNull();
      expect(mockArticleViewerRef.open).toBeNull();

      // Restore original functions
      mockArticleViewerRef.setData = originalSetData;
      mockArticleViewerRef.open = originalOpen;
    });

    test('does not open article viewer when data is invalid', () => {
      const invalidResults = [
        { type: SearchTypeEnums.article, data: { id: 'invalid', type: SearchTypeEnums.article } },
      ];
      renderComponent(invalidResults);
      const row = screen.getByTestId('row-invalid');
      fireEvent.click(row);

      // Since the data is invalid (no baseId), the article viewer should not be opened
      expect(mockArticleViewerRef.setData).not.toHaveBeenCalled();
      expect(mockArticleViewerRef.open).not.toHaveBeenCalled();
    });
  });

  describe('Document Download Functionality', () => {
    test('download button is enabled for uploaded documents', () => {
      renderComponent();
      const downloadButton = screen.getByTestId('action-download');
      fireEvent.click(downloadButton);
      expect(mockDownloadDocument).toHaveBeenCalledWith('doc-1');
    });

    test('download button is disabled for uploading documents', () => {
      renderComponent([{ type: SearchTypeEnums.document, data: MOCK_DOCUMENT_UPLOADING }]);
      expect(screen.queryByTestId('action-download')).not.toBeInTheDocument();
    });
  });

  describe('Row Click Behavior', () => {
    test('does not trigger row click when clicking a link', () => {
      const mockEvent = {
        target: { href: 'http://example.com' },
        preventDefault: vi.fn(),
      };
      renderComponent();
      const articleRow = screen.getByTestId('row-article-1');
      fireEvent.click(articleRow, mockEvent);

      expect(mockArticleViewerRef.setData).not.toHaveBeenCalled();
      expect(mockArticleViewerRef.open).not.toHaveBeenCalled();
    });

    test('handles keyboard navigation', () => {
      renderComponent();
      const articleRow = screen.getByTestId('row-article-1');

      // Test Enter key
      fireEvent.keyDown(articleRow, { key: 'Enter' });
      expect(mockArticleViewerRef.setData).toHaveBeenCalled();
      expect(mockArticleViewerRef.open).toHaveBeenCalled();

      // Test Space key
      mockArticleViewerRef.setData.mockClear();
      mockArticleViewerRef.open.mockClear();
      fireEvent.keyDown(articleRow, { key: ' ' });
      expect(mockArticleViewerRef.setData).toHaveBeenCalled();
      expect(mockArticleViewerRef.open).toHaveBeenCalled();
    });
  });

  describe('Date Handling', () => {
    test('handles undefined dates gracefully', () => {
      renderComponent([{ type: SearchTypeEnums.article, data: MOCK_ARTICLE_NO_DATES }]);
      expect(screen.getByTestId('cell-article-2-lastUpdated')).toHaveTextContent('');
      expect(screen.getByTestId('cell-article-2-createdAt')).toHaveTextContent('');
    });
  });

  describe('Navigation', () => {
    test('handleOpen navigates to the correct base page', () => {
      renderComponent([{ type: SearchTypeEnums.base, data: MOCK_BASE }]);
      const actions = screen.getByTestId('search-row-actions');
      const openButton = within(actions).getByTestId('action-open');
      fireEvent.click(openButton);
      expect(mockNavigate).toHaveBeenCalledWith('/kb/kb-1');
    });

    test('handleOpen navigates to the correct folder page', () => {
      renderComponent([{ type: SearchTypeEnums.folder, data: MOCK_FOLDER }]);
      const actions = screen.getByTestId('search-row-actions');
      const openButton = within(actions).getByTestId('action-open');
      fireEvent.click(openButton);
      expect(mockNavigate).toHaveBeenCalledWith('/kb/folder/folder-1');
    });

    test('handleOpen navigates to the correct document page', () => {
      renderComponent([{ type: SearchTypeEnums.document, data: MOCK_DOCUMENT }]);
      const actions = screen.getByTestId('search-row-actions');
      const openButton = within(actions).getByTestId('action-open');
      fireEvent.click(openButton);
      expect(mockNavigate).toHaveBeenCalledWith('/kb/document/doc-1');
    });

    test('does not navigate when id is missing', () => {
      const invalidResults = [
        { type: SearchTypeEnums.base, data: { ...MOCK_BASE, id: undefined } },
        { type: SearchTypeEnums.folder, data: { ...MOCK_FOLDER, id: undefined } },
        { type: SearchTypeEnums.document, data: { ...MOCK_DOCUMENT, id: undefined } },
      ];
      renderComponent(invalidResults);

      const rows = screen.getAllByTestId('row-undefined');
      rows.forEach((row) => {
        const actions = within(row).getByTestId('search-row-actions');
        const openButton = within(actions).getByTestId('action-open');
        fireEvent.click(openButton);
      });

      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('Empty States and Edge Cases', () => {
    test('renders empty table when no results', () => {
      renderComponent([]);
      expect(screen.getByTestId('grid-table')).toBeInTheDocument();
      expect(screen.queryByTestId(/^row-/)).not.toBeInTheDocument();
    });

    test('handles unknown search result type gracefully', () => {
      const unknownTypeResult = [{ type: 'unknown' as any, data: { id: 'unknown-1' } }];
      renderComponent(unknownTypeResult);
      const openButton = screen.getByTestId('action-open');
      fireEvent.click(openButton);
      // Should not throw and not navigate
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  test('onUpdated callback is called from ArticleViewer', () => {
    renderComponent();
    const updateButton = screen.getByTestId('viewer-update');
    fireEvent.click(updateButton);
    expect(mockOnUpdated).toHaveBeenCalled();
  });
});
