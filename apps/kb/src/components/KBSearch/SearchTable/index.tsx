import { ArticleFeedback } from '@/components/KBArticleCollection/ArticleFeedback';
import ArticleStatus from '@/components/KBArticleCollection/ArticleStatus';
import ArticleViewer, {
  type ArticleViewerRef,
} from '@/components/KBArticleCollection/ArticleViewer';
import { GridTable } from '@/components/common';
import { useDocumentContext } from '@/contexts';
import {
  type Article,
  type DocumentFile,
  FILE_UPLOAD_STATUS,
  type SearchResult,
  SearchTypeEnums,
} from '@/types';
import { isDocument } from '@/utils/tree';
import { Flex } from '@mantine/core';
import { formatDateTime } from '@resola-ai/ui/utils/dateTime';
import { useTranslate } from '@tolgee/react';
import type React from 'react';
import { useCallback, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import SearchRowActions from '../SearchRowActions';
import SearchRowInfomation from '../SearchRowInfomation';

interface SearchTableProps {
  searchResults: SearchResult[];
  onUpdated: () => void;
}

const SearchTable: React.FC<SearchTableProps> = ({ searchResults, onUpdated }) => {
  const { t } = useTranslate(['article', 'common', 'kb']);
  const navigate = useNavigate();
  const articleViewerRef = useRef<ArticleViewerRef>(null);
  const { downloadDocument } = useDocumentContext();
  /**
   * Open Article Viewer
   * @param {SearchResult} searchItem
   * @dependencies articleViewerRef: ArticleViewerRef
   * @returns {void}
   */
  const openArticleViewer = useCallback(
    (searchItem: SearchResult) => {
      if (!articleViewerRef.current || !searchItem) return;
      const articleDetail = searchItem.data as Article;

      // Only open article viewer if we have both articleId and baseId
      if (!articleDetail.id || !articleDetail.baseId) return;

      articleViewerRef.current?.setData?.({
        articleId: articleDetail.id,
        baseId: articleDetail.baseId,
      });
      articleViewerRef.current?.open?.();
    },
    [articleViewerRef]
  );

  /**
   * Handle Open Entity (Article, Document, etc.) when clicking the Open Button
   * @param {SearchResult} searchItem
   * @dependencies openArticleViewer: openArticleViewer function
   */
  const handleOpen = useCallback(
    (searchItem: SearchResult) => {
      if (!searchItem?.data?.id) return;

      switch (searchItem.type) {
        case SearchTypeEnums.article:
          openArticleViewer(searchItem);
          break;

        case SearchTypeEnums.base:
          navigate(`/kb/${searchItem.data.id}`);
          break;

        case SearchTypeEnums.folder:
          navigate(`/kb/folder/${searchItem.data.id}`);
          break;

        case SearchTypeEnums.document:
          navigate(`/kb/document/${searchItem.data.id}`);
          break;

        default:
          break;
      }
    },
    [openArticleViewer, navigate]
  );

  const getHandleDownload = useCallback(
    (searchItem: SearchResult) => {
      if (isDocument(searchItem.data)) {
        const document = searchItem.data as DocumentFile;
        if (document.metadata?.uploadStatus === FILE_UPLOAD_STATUS.uploaded) {
          return () => downloadDocument(document.id as string);
        }
      }

      return undefined;
    },
    [downloadDocument]
  );

  /**
   * Search Result Columns
   * @returns {JSX.Element[]}
   * @dependencies t: i18n function
   */
  const SearchResultColumns = useMemo(
    () => [
      {
        title: t('articleCollection.name'),
        key: 'name',
        size: 8,
      },
      {
        title: t('articleCollection.lastUpdated'),
        key: 'lastUpdated',
      },
      {
        title: t('articleCollection.createdAt'),
        key: 'createdAt',
      },
      {
        title: '',
        key: 'actions',
        size: 1,
      },
    ],
    [t]
  );

  /**
   * Search Result Rows
   * @returns {JSX.Element[]}
   * @dependencies ArticleCollection
   */
  const SearchResultRows = useMemo(
    () =>
      searchResults.map((searchItem: SearchResult) => {
        const { data } = searchItem;

        return {
          props: {
            onClick: (event) => {
              if (event.target?.href) return;
              handleOpen(searchItem);
            },
            style: {
              cursor: 'pointer',
            },
          },
          data: {
            id: data.id,
            name: (
              <Flex align='center' justify='space-between' w='100%' gap='lg' pr='lg'>
                <SearchRowInfomation searchResult={searchItem} />
                {searchItem.type === SearchTypeEnums.article && (
                  <Flex align='center' gap='md'>
                    <ArticleStatus status={(searchItem.data as Article).status} />
                    <ArticleFeedback article={searchItem.data as Article} />
                  </Flex>
                )}
              </Flex>
            ),
            lastUpdated: formatDateTime(data.updatedAt ?? ''),
            createdAt: 'createdAt' in data ? formatDateTime(data.createdAt ?? '') : '',
            actions: (
              <SearchRowActions
                onOpen={() => handleOpen(searchItem)}
                onDownload={getHandleDownload(searchItem)}
              />
            ),
          },
        };
      }),
    [searchResults]
  );

  return (
    <>
      <GridTable columns={SearchResultColumns} rows={SearchResultRows} />
      <ArticleViewer
        ref={articleViewerRef}
        backTitle={t('backToKBList', { ns: 'common' })}
        onUpdated={onUpdated}
      />
    </>
  );
};

export default SearchTable;
