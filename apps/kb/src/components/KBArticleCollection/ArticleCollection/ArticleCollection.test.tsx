import type { Article, KnowledgeBase } from '@/types';
import { KB_TYPE } from '@/types';
import { AllTheProviders } from '@/utils/unitTest';
import { fireEvent, render, screen } from '@testing-library/react';
import type React from 'react';
import { beforeEach, describe, expect, test, vi } from 'vitest';
import ArticleCollection from './index';

// Test constants
const MOCK_KB: KnowledgeBase = {
  id: 'kb-123',
  name: 'Test Knowledge Base',
  description: 'Test description',
  baseType: KB_TYPE.article,
  type: KB_TYPE.article,
  orgId: 'org-123',
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-01'),
  createdBy: {
    id: 'user-123',
    orgId: 'org-123',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
  },
  deletedAt: null,
  parentDirId: 'parent-123',
  templateId: 'template-123',
  isDeleted: false,
  isPublic: false,
  language: 'en',
  logoUrl: '',
  keywords: [],
  customData: [],
  relatedBases: [],
  articles: [],
  articlesCount: 0,
  publishedArticlesCount: 0,
  totalFeedbackCount: 0,
  totalViewCount: 0,
  aiConfig: null,
  logoFileId: null,
  isCollaborative: false,
  isInternalToOrg: false,
  isSearchable: true,
  isAnalyticsEnabled: false,
  aiSearchConfig: null,
} as KnowledgeBase;

const MOCK_ARTICLE: Article = {
  id: 'article-123',
  baseId: 'kb-123',
  title: 'Test Article',
  content: 'Test content',
  contentRaw: 'Test content',
  status: 'published',
  keywords: ['test'],
  relatedArticles: [],
  customData: [],
  createdAt: new Date('2023-01-01'),
  updatedAt: new Date('2023-01-02'),
  createdBy: {
    id: 'user-123',
    orgId: 'org-123',
    createdAt: '2023-01-01',
    updatedAt: '2023-01-01',
    displayName: 'Test User',
    email: '<EMAIL>',
    familyName: 'User',
    givenName: 'Test',
    picture: '',
  },
  isShortcut: false,
};

const MOCK_DRAFT_ARTICLE: Article = {
  ...MOCK_ARTICLE,
  id: 'article-456',
  title: 'Draft Article',
  status: 'draft',
};

const MOCK_ARTICLES: Article[] = [MOCK_ARTICLE, MOCK_DRAFT_ARTICLE];

const MOCK_PAGINATION = {
  hasNext: true,
  hasPrevious: false,
  nextCursor: 'next-cursor',
  previousCursor: '',
  totalCount: 2,
};

// Mock dependencies
vi.mock('@/components/KBArticleGenerator', () => ({
  GeneratorButton: ({ baseId }: { baseId: string }) => (
    <button type='button' data-testid='generator-button' data-base-id={baseId}>
      Generate Article
    </button>
  ),
}));

vi.mock('@/components/KBExport/components/ExportJobByEntityButton', () => ({
  default: ({ entityId, onSuccess }: { entityId: string; onSuccess: () => void }) => (
    <button
      type='button'
      data-testid='export-button'
      data-entity-id={entityId}
      onClick={() => onSuccess()}
    >
      Export
    </button>
  ),
}));

vi.mock('@/components/KBMoving', () => ({
  useArticleMovingModal: () => ({
    openArticleMovingModal: vi.fn(),
  }),
}));

vi.mock('@/components/common', () => ({
  CustomPagination: ({ onChange, onPageSizeChange, pageSize }: any) => (
    <div data-testid='pagination'>
      <button
        type='button'
        data-testid='pagination-next'
        onClick={() => onChange('forward', 'next-cursor')}
      >
        Next
      </button>
      <button
        type='button'
        data-testid='pagination-prev'
        onClick={() => onChange('backward', 'prev-cursor')}
      >
        Previous
      </button>
      <select
        data-testid='page-size-select'
        value={pageSize}
        onChange={(e) => onPageSizeChange(Number(e.target.value))}
      >
        <option value={10}>10</option>
        <option value={20}>20</option>
        <option value={50}>50</option>
      </select>
    </div>
  ),
  GridTable: ({ columns, rows }: any) => (
    <div data-testid='grid-table'>
      <table>
        <thead>
          <tr>
            {columns.map((col: any) => (
              <th key={col.key}>{col.title}</th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((row: any) => (
            <tr
              key={row.data.id}
              data-testid={`article-row-${row.data.id}`}
              style={row.props.style}
              data-activated={row.props.activated}
            >
              {columns.map((col: any, colIndex: number) => (
                <td key={col.key}>
                  {colIndex === 0 ? (
                    <button
                      type='button'
                      onClick={row.props.onClick}
                      style={{
                        border: 'none',
                        background: 'transparent',
                        padding: 0,
                        margin: 0,
                        font: 'inherit',
                        color: 'inherit',
                        cursor: 'pointer',
                        textAlign: 'left',
                        width: '100%',
                      }}
                    >
                      {row.data[col.key]}
                    </button>
                  ) : (
                    row.data[col.key]
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  ),
  SearchBox: ({ onSearch }: any) => (
    <input
      data-testid='search-box'
      placeholder='search'
      onChange={(e) => onSearch(e.target.value)}
    />
  ),
}));

vi.mock('@/contexts', () => ({
  useArticleContext: () => ({
    articleCollection: {
      data: mockArticleContextData.articles,
      pagination: mockArticleContextData.pagination,
    },
    state: {
      isLoadingArticles: mockArticleContextData.isLoadingArticles,
      capturedPayload: mockArticleContextData.capturedPayload,
      activeArticleId: mockArticleContextData.activeArticleId,
      pageLimit: mockArticleContextData.pageLimit,
      searchKeyword: mockArticleContextData.searchKeyword,
    },
    actions: {
      setActiveArticleId: mockArticleContextData.setActiveArticleId,
      setCurrentKb: mockArticleContextData.setCurrentKb,
      setSearchKeyword: mockArticleContextData.setSearchKeyword,
      setPageLimit: mockArticleContextData.setPageLimit,
    },
    services: {
      getCollection: mockArticleContextData.getCollection,
      createArticle: mockArticleContextData.createArticle,
      deleteArticle: mockArticleContextData.deleteArticle,
    },
  }),
  UploaderContextProvider: ({ children }: { children: React.ReactNode }) => children,
}));

vi.mock('@/hooks', () => ({
  useKbAccessControl: () => ({
    permArticle: {
      canGenerate: true,
      canCreate: true,
      canUpdate: true,
      canDelete: true,
    },
  }),
}));

vi.mock('../ArticleActions', () => ({
  default: ({ onOpen, onDelete, onMove }: any) => (
    <div data-testid='article-actions'>
      <button type='button' data-testid='action-open' onClick={onOpen}>
        Open
      </button>
      <button type='button' data-testid='action-delete' onClick={onDelete}>
        Delete
      </button>
      <button type='button' data-testid='action-move' onClick={onMove}>
        Move
      </button>
    </div>
  ),
}));

vi.mock('../ArticleCreateNew', () => ({
  default: ({ onImport }: { onImport: () => void }) => (
    <button type='button' data-testid='create-new-button' onClick={() => onImport()}>
      Create New
    </button>
  ),
}));

vi.mock('../ArticleEmpty', () => ({
  default: ({ onCreate, onImport }: any) => (
    <div data-testid='article-empty'>
      <button type='button' data-testid='empty-create' onClick={onCreate}>
        Create Article
      </button>
      <button type='button' data-testid='empty-import' onClick={onImport}>
        Import Articles
      </button>
    </div>
  ),
}));

vi.mock('../ArticleFeedback', () => ({
  ArticleFeedback: ({ article }: { article: Article }) => (
    <div data-testid='article-feedback' data-article-id={article.id}>
      Feedback
    </div>
  ),
}));

vi.mock('../ArticleName', () => ({
  ArticleName: ({ article }: { article: Article }) => (
    <div data-testid='article-name' data-article-id={article.id}>
      {article.title}
    </div>
  ),
}));

vi.mock('../ArticleStatus', () => ({
  default: ({ status }: { status: string }) => (
    <div data-testid='article-status' data-status={status}>
      {status}
    </div>
  ),
}));

vi.mock('../ArticleViewer', () => ({
  default: ({ backTitle, onUpdated, onClosed }: any) => (
    <div data-testid='article-viewer'>
      <button type='button' data-testid='viewer-back' onClick={onClosed}>
        {backTitle}
      </button>
      <button type='button' data-testid='viewer-update' onClick={() => onUpdated(true)}>
        Update
      </button>
    </div>
  ),
}));

vi.mock('@/utils/article', () => ({
  cleanBadCharacters: (str: string) => str,
}));

vi.mock('@resola-ai/ui/utils/dateTime', () => ({
  formatDateTime: (date: Date) => date.toISOString(),
}));

// Mock context data
const mockArticleContextData = {
  articles: MOCK_ARTICLES,
  pagination: MOCK_PAGINATION,
  isLoadingArticles: false,
  capturedPayload: { direction: 'backward' as const, cursor: '' },
  activeArticleId: '',
  pageLimit: 10,
  searchKeyword: '',
  setActiveArticleId: vi.fn(),
  setCurrentKb: vi.fn(),
  setSearchKeyword: vi.fn(),
  setPageLimit: vi.fn(),
  getCollection: vi.fn(),
  createArticle: vi.fn(),
  deleteArticle: vi.fn(),
};

// Mock additional functions
// const mockOpenArticleViewer = vi.fn();
// const mockHandleCreateArticle = vi.fn();

describe('ArticleCollection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.resetModules();

    // Reset mock data to default state
    mockArticleContextData.articles = MOCK_ARTICLES;
    mockArticleContextData.pagination = MOCK_PAGINATION;
    mockArticleContextData.isLoadingArticles = false;
    mockArticleContextData.activeArticleId = '';
    mockArticleContextData.searchKeyword = '';
    mockArticleContextData.pageLimit = 10;
  });

  describe('Initial Rendering', () => {
    test('renders toolbar with search and action buttons', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('search-box')).toBeInTheDocument();
      expect(screen.getByTestId('export-button')).toBeInTheDocument();
      expect(screen.getByTestId('generator-button')).toBeInTheDocument();
      expect(screen.getByTestId('create-new-button')).toBeInTheDocument();
    });

    test('renders article table with correct columns', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('grid-table')).toBeInTheDocument();
      expect(screen.getByText('articleCollection.name')).toBeInTheDocument();
      expect(screen.queryByText('articleCollection.status')).not.toBeInTheDocument();
      expect(screen.getByText('articleCollection.lastUpdated')).toBeInTheDocument();
      expect(screen.getByText('articleCollection.createdAt')).toBeInTheDocument();
    });

    test('renders article rows with correct data', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Check first article
      expect(screen.getByTestId('article-row-article-123')).toBeInTheDocument();
      expect(screen.getAllByTestId('article-name')).toHaveLength(2);
      expect(screen.getAllByTestId('article-status')).toHaveLength(2);
      expect(screen.getAllByTestId('article-feedback')).toHaveLength(2);
      expect(screen.getAllByTestId('article-actions')).toHaveLength(2);

      // Check second article
      expect(screen.getByTestId('article-row-article-456')).toBeInTheDocument();
    });

    test('renders pagination controls', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('pagination')).toBeInTheDocument();
      expect(screen.getByTestId('pagination-next')).toBeInTheDocument();
      expect(screen.getByTestId('pagination-prev')).toBeInTheDocument();
      expect(screen.getByTestId('page-size-select')).toBeInTheDocument();
    });
  });

  describe('Loading States', () => {
    test('shows loading overlay when loading articles', () => {
      mockArticleContextData.isLoadingArticles = true;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Check that loading overlay is visible
      expect(document.querySelector('.mantine-LoadingOverlay-root')).toBeInTheDocument();
    });

    test('hides loading overlay when not loading', () => {
      mockArticleContextData.isLoadingArticles = false;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // Check that loading overlay is not visible (should be hidden)
      const loadingOverlay = document.querySelector('.mantine-LoadingOverlay-root');
      // When not loading, the overlay should be hidden or not rendered
      expect(loadingOverlay).not.toBeInTheDocument();
    });
  });

  describe('Empty State', () => {
    test('shows empty state when no articles', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('article-empty')).toBeInTheDocument();
      expect(screen.getByTestId('empty-create')).toBeInTheDocument();
      expect(screen.getByTestId('empty-import')).toBeInTheDocument();
    });

    test('hides grid table when no articles', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('grid-table')).not.toBeInTheDocument();
    });

    test('hides pagination when no articles', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('pagination')).not.toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    test('handles search input changes', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const searchBox = screen.getByTestId('search-box');
      fireEvent.change(searchBox, { target: { value: 'test query' } });

      // Component should handle search change (indirect test - component renders without errors)
      expect(searchBox).toHaveValue('test query');
    });

    test('handles pagination next button click', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const nextButton = screen.getByTestId('pagination-next');
      fireEvent.click(nextButton);

      expect(mockArticleContextData.getCollection).toHaveBeenCalledWith(
        'kb-123',
        10,
        'forward',
        'next-cursor',
        '',
        false
      );
    });

    test('handles pagination previous button click', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const prevButton = screen.getByTestId('pagination-prev');
      fireEvent.click(prevButton);

      expect(mockArticleContextData.getCollection).toHaveBeenCalledWith(
        'kb-123',
        10,
        'backward',
        'prev-cursor',
        '',
        false
      );
    });

    test('handles page size change', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const pageSizeSelect = screen.getByTestId('page-size-select');
      fireEvent.change(pageSizeSelect, { target: { value: '20' } });

      // Component should handle page size change (indirect test - component renders without errors)
      expect(pageSizeSelect).toBeInTheDocument();
    });

    test('handles article row click', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const articleRow = screen.getByTestId('article-row-article-123');
      fireEvent.click(articleRow);

      // Component should handle row click (indirect test - component renders without errors)
      expect(articleRow).toHaveAttribute('data-testid', 'article-row-article-123');
    });

    test('handles article delete action', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const deleteButtons = screen.getAllByTestId('action-delete');
      fireEvent.click(deleteButtons[0]);

      expect(mockArticleContextData.deleteArticle).toHaveBeenCalledWith(
        MOCK_ARTICLE,
        expect.any(Function)
      );
    });

    test('handles export button click', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const exportButton = screen.getByTestId('export-button');
      fireEvent.click(exportButton);

      expect(mockArticleContextData.getCollection).toHaveBeenCalled();
    });
  });

  describe('Article Status Display', () => {
    test('displays published status for published articles', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const statusElements = screen.getAllByTestId('article-status');
      const publishedStatus = statusElements.find(
        (el) => el.getAttribute('data-status') === 'published'
      );
      expect(publishedStatus).toBeInTheDocument();
    });

    test('displays draft status for draft articles', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const statusElements = screen.getAllByTestId('article-status');
      const draftStatus = statusElements.find((el) => el.getAttribute('data-status') === 'draft');
      expect(draftStatus).toBeInTheDocument();
    });
  });

  describe('Article Actions', () => {
    test('renders action buttons for each article', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const actionButtons = screen.getAllByTestId('article-actions');
      expect(actionButtons).toHaveLength(2);
    });

    test('handles article open action', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const openButtons = screen.getAllByTestId('action-open');
      fireEvent.click(openButtons[0]);

      // Component should handle action click (indirect test - component renders without errors)
      expect(openButtons[0]).toBeInTheDocument();
    });
  });

  describe('Article Viewer Integration', () => {
    test('renders article viewer component', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('article-viewer')).toBeInTheDocument();
    });

    test('handles article viewer close', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const backButton = screen.getByTestId('viewer-back');
      fireEvent.click(backButton);

      expect(mockArticleContextData.setActiveArticleId).toHaveBeenCalledWith('');
    });

    test('handles article viewer update', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const updateButton = screen.getByTestId('viewer-update');
      fireEvent.click(updateButton);

      expect(mockArticleContextData.getCollection).toHaveBeenCalledWith(
        'kb-123',
        10,
        'backward',
        '',
        '',
        true
      );
    });
  });

  describe('Initialization', () => {
    test('fetches collection on mount', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(mockArticleContextData.setCurrentKb).toHaveBeenCalledWith(MOCK_KB);
      expect(mockArticleContextData.getCollection).toHaveBeenCalledWith(
        'kb-123',
        10,
        'backward',
        '',
        '',
        true
      );
    });

    test('refetches collection when KB ID changes', () => {
      const { rerender } = render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      const newKb = { ...MOCK_KB, id: 'new-kb-id' };
      rerender(
        <AllTheProviders>
          <ArticleCollection kb={newKb} />
        </AllTheProviders>
      );

      expect(mockArticleContextData.setCurrentKb).toHaveBeenCalledWith(newKb);
      expect(mockArticleContextData.getCollection).toHaveBeenCalledWith(
        'new-kb-id',
        10,
        'backward',
        '',
        '',
        true
      );
    });
  });

  describe('Conditional Rendering', () => {
    test('hides create new button when articles are empty', () => {
      mockArticleContextData.articles = [];

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('create-new-button')).not.toBeInTheDocument();
    });

    test('shows create new button when articles exist', () => {
      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.getByTestId('create-new-button')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('handles undefined articles data gracefully', () => {
      mockArticleContextData.articles = undefined as any;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      // When articles is undefined, it might not show empty state but should not crash
      expect(screen.queryByTestId('grid-table')).not.toBeInTheDocument();
    });

    test('handles null pagination gracefully', () => {
      mockArticleContextData.pagination = null as any;

      render(
        <AllTheProviders>
          <ArticleCollection kb={MOCK_KB} />
        </AllTheProviders>
      );

      expect(screen.queryByTestId('pagination')).not.toBeInTheDocument();
    });
  });
});
