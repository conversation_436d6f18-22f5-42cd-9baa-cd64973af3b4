import IconImportSuccess from '@/components/Icons/IconImportSuccess';
import { Box, Button, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { modals } from '@mantine/modals';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

const useStyles = createStyles(() => ({
  modalSuccess: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    margin: '2rem 0',
  },
}));

interface SuccessBoxProps {
  count: number;
}

export const SuccessBox: React.FC<SuccessBoxProps> = ({ count }) => {
  const { t } = useTranslate(['article', 'common']);
  const { classes } = useStyles();

  return (
    <Box className={classes.modalSuccess}>
      <IconImportSuccess />
      <Text size='xl' mt='md' c='decaNavy' fw={700}>
        {t('import.importArticleSuccess')}
      </Text>
      <Text size='lg' c='dimmed' my={7}>
        {t('import.importArticleSuccessDescription', { count })}
      </Text>
      <Button variant='default' onClick={() => modals.closeAll()}>
        {t('closeBtn', { ns: 'common' })}
      </Button>
    </Box>
  );
};
