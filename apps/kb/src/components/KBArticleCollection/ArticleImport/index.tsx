import { IconPdfImport } from '@/components/Icons';
import {
  MAX_ARTICLE_CONTENT_LENGTH,
  MAX_ARTICLE_FILE_SIZE,
  MAX_ARTICLE_ROWS,
  MAX_ARTICLE_TITLE_LENGTH,
} from '@/constants/kb';
import { FILE_CONTENT_TYPE } from '@/types';
import { fileSize } from '@/utils';
import { Box, Button, Flex, Group, Loader, Text, rem } from '@mantine/core';
import { Dropzone, type FileRejection } from '@mantine/dropzone';
import { createStyles } from '@mantine/emotion';
import { IconAlertCircle, IconDownload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect, useState } from 'react';
interface ArticleImportProps {
  KBName: string;
  accept?: {
    [key: string]: string[];
  };
  size?: number;
  maxRows?: number;
  maxLengthTitle?: number;
  maxLengthContent?: number;
  isUploading?: boolean;
  isDisabled?: boolean;
  minimize?: boolean;
  template?: {
    description: string;
    url: string;
  };
  onChange?: (successCount: number) => void;
  onUpload?: (
    file: File,
    onError: (error: { message: string; expandData?: any }) => void
  ) => Promise<void>;
  onClose?: () => void;
}

const useStyles = createStyles((theme) => ({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: rem(20),
  },
  instructions: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: rem(10),
  },
  buttonDownload: {
    backgroundColor: theme.colors.silverFox[1],
    color: theme.colors.blue[6],
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
    '&:hover': {
      backgroundColor: theme.colors.blue[1],
      color: theme.colors.blue[6],
    },
  },
  buttonGroup: {
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    paddingTop: theme.spacing.md,
  },
  dropzone: {
    width: '100%',
    minHeight: 200,
    border: `1px dashed ${theme.colors.decaGrey[2]}`,
    borderRadius: theme.radius.md,
    padding: theme.spacing.lg,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },

  error: {
    backgroundColor: theme.colors.decaRed[1],
    borderRadius: theme.radius.md,
    padding: theme.spacing.xs,
    marginTop: theme.spacing.md,
    color: theme.colors.decaRed[6],
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing.xs,
  },
  loadingBox: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    flexDirection: 'column',
    margin: '2rem 0',
  },
}));

const ArticleImport: React.FC<ArticleImportProps> = ({
  KBName,
  accept = {
    [FILE_CONTENT_TYPE.csv]: [],
  },
  size = MAX_ARTICLE_FILE_SIZE,
  maxRows = MAX_ARTICLE_ROWS,
  maxLengthTitle = MAX_ARTICLE_TITLE_LENGTH,
  maxLengthContent = MAX_ARTICLE_CONTENT_LENGTH,
  template,
  onChange,
  onUpload,
  onClose,
  isUploading,
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate(['article', 'common']);
  const [file, setFile] = useState<File | null>(null);
  const [error, setError] = useState<{ message: string; expandData?: any } | null>(null);

  const handleChange = useCallback(
    (inputFiles: File[] | null) => {
      setError(null);
      if (inputFiles?.length) {
        setFile(inputFiles[0]);
      } else {
        setFile(null);
      }
    },
    [setFile]
  );

  const handleDownloadTemplate = useCallback(() => {
    template?.url && window.open(template.url, '_blank');
  }, [template?.url]);

  const handleUpload = useCallback(async () => {
    if (file) {
      await onUpload?.(file, (error: { message: string; expandData?: any }) => {
        setError(error);
      });
      setFile(null);
    }
  }, [file, onChange]);

  const handleCancel = useCallback(() => {
    if (file) {
      setFile(null);
    } else {
      onClose?.();
    }
  }, [onClose, file]);

  const handleReject = useCallback((files: FileRejection[]) => {
    const file = files[0];
    if (file.errors.length) {
      const error = file.errors[0];
      setError({ message: error.code });
    }
  }, []);

  useEffect(() => {
    return () => {
      setFile(null);
      setError(null);
    };
  }, []);

  return (
    <Box className={classes.container}>
      {isUploading ? (
        <Box className={classes.loadingBox}>
          <Loader />
          <Text size='xl' mt='md' c='decaNavy' fw={700}>
            {t('import.uploading', { ns: 'article' })}
          </Text>
          <Text size='md' c='dimmed' mt={7}>
            {t('import.uploadingDesc')}
          </Text>
        </Box>
      ) : (
        <>
          {file ? (
            <Text>
              {t('import.descriptionConfirm', {
                KBName: KBName,
              })}
            </Text>
          ) : (
            <>
              <Box className={classes.instructions}>
                <Text size='md'>{t('import.csvInstructions', { ns: 'article' })}</Text>
                <Button
                  component='a'
                  variant='light'
                  leftSection={<IconDownload size={16} />}
                  onClick={handleDownloadTemplate}
                  className={classes.buttonDownload}
                >
                  {t('import.downloadTemplate', { ns: 'article' })}
                </Button>
              </Box>

              <Dropzone
                onDrop={handleChange}
                accept={accept}
                maxSize={size}
                maxFiles={1}
                className={classes.dropzone}
                onReject={handleReject}
              >
                <Flex justify='center' align='center' direction='column'>
                  <Box style={{ textAlign: 'center' }}>
                    <IconPdfImport />
                    <Text size='xl' mt='md'>
                      {t('import.dropzone', { ns: 'article' })}
                    </Text>
                    <Text size='sm' c='dimmed' mt={7}>
                      {t('import.maxFileSizeAndRows', {
                        size: `${fileSize(size)}`,
                        rows: maxRows,
                      })}
                    </Text>
                  </Box>
                  {error && (
                    <Box className={classes.error}>
                      <IconAlertCircle size={16} />
                      <Text>
                        {String(
                          t(`import.${error.message}`, {
                            size: `${fileSize(size)}`,
                            type: Object.keys(accept)[0].replace('.', '').toUpperCase(),
                            maxRows,
                            maxLengthTitle,
                            maxLengthContent,
                            ...error.expandData,
                          })
                        )}
                      </Text>
                    </Box>
                  )}
                </Flex>
              </Dropzone>
            </>
          )}
          <Group justify='right' w='100%'>
            <Button variant='default' onClick={handleCancel}>
              {t('actions.cancel', { ns: 'common' })}
            </Button>
            {file && (
              <Button onClick={handleUpload} loading={isUploading}>
                {t('import.confirmImport', { ns: 'article' })}
              </Button>
            )}
          </Group>
        </>
      )}
    </Box>
  );
};

export default ArticleImport;
