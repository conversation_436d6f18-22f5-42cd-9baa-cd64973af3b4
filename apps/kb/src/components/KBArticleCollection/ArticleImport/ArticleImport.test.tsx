import { AllTheProviders } from '@/utils/unitTest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import ArticleImport from '.';

// Mock dependencies
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string, params?: Record<string, any>) => {
      if (!params) return key;
      const { ns, ...otherParams } = params;
      let result = key;
      if (Object.keys(otherParams).length > 0) {
        const paramStrings = Object.entries(otherParams)
          .map(([k, v]) => `${k}=${v}`)
          .sort()
          .join(' ');
        if (paramStrings) {
          result += ` ${paramStrings}`;
        }
      }
      return result;
    },
  }),
}));

vi.mock('@/components/Icons', () => ({
  IconPdfImport: () => <div data-testid='icon-pdf-import' />,
  IconAIGenerator: () => <div data-testid='icon-ai-generator' />,
}));

vi.mock('@/utils', () => ({
  fileSize: (size: number) => `${size}B`,
}));

const MOCK_KB_NAME = 'Test KB';
const MOCK_TEMPLATE_URL = 'http://example.com/template.csv';

const renderComponent = (props = {}) => {
  const defaultProps = {
    KBName: MOCK_KB_NAME,
    template: {
      description: 'Test template',
      url: MOCK_TEMPLATE_URL,
    },
    onUpload: vi.fn(),
    onClose: vi.fn(),
    onChange: vi.fn(),
    isUploading: false,
  };
  return render(
    <AllTheProviders>
      <ArticleImport {...defaultProps} {...props} />
    </AllTheProviders>
  );
};

describe('ArticleImport', () => {
  beforeEach(() => {
    vi.resetAllMocks();
    global.open = vi.fn();
  });

  const getDropzone = () => {
    const dropzoneEl = screen.getByText(/import.dropzone/).parentElement;
    if (!dropzoneEl) throw new Error('Dropzone not found');
    const container = dropzoneEl.parentElement?.parentElement;
    if (!container) throw new Error('Dropzone container not found');
    return container;
  };

  const dropFile = (file: File) => {
    fireEvent.drop(getDropzone(), {
      dataTransfer: {
        files: [file],
        items: [
          {
            kind: 'file',
            type: file.type,
            getAsFile: () => file,
          },
        ],
        types: ['Files'],
      },
    });
  };

  it('should render initial state correctly', () => {
    renderComponent();
    expect(screen.getByText('import.csvInstructions')).toBeInTheDocument();
    expect(screen.getByText('import.downloadTemplate')).toBeInTheDocument();
    expect(screen.getByTestId('icon-pdf-import')).toBeInTheDocument();
    expect(screen.getByText('import.dropzone')).toBeInTheDocument();
    expect(screen.getByText('actions.cancel')).toBeInTheDocument();
    expect(screen.queryByText('import.confirmImport')).not.toBeInTheDocument();
  });

  it('should open the template URL when download button is clicked', async () => {
    renderComponent();
    await userEvent.click(screen.getByText('import.downloadTemplate'));
    expect(global.open).toHaveBeenCalledWith(MOCK_TEMPLATE_URL, '_blank');
  });

  it('should update state when a file is dropped', async () => {
    renderComponent();
    const file = new File(['title,content'], 'test.csv', { type: 'text/csv' });
    dropFile(file);

    expect(await screen.findByText(/import.descriptionConfirm/)).toBeInTheDocument();
    expect(await screen.findByText('import.confirmImport')).toBeInTheDocument();
  });

  it('should show an error when a file that is too large is dropped', async () => {
    renderComponent({ size: 10 });

    const largeFile = new File(['this is a file larger than 10 bytes'], 'large.csv', {
      type: 'text/csv',
    });
    dropFile(largeFile);

    await waitFor(() => {
      expect(screen.getByText('import.file-too-large')).toBeInTheDocument();
    });
  });

  it('should show an error when a file with invalid type is dropped', async () => {
    renderComponent({ accept: { 'text/csv': [] } });
    const invalidFile = new File(['content'], 'image.png', { type: 'image/png' });

    dropFile(invalidFile);

    await waitFor(() => {
      expect(screen.getByText('import.file-invalid-type')).toBeInTheDocument();
    });
  });

  it('should reset state when cancel is clicked after dropping a file', async () => {
    renderComponent();
    const file = new File(['title,content'], 'test.csv', { type: 'text/csv' });
    dropFile(file);

    await waitFor(() => {
      expect(screen.getByText('import.confirmImport')).toBeInTheDocument();
    });

    await userEvent.click(screen.getByText('actions.cancel'));

    expect(screen.getByText('import.csvInstructions')).toBeInTheDocument();
    expect(screen.queryByText('import.confirmImport')).not.toBeInTheDocument();
  });

  it('should call onClose when cancel is clicked without a file', async () => {
    const onClose = vi.fn();
    renderComponent({ onClose });

    await userEvent.click(screen.getByText('actions.cancel'));

    expect(onClose).toHaveBeenCalled();
  });

  it('should call onUpload when import button is clicked', async () => {
    const onUpload = vi.fn().mockResolvedValue(undefined);
    renderComponent({ onUpload });
    const file = new File(['title,content'], 'test.csv', { type: 'text/csv' });

    dropFile(file);

    await waitFor(() => {
      expect(screen.getByText('import.confirmImport')).toBeInTheDocument();
    });

    const importButton = screen.getByText('import.confirmImport');
    await userEvent.click(importButton);

    expect(onUpload).toHaveBeenCalledWith(file, expect.any(Function));

    await waitFor(() => {
      expect(screen.getByText('import.csvInstructions')).toBeInTheDocument();
    });
  });

  it('should display an error if onUpload fails', async () => {
    const errorMessage = { message: 'UPLOAD_FAILED' };
    const onUpload = vi.fn(async (_file, onError) => {
      onError(errorMessage);
    });
    renderComponent({ onUpload });

    const file = new File(['title,content'], 'test.csv', { type: 'text/csv' });
    dropFile(file);

    await waitFor(() => {
      expect(screen.getByText('import.confirmImport')).toBeInTheDocument();
    });

    await userEvent.click(screen.getByText('import.confirmImport'));

    await waitFor(() => {
      expect(screen.getByText('import.UPLOAD_FAILED')).toBeInTheDocument();
    });
  });

  it('should show loading state when isUploading is true', () => {
    renderComponent({ isUploading: true });

    expect(screen.getByText('import.uploading')).toBeInTheDocument();
    expect(screen.getByText('import.uploadingDesc')).toBeInTheDocument();

    expect(screen.queryByText('import.csvInstructions')).not.toBeInTheDocument();
    expect(screen.queryByText('actions.cancel')).not.toBeInTheDocument();
  });
});
