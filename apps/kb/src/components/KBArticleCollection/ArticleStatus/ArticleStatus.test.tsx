import { AllTheProviders } from '@/utils/unitTest';
import { render, screen } from '@testing-library/react';
import { describe, expect, test } from 'vitest';
import ArticleStatus from './index';

describe('ArticleStatus', () => {
  test('renders draft status correctly', () => {
    render(
      <AllTheProviders>
        <ArticleStatus status='draft' />
      </AllTheProviders>
    );

    expect(screen.getByText('publishStatus.draft')).toBeInTheDocument();
  });

  test('renders published status correctly', () => {
    render(
      <AllTheProviders>
        <ArticleStatus status='published' />
      </AllTheProviders>
    );

    expect(screen.getByText('publishStatus.published')).toBeInTheDocument();
  });

  test('renders unknown status as draft by default', () => {
    render(
      <AllTheProviders>
        <ArticleStatus status='unknown' />
      </AllTheProviders>
    );

    expect(screen.getByText('publishStatus.draft')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    const { container } = render(
      <AllTheProviders>
        <ArticleStatus status='published' className='custom-class' />
      </AllTheProviders>
    );

    const element = container.querySelector('span');
    expect(element).toHaveClass('custom-class');
  });

  test('handles empty status by defaulting to draft', () => {
    render(
      <AllTheProviders>
        <ArticleStatus status='' />
      </AllTheProviders>
    );

    expect(screen.getByText('publishStatus.draft')).toBeInTheDocument();
  });

  test('handles null status by defaulting to draft', () => {
    render(
      <AllTheProviders>
        {/* @ts-expect-error: Testing with null status */}
        <ArticleStatus status={null} />
      </AllTheProviders>
    );

    expect(screen.getByText('publishStatus.draft')).toBeInTheDocument();
  });

  test('handles undefined status by defaulting to draft', () => {
    render(
      <AllTheProviders>
        {/* @ts-expect-error: Testing with undefined status */}
        <ArticleStatus status={undefined} />
      </AllTheProviders>
    );

    expect(screen.getByText('publishStatus.draft')).toBeInTheDocument();
  });
});
