import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import type React from 'react';

export interface ArticleStatusProps {
  /** Article status */
  status: string;
  /** Additional CSS class */
  className?: string;
}

const useStyles = createStyles((theme) => ({
  statusBadge: {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: `${rem(6)} ${rem(12)}`,
    borderRadius: rem(16),
    border: 'none',
    fontSize: rem(12),
    fontWeight: 500,
    textTransform: 'none',
    minWidth: rem(80),
    height: rem(24),
    lineHeight: 1,
  },

  draft: {
    backgroundColor: theme.colors.decaYellow[0],
    color: theme.colors.decaYellow[9],
  },

  published: {
    backgroundColor: theme.colors.decaGreen[0],
    color: theme.colors.decaGreen[9],
  },
}));

/**
 * Get the display text for an article status
 */
const getStatusText = (status: string, t: (key: string) => string): string => {
  switch (status) {
    case 'draft':
      return t('publishStatus.draft');
    case 'published':
      return t('publishStatus.published');
    default:
      return t('publishStatus.draft');
  }
};

/**
 * Get the CSS class for an article status
 */
const getStatusClass = (status: string, classes: any): string => {
  switch (status) {
    case 'draft':
      return classes.draft;
    case 'published':
      return classes.published;
    default:
      return classes.draft;
  }
};

/**
 * ArticleStatus component displays the article status as a colored badge
 * Supports Draft and Published statuses with consistent styling
 */
export const ArticleStatus: React.FC<ArticleStatusProps> = ({ status, className = '' }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('article');

  return (
    <span className={cx(classes.statusBadge, getStatusClass(status, classes), className)}>
      {getStatusText(status, t)}
    </span>
  );
};

export default ArticleStatus;
