import { performance } from 'node:perf_hooks';
import * as matchers from '@testing-library/jest-dom/matchers';
import { afterAll, afterEach, beforeAll, expect, vi } from 'vitest';

global.performance = performance;

expect.extend(matchers);

// RAF polyfill
let handle = 0;
const timeouts = new Map();

const raf = (callback) => {
  const id = ++handle;
  const timeoutId = setTimeout(() => {
    callback(performance.now());
    timeouts.delete(id);
  }, 0);
  timeouts.set(id, timeoutId);
  return id;
};

const caf = (id) => {
  const timeoutId = timeouts.get(id);
  if (timeoutId) {
    clearTimeout(timeoutId);
    timeouts.delete(id);
  }
};

window.requestAnimationFrame = raf;
window.cancelAnimationFrame = caf;
global.requestAnimationFrame = raf;
global.cancelAnimationFrame = caf;

// Ensure globalThis also has the polyfill (covers libraries referencing global scope directly)
globalThis.requestAnimationFrame = raf;
globalThis.cancelAnimationFrame = caf;

afterEach(() => {
  for (const timeoutId of timeouts.values()) {
    clearTimeout(timeoutId);
  }
  timeouts.clear();
});

const originalError = console.error;
const originalWarn = console.warn;
// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: (query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => {},
    }),
  });
});

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});

if (typeof global.ResizeObserver === 'undefined') {
  global.ResizeObserver = class MockResizeObserver {
    observe = vi.fn();
    unobserve = vi.fn();
    disconnect = vi.fn();
  };
}

vi.mock('gsap', () => ({
  default: {
    registerPlugin: vi.fn(),
    to: vi.fn(),
    set: vi.fn(),
  },
  gsap: {
    registerPlugin: vi.fn(),
    to: vi.fn(),
    set: vi.fn(),
  },
}));

// Existing mock to cover the module path without extension
vi.mock('gsap/dist/ScrollTrigger', () => ({
  default: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
  ScrollTrigger: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
}));

// Additional mock to cover the explicit .js import path occasionally used by GSAP
vi.mock('gsap/dist/ScrollTrigger.js', () => ({
  default: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
  ScrollTrigger: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
}));

// Catch other path variations for ScrollTrigger
vi.mock('gsap/ScrollTrigger', () => ({
  default: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
  ScrollTrigger: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
}));

vi.mock('gsap/ScrollTrigger.js', () => ({
  default: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
  ScrollTrigger: {
    create: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    refresh: vi.fn(),
    killAll: vi.fn(),
  },
}));
