{"name": "kb", "private": true, "version": "0.14.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "biome lint .", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "biome format --write .", "lint-staged-check": "lint-staged", "lint:eslint:fix": "biome lint --write .", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage", "release": "standard-version -t kb@", "release:minor": "standard-version -t kb@ --release-as minor", "release:patch": "standard-version -t kb@ --release-as patch", "release:major": "standard-version -t kb@ --release-as major"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@datadog/browser-logs": "^6.7.0", "@datadog/browser-rum": "^6.7.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/serialize": "1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@mantine/tiptap": "7.17.7", "@resola-ai/blocknote-editor": "workspace:*", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "^3.17.0", "@testing-library/user-event": "^14.6.1", "@tiptap/react": "^2.10.4", "@tolgee/format-icu": "5.33.2", "@tolgee/react": "5.33.2", "@tolgee/web": "5.33.2", "axios": "^1.8.2", "clsx": "2.1.1", "dayjs": "^1.11.12", "dompurify": "3.2.4", "dotenv": "16.3.1", "lodash": "^4.17.21", "mantine-contextmenu": "7.12.2", "react": "^18.2.0", "react-arborist": "^3.4.0", "react-confetti-explosion": "^2.1.2", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "react-router-dom": "6.21.3", "react-shadow": "^20.6.0", "react-tag-input-component": "2.0.2", "standard-version": "^9.5.0", "ulid": "^2.3.0", "use-between": "^1.3.5", "vite-tsconfig-paths": "^4.2.0", "zod": "^3.24.1"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "@resola-ai/biome-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@sentry/react": "^7.81.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.202", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "2.1.9", "husky": "^8.0.3", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "vite": "5.4.19", "vitest": "2.1.9"}, "lint-staged": {"*.{js,ts,tsx, jsx}": ["biome check --write", "biome lint --write"]}}