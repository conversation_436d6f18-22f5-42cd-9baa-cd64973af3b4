import { DefaultElementProps } from '@/constants';
import { useResponsiveNode } from '@/hooks';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { ObjectFitTypes, PageBuilderElement } from '@/types/enum/builder';
import { useEditor } from '@craftjs/core';
import { Flex, rem } from '@mantine/core';
import clsx from 'clsx';
import { useEffect } from 'react';
import { ContainerSettings } from '../../Settings';
import { BackgroundType } from '../../Settings/BackgroundSettings/types';
import classes from './Container.module.css';
type ContainerElementProps = {
  children: React.ReactNode;
};

const ContainerElement = ({ children }: ContainerElementProps) => {
  const {
    connectors: { connect },
    actions: { setProp },
    selected,
    gap,
    padding,
    align,
    justify,
    direction,
    mediaUrl,
    objectFit,
    width,
    height,
    backgroundColor,
    backgroundType,
    overlay,
    parentId,
    builderPreviewMode,
  } = useResponsiveNode((node) => ({
    selected: node.events.selected,
    gap: node.data.props.gap,
    padding: node.data.props.padding,
    align: node.data.props.align,
    justify: node.data.props.justify,
    direction: node.data.props.direction,
    mediaUrl: node.data.props.mediaUrl,
    objectFit: node.data.props.objectFit,
    width: node.data.props.width,
    height: node.data.props.height,
    backgroundColor: node.data.props.backgroundColor,
    backgroundType: node.data.props.backgroundType,
    overlay: node.data.props.overlay,
    parentId: node.data.parent,
  }));

  const { query } = useEditor();
  const { getThemeColor } = useCurrentTheme();
  const parentNode = parentId ? query.node(parentId).get() : null;
  const parentProps = parentNode?.data.props;
  const maxWidthValue = parentProps?.hasMaxWidth?.[builderPreviewMode]
    ? parentProps?.maxWidth?.[builderPreviewMode]
    : null;

  useEffect(() => {
    // if the parent has maxWidth, set the maxWidth of the container element to the parent's maxWidth
    setProp((props) => (props.maxWidth = maxWidthValue));
  }, [maxWidthValue]);

  return (
    <Flex
      ref={(ref: HTMLDivElement | null) => {
        if (ref) {
          connect(ref);
        }
      }}
      align={align}
      gap={gap}
      justify={justify}
      direction={direction}
      w={rem(width)}
      pos='relative'
      h={rem(height)}
      className={clsx(
        classes.containerElement,
        selected && classes.selectedElement,
        overlay?.enabled && backgroundType === BackgroundType.Image && classes.containerWithOverlay
      )}
      sx={{
        ...(backgroundType === BackgroundType.Image && {
          backgroundImage: `url(${mediaUrl})`,
        }),
        ...(backgroundType === BackgroundType.Color && {
          backgroundColor: getThemeColor(backgroundColor, 'background'),
        }),
        ...(backgroundType === BackgroundType.None && {
          backgroundColor: 'transparent',
        }),
        padding: `${padding.top}px ${padding.right}px ${padding.bottom}px ${padding.left}px`,
        backgroundSize: [ObjectFitTypes.Cover, ObjectFitTypes.Fill].includes(
          objectFit || ObjectFitTypes.Fill
        )
          ? ObjectFitTypes.Cover
          : ObjectFitTypes.Contain,
        position: 'relative',
        ...(overlay?.enabled &&
          backgroundType === BackgroundType.Image && {
            '--overlay-color': overlay.color,
          }),
        maxWidth: maxWidthValue,
      }}
    >
      {children}
    </Flex>
  );
};

ContainerElement.craft = {
  props: DefaultElementProps[PageBuilderElement.ContainerElement],
  related: {
    settings: ContainerSettings,
  },
  rules: {
    canDelete: true,
  },
};

export default ContainerElement;
