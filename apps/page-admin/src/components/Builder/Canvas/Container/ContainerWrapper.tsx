import { DefaultElementProps } from '@/constants';
import { useResponsiveNode } from '@/hooks';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { useAppSelector } from '@/store/hooks';
import { Direction, ObjectFitTypes, PageBuilderElement } from '@/types/enum/builder';
import { getDefaultValuesForResponsiveNode } from '@/utils';
import { Element } from '@craftjs/core';
import { rem } from '@mantine/core';
import clsx from 'clsx';
import { uniqueId } from 'lodash';
import { BackgroundType } from '../../Settings/BackgroundSettings/types';
import ContainerSettings from '../../Settings/ContainerSettings';
import { ElementWrapper } from '../ElementWrapper';
import classes from './Container.module.css';
import { default as ContainerElement } from './ContainerElement';

type ContainerProps = {
  columns?: Record<string, number>;
  direction?: Record<string, Direction>;
  children?: React.ReactNode;
};

const ContainerWrapper = ({
  columns = getDefaultValuesForResponsiveNode(1),
  direction = getDefaultValuesForResponsiveNode(Direction.Column),
  children,
}: ContainerProps) => {
  const { builderPreviewMode } = useAppSelector((state) => state.headerNavigation);
  const { getThemeColor } = useCurrentTheme();
  const {
    width,
    height,
    align,
    justify,
    color,
    fontSize,
    selected,
    padding,
    gap,
    linkedNodes,
    mediaUrl,
    backgroundColor,
    backgroundType,
    overlay,
    objectFit,
  } = useResponsiveNode((node) => ({
    padding: node.data.props.padding,
    gap: node.data.props.gap,
    width: node.data.props.width,
    height: node.data.props.height,
    align: node.data.props.align,
    justify: node.data.props.justify,
    color: node.data.props.color,
    fontSize: node.data.props.fontSize,
    selected: node.events.selected,
    linkedNodes: node.data.linkedNodes,
    mediaUrl: node.data.props.mediaUrl,
    backgroundColor: node.data.props.backgroundColor,
    backgroundType: node.data.props.backgroundType,
    overlay: node.data.props.overlay,
    objectFit: node.data.props.objectFit,
  }));

  const linkedNodesCount = Object.keys(linkedNodes || {}).length;

  return (
    <ElementWrapper
      className={clsx(
        classes.containerWrapper,
        selected && classes.selectedContainer,
        overlay?.enabled && backgroundType === BackgroundType.Image && classes.containerWithOverlay
      )}
      style={{
        width: rem(width),
        height: rem(height),
        minHeight: height ? 'auto' : rem(200),
        alignItems: align,
        justifyContent: justify,
        color: color,
        fontSize: fontSize,
        flexDirection: direction[builderPreviewMode],
        padding: `${rem(padding.top)} ${rem(padding.right)} ${rem(padding.bottom)} ${rem(padding.left)}`,
        gap: gap,
        ...(backgroundType === BackgroundType.Image && {
          backgroundImage: `url(${mediaUrl})`,
        }),
        ...(backgroundType === BackgroundType.Color && {
          backgroundColor: getThemeColor(backgroundColor, 'background'),
        }),
        ...(backgroundType === BackgroundType.None && {
          backgroundColor: 'transparent',
        }),
        backgroundSize: [ObjectFitTypes.Cover, ObjectFitTypes.Fill].includes(
          objectFit || ObjectFitTypes.Fill
        )
          ? ObjectFitTypes.Cover
          : ObjectFitTypes.Contain,
        position: 'relative',
        ...(overlay?.enabled &&
          backgroundType === BackgroundType.Image && {
            '--overlay-color': overlay.color,
          }),
      }}
    >
      {linkedNodesCount > 0
        ? Object.keys(linkedNodes).map((id) => (
            <Element key={id} id={id} is={ContainerElement} canvas>
              {children}
            </Element>
          ))
        : Array.from({ length: columns[builderPreviewMode] }).map((_, index) => (
            <Element id={`container-${uniqueId()}`} key={index} is={ContainerElement} canvas>
              {children}
            </Element>
          ))}
    </ElementWrapper>
  );
};

ContainerWrapper.craft = {
  props: DefaultElementProps[PageBuilderElement.ContainerWrapper],
  related: {
    settings: ContainerSettings,
  },
  rules: {
    canDelete: true,
  },
};

export default ContainerWrapper;
