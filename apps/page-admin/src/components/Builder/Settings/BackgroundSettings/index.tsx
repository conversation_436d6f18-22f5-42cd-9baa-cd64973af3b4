import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { ObjectFitTypes } from '@/types/enum';
import { Box, Flex } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import ColorSettings from '../ColorSettings/ColorSettings';
import SettingTitle from '../SettingTitle';
import BackgroundTypeTabs from './BackgroundTypeTabs';
import ImageBackground from './ImageBackground';
import { type BackgroundSettingsProps, BackgroundType, type OverlaySettings } from './types';

// Re-export ImageOnlyBackground for direct use
export { default as ImageOnlyBackground } from './ImageOnlyBackground';

const BackgroundSettings = ({
  type = BackgroundType.None,
  imageUrl,
  objectFit = ObjectFitTypes.Fill,
  backgroundColor = '#ffffff',
  onTypeChange = () => {},
  onImageChange,
  onFitChange,
  onBackgroundColorChange = () => {},
  overlay = { enabled: false, color: '#00000033' },
  onOverlayChange = () => {},
}: BackgroundSettingsProps) => {
  const { t } = useTranslate('builder');
  const { getThemeColor } = useCurrentTheme();
  const [activeType, setActiveType] = useState<BackgroundType>(type);

  // Sync internal state with props
  useEffect(() => {
    setActiveType(type);
  }, [type]);

  const handleTypeChange = (newType: BackgroundType) => {
    setActiveType(newType);
    if (onTypeChange) {
      onTypeChange(newType);
    }
  };

  const renderBackgroundSettings = () => {
    switch (activeType) {
      case BackgroundType.None:
        return null;
      case BackgroundType.Color:
        return (
          <ColorSettings
            label={t('builder.color')}
            defaultValue={getThemeColor(backgroundColor, 'background')}
            onChange={onBackgroundColorChange}
          />
        );
      case BackgroundType.Image:
        return (
          <ImageBackground
            imageUrl={imageUrl}
            objectFit={objectFit}
            onImageChange={onImageChange}
            onFitChange={onFitChange}
            overlay={overlay as OverlaySettings}
            onOverlayChange={onOverlayChange}
          />
        );
      default:
        return null;
    }
  };

  return (
    <Box p={'md'}>
      <Flex justify='space-between' align='center' mb='md'>
        <SettingTitle title={t('builder.background')} />
        <BackgroundTypeTabs activeType={activeType} onTypeChange={handleTypeChange} />
      </Flex>
      {renderBackgroundSettings()}
    </Box>
  );
};

export default BackgroundSettings;
