import { useUserInfor } from '@/hooks';
import { useResponsiveNode } from '@/hooks/useResponsiveNode';
import type { PaddingValue } from '@/types';
import { Divider, Flex, Stack, Text, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import BackgroundSettings from './BackgroundSettings';
import { BackgroundType } from './BackgroundSettings/types';
import DeleteButton from './DeleteButton';
import LayoutSetting from './LayoutSettings';
import LinkSettings from './LinkSettings';
import PaddingSettings from './PaddingSettings';
import SectionPublish from './SectionPublish/SectionPublish';
import SizeSettings from './SizeSettings';

const ContainerSettings = () => {
  const { t } = useTranslate('builder');
  const {
    actions: { setProp },
    gap,
    padding,
    align,
    justify,
    mediaUrl,
    objectFit,
    overlay,
    backgroundColor,
    backgroundType,
    isContainerWrapper,
    isGrouped,
  } = useResponsiveNode(
    (node) => ({
      gap: node.data.props.gap,
      padding: node.data.props.padding,
      align: node.data.props.align,
      justify: node.data.props.justify,
      mediaUrl: node.data.props.mediaUrl,
      objectFit: node.data.props.objectFit,
      overlay: node.data.props.overlay,
      backgroundColor: node.data.props.backgroundColor,
      backgroundType: node.data.props.backgroundType,
      isContainerWrapper: node.data.props.isContainerWrapper,
      isGrouped: node.data.props.isGrouped,
    }),
    {
      backgroundColor: '#ffffff',
      backgroundType: BackgroundType.Color,
      isGrouped: false,
    }
  );

  const handleImageChange = (imageUrl: string) => {
    setProp((props) => (props.mediaUrl = imageUrl));
  };

  const handleTypeChange = (type: BackgroundType) => {
    setProp((props) => (props.backgroundType = type));
  };

  const handleBackgroundColorChange = (color: string) => {
    setProp((props) => (props.backgroundColor = color));
  };

  const handlePaddingChange = (padding: PaddingValue) => {
    setProp((props) => (props.padding = padding));
  };

  const { isStudioUser } = useUserInfor();

  return (
    <Flex h='100%' direction='column'>
      <Stack gap={0} className='flex-1' c='decaGrey.9'>
        <Text fw={500} c={'decaDark.1'} p={'md'} pb='0'>
          {t('design')}
        </Text>
        <SizeSettings showMaxWidth={isContainerWrapper} />
        <Divider />
        <LayoutSetting
          title={t('layout')}
          gap={gap}
          padding={padding}
          align={align}
          justify={justify}
        />
        <PaddingSettings padding={padding} onChange={handlePaddingChange} />
        <Divider />
        <BackgroundSettings
          type={backgroundType}
          imageUrl={mediaUrl}
          objectFit={objectFit}
          backgroundColor={backgroundColor}
          onTypeChange={handleTypeChange}
          onImageChange={handleImageChange}
          onFitChange={(value) => setProp((props) => (props.objectFit = value))}
          onBackgroundColorChange={handleBackgroundColorChange}
          overlay={{
            ...(overlay || { enabled: false, color: '#00000033' }),
          }}
          onOverlayChange={(value) => setProp((props) => (props.overlay = value))}
        />
        <Divider />
        {!isContainerWrapper && <LinkSettings />}
      </Stack>
      <Divider />

      {(isContainerWrapper || isGrouped) && (
        <Flex px={rem(16)} py={rem(8)} gap={'md'}>
          <DeleteButton />
          {isStudioUser && <SectionPublish />}
        </Flex>
      )}
    </Flex>
  );
};

export default ContainerSettings;
