import ChattingWindowImage from '@/assets/images/chatting-window-connect.svg';
import { useToolbar } from '@/contexts/ToolbarContext';
import usePages from '@/hooks/usePages';
import { ChatbotAPI } from '@/services/api/intergration';
import type { IntegrationPayload } from '@/types/Intergration';
import { Box, Checkbox, Divider, Flex, Image, Menu, Radio, Stack, Text, rem } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { IconChevronDown } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';

interface ExistingChatwindowContentProps {
  chatwindowData?: any; // This will be properly typed when implementing the second case
  integrationData?: any; // For update mode
  onClose: () => void;
  onSuccess?: () => void; // Callback for successful operations
}

const ExistingChatwindowContent = ({
  chatwindowData,
  integrationData,
  onClose,
  onSuccess,
}: ExistingChatwindowContentProps) => {
  const { t } = useTranslate('integrations');
  const { siteId } = useParams();
  const { data } = usePages({ siteId: siteId || '' });
  const selectRef = useRef<HTMLDivElement>(null);
  const textRefs = useRef<Map<string, HTMLButtonElement>>(new Map());
  const { addOutsideRef, removeOutsideRef } = useToolbar();

  const [selectedChatwindow, setSelectedChatwindow] = useState<string | null>(null);
  const [pageOption, setPageOption] = useState<'all_pages' | 'specific_pages'>('specific_pages');
  const [selectedPages, setSelectedPages] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const isUpdateMode = !!integrationData;

  const chatwindowOptions = chatwindowData?.map((c) => ({
    value: c.id,
    label: c.title,
  }));

  const pageOptions = data?.map((p) => ({
    value: p.logical_id,
    label: p.name,
  }));

  useEffect(() => {
    if (selectRef.current) {
      addOutsideRef(selectRef.current);
    }
    // Add all text refs to outside refs
    textRefs.current.forEach((ref) => {
      addOutsideRef(ref);
    });

    return () => {
      if (selectRef.current) {
        removeOutsideRef(selectRef.current);
      }
      // Remove all text refs from outside refs
      textRefs.current.forEach((ref) => {
        removeOutsideRef(ref);
      });
    };
  }, [selectRef.current, textRefs.current]);

  // Initialize form with existing data if in update mode
  useEffect(() => {
    if (isUpdateMode && integrationData) {
      // Set the selected chatwindow from integration data
      setSelectedChatwindow(integrationData.resource_id);

      // Determine page option based on existing configuration
      if (integrationData.options?.page_scope === 'all_pages') {
        setPageOption('all_pages');
        setSelectedPages([]); // Clear selected pages when switching to all
      } else {
        setPageOption('specific_pages');
        setSelectedPages(integrationData.options?.page_ids || []);
      }
    } else if (!isUpdateMode && chatwindowOptions && chatwindowOptions.length > 0) {
      // Only set default for create mode when chatwindow options are available
      setSelectedChatwindow(chatwindowOptions[0].value);
    }
  }, [integrationData, isUpdateMode]); // Remove chatwindowOptions from dependencies to prevent resetting

  // Handle initial default value for create mode
  useEffect(() => {
    if (!isUpdateMode && !selectedChatwindow && chatwindowOptions && chatwindowOptions.length > 0) {
      setSelectedChatwindow(chatwindowOptions[0].value);
    }
  }, [chatwindowOptions, isUpdateMode, selectedChatwindow]);

  const handleSubmit = async () => {
    if (!selectedChatwindow || !siteId) return;

    setIsLoading(true);
    try {
      const payload: IntegrationPayload = {
        site_id: siteId,
        type: 'deca_chatwindow',
        resource_id: selectedChatwindow,
        options: {
          page_scope: pageOption,
          page_ids: selectedPages,
        },
      };

      if (isUpdateMode) {
        await ChatbotAPI.updateIntegration(siteId, payload);
      } else {
        await ChatbotAPI.connectChatwindow(payload);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      console.error(`Failed to ${isUpdateMode ? 'update' : 'connect'} chatwindow:`, error);
      // You might want to show an error notification here
    } finally {
      setIsLoading(false);
    }
  };

  const isSubmitDisabled =
    !selectedChatwindow || (pageOption === 'specific_pages' && selectedPages.length === 0);

  return (
    <Box>
      <Image src={ChattingWindowImage} width={238} />

      <Text fw={500} size='md' mt={20}>
        {isUpdateMode ? t('updateChatwindowConfiguration') : t('connectToChatwindow')}
      </Text>

      <Text
        size='sm'
        c='dimmed'
        mt={8}
        dangerouslySetInnerHTML={{ __html: t('chatwindowConfigurationDescription') }}
      />

      <Divider my={20} />

      {/* Select Chatwindow Dropdown */}
      <Text size='sm' fw={500} mb={4} mt={8}>
        {t('selectChatwindow')}
      </Text>
      <Menu
        shadow='md'
        width='target'
        position='bottom-start'
        withinPortal={false}
        styles={{
          dropdown: {
            maxHeight: 180,
            overflowY: 'auto',
            minWidth: 'var(--mantine-spacing-xs)',
          },
        }}
      >
        <Menu.Target>
          <Box
            ref={selectRef}
            style={{
              border: '1px solid var(--mantine-color-gray-3)',
              borderRadius: 'var(--mantine-radius-sm)',
              padding: '12px',
              cursor: 'pointer',
              backgroundColor: 'white',
              minHeight: 36,
            }}
          >
            <Flex align='center' justify='space-between'>
              <Text size='sm' c={selectedChatwindow ? 'gray.9' : 'gray.5'}>
                {selectedChatwindow
                  ? chatwindowOptions?.find((opt) => opt.value === selectedChatwindow)?.label
                  : t('chatwindowName')}
              </Text>
              <IconChevronDown size={16} />
            </Flex>
          </Box>
        </Menu.Target>

        <Menu.Dropdown>
          {chatwindowOptions?.map((option) => (
            <Menu.Item
              key={option.value}
              onClick={() => setSelectedChatwindow(option.value)}
              ref={(el) => {
                if (el) {
                  textRefs.current.set(option.value, el);
                } else {
                  textRefs.current.delete(option.value);
                }
              }}
            >
              <Text>{option.label}</Text>
            </Menu.Item>
          ))}
        </Menu.Dropdown>
      </Menu>

      {/* Radio for All/Specific pages */}
      <Text size='sm' fw={500} mt={20} mb={4}>
        {t('choosePagesToDisplay')}
      </Text>
      <Radio.Group
        value={pageOption}
        onChange={(value) => {
          setPageOption(value as 'all_pages' | 'specific_pages');
          if (value === 'all') {
            setSelectedPages([]);
          } else {
            setSelectedPages(integrationData.options?.page_ids || []);
          }
        }}
        name='pageOption'
        mb={4}
      >
        <Flex gap={24}>
          <Radio value='all_pages' label={t('allPages')} size='xs' />
          <Radio value='specific_pages' label={t('specificPages')} size='xs' />
        </Flex>
      </Radio.Group>

      {/* Multi-select for pages if "Specific pages" is selected */}
      {pageOption === 'specific_pages' && (
        <Box
          mt={20}
          style={{
            border: '1px solid #E3E3E8',
            borderRadius: 8,
            padding: 8,
            maxHeight: 180,
            overflowY: 'auto',
          }}
        >
          <Stack gap={4}>
            {pageOptions?.map((page) => (
              <Checkbox
                size='xs'
                py={7}
                key={page.value}
                label={page.label}
                checked={selectedPages.includes(page.value)}
                onChange={(e) => {
                  if (e.currentTarget.checked) {
                    setSelectedPages((prev) => [...prev, page.value]);
                  } else {
                    setSelectedPages((prev) => prev.filter((v) => v !== page.value));
                  }
                }}
              />
            ))}
          </Stack>
        </Box>
      )}

      <Flex gap={rem(12)} justify='flex-end' mt={rem(20)} w='100%'>
        <DecaButton variant='neutral' onClick={onClose} disabled={isLoading}>
          {t('cancel')}
        </DecaButton>
        <DecaButton
          variant='primary'
          disabled={isSubmitDisabled}
          loading={isLoading}
          onClick={handleSubmit}
        >
          {isUpdateMode ? t('update') : t('connect')}
        </DecaButton>
      </Flex>
    </Box>
  );
};

export default ExistingChatwindowContent;
