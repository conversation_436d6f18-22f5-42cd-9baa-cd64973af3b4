import PlugImage from '@/assets/images/img-plug.svg';
import LogoChatWindow from '@/assets/images/logo-chatwindow-jp.svg';
import useDecaIntergrations from '@/hooks/useIntergrations';
import { ChatbotAPI } from '@/services/api/intergration';
import type { Integration, IntegrationPayload } from '@/types/Intergration';
import {
  Box,
  Button,
  Divider,
  Flex,
  Image,
  Stack,
  Switch,
  Text,
  Tooltip,
  rem,
  useMantineTheme,
} from '@mantine/core';
import { IconPlus, IconSettings } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import DeactivateConfirmModal from './IntegrationMenu/DeactivateConfirmModal';
import { ConnectChatwindowModal } from './IntegrationMenu/Modals';

const IntegrationMenu = ({
  setOutsideRefs = () => {},
}: {
  setOutsideRefs?: (refs: (HTMLDivElement | null)[]) => void;
}) => {
  const { t } = useTranslate('integrations');
  const theme = useMantineTheme();
  const { siteId } = useParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modal, setModal] = useState<HTMLDivElement | null>(null);
  const [toggleStates, setToggleStates] = useState<Record<string, boolean>>({});
  const [editingIntegration, setEditingIntegration] = useState<any>(null);
  const [deactivateModalOpen, setDeactivateModalOpen] = useState(false);
  const [pendingDeactivateIntegration, setPendingDeactivateIntegration] = useState<any>(null);
  const initializedRef = useRef(false);

  const { integrations, mutate } = useDecaIntergrations(siteId || '');

  useEffect(() => {
    setOutsideRefs([modal]);
  }, [modal]);

  useEffect(() => {
    // Initialize toggle states for all integrations only once
    if (!integrations?.length) return;

    const initialStates: Record<string, boolean> = {};
    integrations.forEach((integration) => {
      initialStates[integration.id] = integration.is_enabled || false;
    });

    setToggleStates({ ...initialStates });
    initializedRef.current = true;
  }, [integrations]);

  const handleOpenModal = (integration?: any) => {
    setEditingIntegration(integration || null);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingIntegration(null);
  };

  const handleToggle = async (integrationId: string, checked: boolean) => {
    const integration = integrations?.find((i) => i.id === integrationId);
    if (!integration || !siteId) return;

    // If turning off, show confirmation modal
    if (!checked) {
      setPendingDeactivateIntegration(integration);
      setDeactivateModalOpen(true);
      return;
    }

    // If turning on, proceed immediately
    await updateIntegrationStatus(integrationId, checked, integration);
  };

  const updateIntegrationStatus = async (
    integrationId: string,
    checked: boolean,
    integration: Integration
  ) => {
    setToggleStates((prev) => ({
      ...prev,
      [integrationId]: checked,
    }));

    try {
      // Create payload according to IntegrationPayload type structure
      const payload: IntegrationPayload = {
        site_id: siteId!,
        type: integration.type,
        is_enabled: checked,
        resource_id: integration.resource_id,
        options: {
          page_scope: integration.options?.page_scope || 'all_pages',
          page_ids: integration.options?.page_ids || [],
        },
      };

      await ChatbotAPI.updateIntegration(siteId!, payload);
      // Refresh the integrations data
      mutate();
    } catch (error) {
      console.error('Failed to update integration:', error);
      // Revert the toggle state on error
      setToggleStates((prev) => ({
        ...prev,
        [integrationId]: !checked,
      }));
    }
  };

  const handleDeactivateConfirm = async () => {
    if (pendingDeactivateIntegration) {
      await updateIntegrationStatus(
        pendingDeactivateIntegration.id,
        false,
        pendingDeactivateIntegration
      );
      setPendingDeactivateIntegration(null);
    }
  };

  const handleSuccess = () => {
    // Refresh integrations data after successful operations
    mutate();
  };

  const renderIntegrationItem = (integration: Integration) => {
    // TODO update status
    const isConnected = integration.status === 'connected';
    const isDisabled = integration.status === 'disabled';

    let statusColor: string;
    let statusBackground: string;
    let statusText: string;

    if (isConnected) {
      statusColor = theme.colors.green[6];
      statusBackground = theme.colors.green[0];
      statusText = t('connected');
    } else if (isDisabled) {
      statusColor = theme.colors.gray[6];
      statusBackground = theme.colors.gray[0];
      statusText = t('disabled');
    } else {
      statusColor = theme.colors.red[6];
      statusBackground = theme.colors.red[0];
      statusText = t('disconnected');
    }

    return (
      <Box
        key={integration.id}
        w='100%'
        h='100%'
        bg='white'
        style={{ border: `1px solid ${theme.colors.gray[3]}`, borderRadius: 12 }}
        p={rem(16)}
      >
        <Flex align='center' gap={12}>
          <Image src={LogoChatWindow} height={28} />
        </Flex>
        <Divider my={12} />
        <Flex align='center' gap={16}>
          <Box>
            <Text fw={600} size='md'>
              {integration.title}
            </Text>
            <Box mt={4}>
              {integration.status === 'disconnected' ? (
                <Tooltip label={t('disconnectedTooltip')} withArrow position='top'>
                  <Text
                    size='xs'
                    fw={500}
                    style={{
                      background: statusBackground,
                      color: statusColor,
                      borderRadius: 8,
                      padding: '2px 10px',
                      display: 'inline-block',
                      cursor: 'help',
                    }}
                  >
                    {statusText}
                  </Text>
                </Tooltip>
              ) : (
                <Text
                  size='xs'
                  fw={500}
                  style={{
                    background: statusBackground,
                    color: statusColor,
                    borderRadius: 8,
                    padding: '2px 10px',
                    display: 'inline-block',
                  }}
                >
                  {statusText}
                </Text>
              )}
            </Box>
          </Box>
        </Flex>
        <Divider my={12} />
        <Flex align='center' justify='space-between'>
          <Button
            variant='subtle'
            leftSection={<IconSettings size={18} />}
            color='decaBlue.7'
            fw={600}
            size='compact-sm'
            onClick={() => handleOpenModal(integration)}
            style={{ paddingLeft: 0 }}
          >
            {t('settings')}
          </Button>
          <Switch
            checked={toggleStates[integration.id] || false}
            onChange={(e) => handleToggle(integration.id, e.currentTarget.checked)}
            color='green'
            size='md'
            styles={{ track: { minWidth: 40 } }}
          />
        </Flex>
      </Box>
    );
  };

  return (
    <>
      <Flex
        direction='column'
        align='center'
        justify='flex-start'
        w={280}
        p='md'
        style={{ position: 'relative' }}
      >
        {/* Header always visible */}
        <Flex w='100%' align='center' justify='space-between' mb='xl'>
          <Text fw={700} size='md'>
            {t('integration')}
          </Text>
          <Tooltip
            label={t('maxIntegrationsReached')}
            disabled={!integrations || integrations.length === 0}
            withArrow
            position='bottom'
          >
            <Button
              variant='transparent'
              size='xs'
              p={0}
              style={{
                minWidth: 0,
                height: 28,
                width: 28,
                border: 'none',
                background: 'transparent',
                color:
                  integrations && integrations.length > 0
                    ? theme.colors.gray[4]
                    : theme.colors.gray[6],
              }}
              disabled={integrations && integrations.length > 0}
              onClick={() => handleOpenModal()}
            >
              <IconPlus size={18} />
            </Button>
          </Tooltip>
        </Flex>
        {/* Body conditional */}
        {integrations && integrations.length > 0 ? (
          <Box
            style={{
              flex: 1,
              overflowY: 'auto',
              width: '100%',
              paddingRight: 4,
            }}
          >
            <Stack gap={12}>{integrations.map(renderIntegrationItem)}</Stack>
          </Box>
        ) : (
          <Flex direction='column' align='center' justify='center' h='100%' style={{ flex: 1 }}>
            <Image
              src={PlugImage}
              alt='Plug'
              width={180}
              height={180}
              style={{ objectFit: 'contain' }}
            />
            <Text fw={600} size='md' mt='md'>
              {t('noIntegrationsFound')}
            </Text>
            <Text color='dimmed' ta='center' mt={18} mb={24} size='sm'>
              {t('noIntegrationsDescription')}
            </Text>
            <Button size='sm' onClick={() => handleOpenModal()}>
              {t('addNewIntegration')}
            </Button>
          </Flex>
        )}
      </Flex>

      <ConnectChatwindowModal
        opened={isModalOpen}
        onClose={handleCloseModal}
        setModalRef={setModal}
        integrationData={editingIntegration}
        onSuccess={handleSuccess}
      />

      <DeactivateConfirmModal
        opened={deactivateModalOpen}
        onClose={() => {
          setDeactivateModalOpen(false);
          setPendingDeactivateIntegration(null);
        }}
        onConfirm={handleDeactivateConfirm}
        integrationName={pendingDeactivateIntegration?.title || ''}
      />
    </>
  );
};

export default IntegrationMenu;
