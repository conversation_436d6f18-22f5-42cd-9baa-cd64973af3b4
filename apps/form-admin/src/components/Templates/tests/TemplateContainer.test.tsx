import {describe, it, vi} from 'vitest';
import TemplateContainer from '../TemplateContainer';
import type {Form} from '@/types';
import {render} from "@/utils/test-util/testUiComponent";

vi.mock('@/services/api', () => ({
  FormsAPI: {
    getTemplates: vi.fn()
  },
  TagsAPI: {
    getTags: vi.fn()
  }
}))

describe('TemplateContainer', () => {
  const mockOnChooseTemplate = vi.fn();

  const defaultProps = {
    onChooseTemplate: mockOnChooseTemplate,
    chosenTemplate: null as Form | null,
    isPage: false,
  };

  it('should render TemplateSelectContainer when isPage is false', () => {
    render(<TemplateContainer {...defaultProps} />);

  });

  it('should render TemplatePageContainer when isPage is true', () => {
    render(<TemplateContainer {...defaultProps} isPage={true}/>);

  });
});
