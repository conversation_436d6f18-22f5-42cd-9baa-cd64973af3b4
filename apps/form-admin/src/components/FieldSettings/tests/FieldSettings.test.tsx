import { describe, it } from 'vitest';
import FieldSettings from '../index';
import { render } from '@/utils/test-util/testUiComponent';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { mockFormBuilderData } from '@/utils/test-util/formFieldRender';
import {
  BuilderRightMenuType,
  Integration,
  IntegrationSectionTypes,
  ResultTab,
  ShareAction,
} from '@/types';
import { AppearanceSettingsOptionType } from '@/types/enum'; // Assuming i18n is set up properly in your project

describe('FieldSettings Component', () => {
  const renderComponent = (id: string) => {
    return render(
      <DndProvider backend={HTML5Backend}>
        <FieldSettings />
      </DndProvider>,
      {
        builder: {
          builderJson: mockFormBuilderData,
          selectingSectionId: mockFormBuilderData.content?.[0]!.id,
          selectingShareSection: ShareAction.Share,
          selectingResultSection: ResultTab.Results,
          rightMenuType: BuilderRightMenuType.Question,
          selectedAppearanceSettings: AppearanceSettingsOptionType.Default,
          formSaving: null,
          selectedIntegrationSection: IntegrationSectionTypes.Installed,
          selectedIntegrationItem: {} as Integration,
          resultHiddenFieldIds: [] as string[],
          resultPinnedColumnId: null,
          screenshot: null,
          currentViewId: 'default',
          isDragging: false,
          currentHistoryKey: '',
          selectingFieldId: id,
        },
      }
    );
  };
  (mockFormBuilderData.content?.[0] as any).content.forEach(element => {
    it(`render question setting type ${element.type}`, () => {
      renderComponent(element.id);
    });
  });
});
