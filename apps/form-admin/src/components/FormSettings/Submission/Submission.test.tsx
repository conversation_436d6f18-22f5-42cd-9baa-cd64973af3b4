import { screen } from '@testing-library/react';
import { vi } from 'vitest';
import Submission from './index';
import { render } from '@/utils/test-util/testUiComponent';
import { FormProvider, useForm } from '@/contexts';
import { PropsWithChildren } from 'react';

const mockFormData = {
  name: 'Test Form',
  tags: ['tag1', 'tag2'],
  startAt: '2023-01-01T00:00:00Z',
  expiredAt: '2023-12-31T00:00:00Z',
  setting: {
    submission: {
      mode: 'test-mode',
      message: 'test-message',
      caption: 'test-caption',
      button: 'test-button',
      redirectUrl: 'https://example.com',
      enableBranding: true,
      limitResponse: true,
      limitNumber: 10,
      thankMessage: { emailQuestionId: '12345' },
    },
  },
};
const MockWrapper = ({ children }: PropsWithChildren) => {
  const form = useForm({
    initialValues: mockFormData,
  });

  return <FormProvider form={form}>{children}</FormProvider>;
};

describe('Submission Component', () => {
  const mockProps = {
    onSubmit: vi.fn(),
  };

  const setup = (props = mockProps) => {
    return render(
      <MockWrapper>
        <Submission {...props} />
      </MockWrapper>
    );
  };

  it('should render without crashing', () => {
    setup();
    expect(screen.getByText('decaBrandingLabel')).toBeInTheDocument();
  });
});
