import {screen} from '@testing-library/react';
import <PERSON><PERSON>ield from '../ActionField';
import {vi} from 'vitest';
import {render} from "@/utils/test-util/testUiComponent";

vi.mock('@/services/api', () => ({
  FormsAPI: {
    exportResultCsv: vi.fn(),
  },
}));

vi.mock('@/utils/assetExportHelper', () => ({
  triggerExportFiles: vi.fn(),
}));

describe('ActionField Component', () => {
  const mockViews = [
    {id: 'view1', name: 'View 1'},
    {id: 'view2', name: 'View 2'},
  ];

  const setup = () =>
      render(<ActionField views={mockViews}/>);

  it('should render the component correctly', () => {
    setup();
    expect(screen.getByText('View 1')).toBeInTheDocument();
  });

});
