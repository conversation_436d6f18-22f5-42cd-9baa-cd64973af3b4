import {fireEvent, screen} from '@testing-library/react';
import FilterField from '../FilterField';
import {render} from "@/utils/test-util/testUiComponent";

describe('FilterField Component', () => {

  it('should render FilterField and check default components', () => {
    render(<FilterField/>);

    const filterButton = screen.getByText('filter');
    expect(filterButton).toBeInTheDocument();

    fireEvent.click(filterButton);

  });
});
