import {describe, it, beforeEach, vi, expect} from 'vitest';
import {screen, fireEvent} from '@testing-library/react';
import ConditionField from '../ConditionField';
import {FieldType} from "@/types";
import {render} from "@/utils/test-util/testUiComponent";

describe('ConditionField Component', () => {
  const mockOnChangeField = vi.fn();
  const mockOnCopyCondition = vi.fn();
  const mockOnRemoveCondition = vi.fn();

  let props: any;

  const questions = [
    {
      id: 'question1',
      label_text: 'Question 1',
      type: FieldType.ShortQA,
      options: [],
    },
    {
      id: 'question2',
      label_text: 'Question 2',
      type: FieldType.Dropdown,
      name: 'prefecture',
      options: [{label: 'Option 1'}, {label: 'Option 2'}],
    },
  ];

  const field = {
    questionId: 'question1',
    value: '',
    type: '$and',
    fieldType: FieldType.ShortQA,
  };

  beforeEach(() => {
    props = {
      questions,
      index: 0,
      field,
      onChangeField: mockOnChangeField,
      onCopyCondition: mockOnCopyCondition,
      onRemoveCondition: mockOnRemoveCondition,
    };
  });
  
  it('handles value change correctly', () => {
    render(<ConditionField {...props} />);
    const input = screen.getByPlaceholderText('enterValue');

    fireEvent.change(input, {target: {value: 'new value'}});
    expect(mockOnChangeField).toHaveBeenCalledWith(
        {...field, value: 'new value'},
        0
    );
  });

});
