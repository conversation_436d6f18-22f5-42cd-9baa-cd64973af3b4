import ResultsTable from '../ResultsTable';
import { render } from '@/utils/test-util/testUiComponent';

describe('ResultsTable', () => {
  const questions = [
    {
      isDeleted: false,
      isHidden: false,
      label: 'Sample Label',
      labelText: 'Sample Label Text',
      questionId: 'q1',
      type: 'text',
    },
    {
      isDeleted: false,
      isHidden: false,
      label: 'Sample Label',
      labelText: 'Sample Label Text',
      questionId: 'q2',
      type: 'text',
    },
  ];
  const responses = {
    response: [
      {
        id: '1',
        created_by: { ip: '***********', user_agent: 'Chrome' },
        created_at: '2023-10-01T00:00:00Z',
        pages: {},
      },
    ],
    pagination: { totalItem: 2, perPage: 2, currentPage: 1, totalPage: 2 },
  } as any;
  const baseProps = {
    questions,
    responses,
    loading: false,
  };

  const renderComponent = (propsOverride = {}) =>
    render(<ResultsTable {...baseProps} {...propsOverride} />);

  it('renders loading overlay when loading is true', () => {
    renderComponent({ loading: true });
  });
  it('renders "No Responses" when there are no responses', () => {
    renderComponent({ responses: null });
  });

  it('renders the results table when responses exist', () => {
    renderComponent();
  });
});
