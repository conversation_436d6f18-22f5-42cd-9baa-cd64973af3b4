import {fireEvent} from '@testing-library/react';
import {describe, it, expect, vi} from 'vitest';
import DragField from '../DragField';
import {CustomizeFieldInfo} from '@/types';
import {render} from "@/utils/test-util/testUiComponent";

describe('DragField Component', () => {
  const mockMoveItem = vi.fn();
  const mockOnToggle = vi.fn();
  const mockOnMouseDown = vi.fn();

  const field: CustomizeFieldInfo = {
    label: 'test_label',
    isHidden: false,
    isDeleted: false,
    type: 'line',
    labelText: 'Test Label',
    questionId: 'q1',
  };

  const renderDragField = () =>
      render(
          <DragField
              field={field}
              index={1}
              moveItem={mockMoveItem}
              onMouseDown={mockOnMouseDown}
              onToggle={mockOnToggle}
          >
            Test Children
          </DragField>
      );

  it('should render properly', () => {
    const {getByText} = renderDragField();

    expect(getByText('Test Label')).toBeInTheDocument();
  });

  it('should call onMouseDown when div is clicked', () => {
    const {container} = renderDragField();
    const mainDiv = container.querySelector('div[data-handler-id]')!;
    fireEvent.mouseDown(mainDiv);
    expect(mockOnMouseDown).toHaveBeenCalled();
  });
  
});
