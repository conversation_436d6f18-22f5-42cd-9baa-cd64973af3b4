import FormBase from '../FormBuilder/FormBase';
import { BuilderRightMenuType, ResultTab, ShareAction } from '@/types/form-builder';
import { AppearanceSettingsOptionType } from '@/types/enum';
import { render } from '@/utils/test-util/testUiComponent';
import { Integration, IntegrationSectionTypes } from '@/types';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { mockFormBuilderData } from '@/utils/test-util/formFieldRender';

describe('FormBase', () => {
  const renderComponent = (props = {}) => {
    return render(
      <DndProvider backend={HTML5Backend}>
        <FormBase content={mockFormBuilderData.content!} {...props} />
      </DndProvider>,
      {
        builder: {
          builderJson: mockFormBuilderData,
          selectingSectionId: 'section-1',
          selectingShareSection: ShareAction.Share,
          selectingResultSection: ResultTab.Results,
          rightMenuType: BuilderRightMenuType.Question,
          selectedAppearanceSettings: AppearanceSettingsOptionType.Default,
          formSaving: null,
          selectedIntegrationSection: IntegrationSectionTypes.Installed,
          selectedIntegrationItem: {} as Integration,
          resultHiddenFieldIds: [] as string[],
          resultPinnedColumnId: null,
          screenshot: null,
          currentViewId: 'default',
          isDragging: false,
          currentHistoryKey: '',
        },
      }
    );
  };

  it('renders form fields correctly', () => {
    const { getByText } = renderComponent();
    expect(getByText('Your Name')).toBeInTheDocument();
  });
});
