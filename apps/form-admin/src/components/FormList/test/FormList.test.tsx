import { screen } from '@testing-library/react';
import { describe, it, vi, expect, Mock } from 'vitest';
import FormList from '../FormList';
import useForms from '@/hooks/useForms';
import { useWorkspaceContext } from '@/contexts';
import { render } from '@/utils/test-util/testUiComponent';

// Mock dependencies
vi.mock('@/hooks/useForms', () => ({
  default: vi.fn(),
}));
vi.mock('@/contexts', () => ({
  useWorkspaceContext: vi.fn(),
}));

describe('<FormList />', () => {
  const defaultProps = {
    isGridView: true,
    search: '',
    page: 1,
    setPage: vi.fn(),
    perPage: 10,
    setPerPage: vi.fn(),
    filter: '',
    isFavorite: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should display the loading overlay when loading', () => {
    (useForms as Mock).mockReturnValue({
      response: null,
      isLoading: true,
      isError: false,
      mutate: vi.fn(),
      totalPage: 1,
      isValidating: false,
    });
    (useWorkspaceContext as Mock).mockReturnValue({
      isCreatingForm: false,
    });

    render(<FormList {...defaultProps} />);
  });

  it('should display an error alert when there is an error', () => {
    (useForms as Mock).mockReturnValue({
      response: null,
      isLoading: false,
      isError: true,
      mutate: vi.fn(),
      totalPage: 1,
      isValidating: false,
    });
    (useWorkspaceContext as Mock).mockReturnValue({
      isCreatingForm: false,
    });

    render(<FormList {...defaultProps} />);
    expect(screen.getByText('Error')).toBeInTheDocument();
  });

  it('should display the NoFormScreen when there are no forms', () => {
    (useForms as Mock).mockReturnValue({
      response: [],
      isLoading: false,
      isError: false,
      mutate: vi.fn(),
      totalPage: 1,
      isValidating: false,
    });
    (useWorkspaceContext as Mock).mockReturnValue({
      isCreatingForm: false,
    });

    render(<FormList {...defaultProps} />);
  });

  it('should display the GridView when isGridView is true', () => {
    (useForms as Mock).mockReturnValue({
      response: [{ id: 1, name: 'Test Form' }],
      isLoading: false,
      isError: false,
      mutate: vi.fn(),
      totalPage: 1,
      isValidating: false,
    });
    (useWorkspaceContext as Mock).mockReturnValue({
      isCreatingForm: false,
    });

    render(<FormList {...defaultProps} />);
  });

  it('should display the TableView when isGridView is false', () => {
    (useForms as Mock).mockReturnValue({
      response: [{ id: 1, name: 'Test Form', integrations: [] }],
      isLoading: false,
      isError: false,
      mutate: vi.fn(),
      totalPage: 1,
      isValidating: false,
    });
    (useWorkspaceContext as Mock).mockReturnValue({
      isCreatingForm: false,
    });

    render(<FormList {...defaultProps} isGridView={false} />);
  });

  it('should render PaginationControl with the correct props', () => {
    (useForms as Mock).mockReturnValue({
      response: [{ id: 1, name: 'Test Form' }],
      isLoading: false,
      isError: false,
      mutate: vi.fn(),
      totalPage: 5,
      isValidating: false,
    });
    (useWorkspaceContext as Mock).mockReturnValue({
      isCreatingForm: false,
    });

    render(<FormList {...defaultProps} />);
  });
});
