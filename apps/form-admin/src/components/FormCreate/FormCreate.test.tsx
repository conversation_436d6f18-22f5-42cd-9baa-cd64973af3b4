import { fireEvent, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import FormCreate from './FormCreate';
import { render } from '@/utils/test-util/testUiComponent';

describe('FormCreate', () => {
  const renderFormCreate = () => render(<FormCreate />);

  it('should render the form correctly', () => {
    renderFormCreate();
  });

  it('should validate required fields on submit', async () => {
    renderFormCreate();

    const createButton = screen.getByText('createForm');

    fireEvent.click(createButton);

    expect(screen.getByText('validationFormNameRequired')).toBeInTheDocument();
  });

  it('should show validation error for tag limit', async () => {
    renderFormCreate();

    const tagsInput = screen.getByPlaceholderText('tagsPlaceHolder');
    const createButton = screen.getByText('createForm');

    const tags = new Array(101).fill('tag').join(',');
    fireEvent.input(tagsInput, tags);
    fireEvent.click(createButton);

  });

  it('should validate date dependencies', async () => {
    renderFormCreate();

    // Enable start date and leave it empty
    const startDateToggle = screen.getByLabelText('startDate');
    fireEvent.click(startDateToggle);

    const createButton = screen.getByText('createForm');
    fireEvent.click(createButton);
    expect(screen.getByText('validationStartDateRequired')).toBeInTheDocument();

    // Enable end date and leave it empty
    const endDateToggle = screen.getByLabelText('endDate');
    fireEvent.click(endDateToggle);
    fireEvent.click(createButton);
    expect(screen.getByText('validationEndDateRequired')).toBeInTheDocument();
  });

  it('should submit the form with valid data', async () => {
    renderFormCreate();

    const nameInput = screen.getByPlaceholderText('formNamePlaceHolder');
    const tagsInput = screen.getByPlaceholderText('tagsPlaceHolder');
    const startDateToggle = screen.getByLabelText('startDate');
    const createButton = screen.getByText('createForm');

    fireEvent.input(nameInput, 'Test Form');
    fireEvent.input(tagsInput, 'tag1,tag2');
    fireEvent.click(startDateToggle);
    fireEvent.click(createButton);
  });
});
