import { screen, fireEvent } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import EmbedSettings from '../Embed';
import { render } from '@/utils/test-util/testUiComponent';

describe('<EmbedSettings />', () => {
  it('renders embed header text correctly', () => {
    render(<EmbedSettings />);
    expect(screen.getByText('embed')).toBeInTheDocument();
  });

  it('renders the tabs and displays "Design" component on default', () => {
    render(<EmbedSettings />);
    // Check if the tabs are rendered
    expect(screen.getByText('embedDesign')).toBeInTheDocument();
    expect(screen.getByText('hiddenFields')).toBeInTheDocument();
  });

  it('switches to HiddenFields tab and renders the component on click', () => {
    render(<EmbedSettings />);
    const hiddenFieldsTab = screen.getByText('hiddenFields');

    fireEvent.click(hiddenFieldsTab);
  });

  it('check settings fullPage and renders the component on click', () => {
    render(<EmbedSettings />);
    const fullPageTab = screen.getByText('fullPage');

    fireEvent.click(fullPageTab);
  });
  it('check settings popup and renders the component on click', () => {
    render(<EmbedSettings />);
    const fullPageTab = screen.getByText('popup');

    fireEvent.click(fullPageTab);
  });
  it('check settings slider and renders the component on click', () => {
    render(<EmbedSettings />);
    const fullPageTab = screen.getByText('slider');

    fireEvent.click(fullPageTab);
  });
  it('check settings popover and renders the component on click', () => {
    render(<EmbedSettings />);
    const fullPageTab = screen.getByText('popover');

    fireEvent.click(fullPageTab);
  });

  it('check settings sidebar and renders the component on click', () => {
    render(<EmbedSettings />);
    const fullPageTab = screen.getByText('sidebar');

    fireEvent.click(fullPageTab);
  });
});
