import { fireEvent, screen } from '@testing-library/react';
import ShareSettings from '../Share';
import { render } from '@/utils/test-util/testUiComponent';

describe('ShareSettings', () => {
  it('renders correctly with default tab', () => {
    render(<ShareSettings />);
    expect(screen.getByText('sharing')).toBeInTheDocument();
    expect(screen.getByText('preview')).toBeInTheDocument();
    expect(screen.getByText('hiddenFields')).toBeInTheDocument();
  });

  it('switches tab when clicked', async () => {
    render(<ShareSettings />);
    const hiddenTab = screen.getByText('hiddenFields');
    fireEvent.click(hiddenTab);
  });

  it('applies activeTab style to the active tab', async () => {
    render(<ShareSettings />);
    const hiddenTab = screen.getByText('hiddenFields');
    fireEvent.click(hiddenTab);
  });
});
