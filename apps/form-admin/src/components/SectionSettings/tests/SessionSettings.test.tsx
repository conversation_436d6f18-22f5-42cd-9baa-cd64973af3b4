import { screen, fireEvent } from '@testing-library/react';
import SectionSettings from '../index';
import { render } from '@/utils/test-util/testUiComponent';

describe('SectionSettings', () => {
  const renderComponent = () => render(<SectionSettings />);

  it('should render the section title', () => {
    renderComponent();
    expect(screen.getByText(/editPage/i)).toBeInTheDocument();
  });

  it('should render all tabs with their labels translated', () => {
    renderComponent();
    expect(screen.getByText(/fieldsTabLayout/i)).toBeInTheDocument();
    expect(screen.getByText(/layoutTabLabel/i)).toBeInTheDocument();
    expect(screen.getByText(/hiddenFieldTabLabel/i)).toBeInTheDocument();
  });

  it('should display correct component when switching tabs', () => {
    renderComponent();

    // Check default tab component rendered
    expect(screen.getByText(/fieldsTabLayout/i)).toBeInTheDocument(); // Replace with appropriate content in FieldList

    // Switch to "layout" tab
    fireEvent.click(screen.getByText(/layoutTabLabel/i));

    // Switch to "hiddenField" tab
    fireEvent.click(screen.getByText(/hiddenFieldTabLabel/i));
  });

  it('should update the active tab class when a tab is clicked', () => {
    renderComponent();

    const layoutTab = screen.getByText(/layoutTabLabel/i);
    fireEvent.click(layoutTab);

    // Validate the active tab has the appropriate class
  });
});
