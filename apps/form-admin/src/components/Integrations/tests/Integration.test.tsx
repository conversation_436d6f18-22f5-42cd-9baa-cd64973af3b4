import Integrations from '../index';
import { expect, Mock, vi } from 'vitest';
import { render } from '@/utils/test-util/testUiComponent';
import { useSearchParams } from 'react-router-dom';
import { Integration } from '@/types';
import { IntegrationAPI } from '@/services/api';
import { waitFor, screen } from '@testing-library/react';
import { IntegrationType } from '@/types/enum';

vi.mock('react-router-dom', async importOriginal => {
  const original = (await importOriginal()) as any;
  return {
    ...original,
    useSearchParams: vi.fn(),
  };
});

vi.mock('@/services/api', async importOriginal => {
  const original = (await importOriginal()) as any;
  return {
    ...original,
    IntegrationAPI: {
      get: vi.fn(),
    },
  };
});

const mockIntegration: Integration = {
  id: 'integration_123',
  type: IntegrationType.Email,
  form_id: 'form_456',
  form_name: 'Example Form',
  settings: {
    to: ['<EMAIL>'],
    url: 'https://api.example.com/endpoint',
    headers: {
      Authorization: 'Bearer example-token',
      'Content-Type': 'application/json',
    },
    headersTemp: [
      { id: 'header_1', key: 'Authorization', value: 'Bearer example-token' },
      { id: 'header_2', key: 'Content-Type', value: 'application/json' },
    ],
    base_id: 'base_789',
    base_name: 'Example Base',
    table_name: 'Example Table',
    table_id: 'table_1011',
    mappings: [
      {
        question_id: 'question_1',
        field_type: 'text',
        field_name: 'Example Field',
        is_excluded: false,
        field_id: 'field_123',
      },
      {
        question_id: 'question_2',
        field_type: 'email',
        field_name: 'Email Field',
        is_excluded: true,
        field_id: 'field_456',
      },
    ],
    mappings_temp: [{ tempKey: 'tempValue1' }, { tempKey: 'tempValue2' }],
    channel_id: 'channel_123',
    channel_secret: 'channel_secret_key',
    access_token: 'access_token_key',
    account_id: 'account_456',
    liff_app_name: 'Example App',
    liff_id: 'liff_789',
    liff_mode: 'compact',
  },
  is_enabled: true,
  created_by: 'user_123',
  created_at: '2023-10-20T12:00:00Z',
  updated_at: '2023-10-21T12:00:00Z',
  allForm: false,
  form_ids: ['form_123', 'form_456'],
};

describe('Integrations Component', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders LoadingOverlay no integrationId ', async () => {
    (useSearchParams as Mock).mockReturnValue([new URLSearchParams()]);
    render(<Integrations />);
  });

  it('renders List when integrationId is present', () => {
    const urlSearchParams = new URLSearchParams('int_id=123');
    (useSearchParams as Mock).mockReturnValue([urlSearchParams]);
    (IntegrationAPI.get as Mock).mockReturnValue(mockIntegration);
    render(<Integrations />);
    waitFor(() => {
      expect(screen.getByText('backToMenuLabel')).toBeInTheDocument();
    });
  });
});
