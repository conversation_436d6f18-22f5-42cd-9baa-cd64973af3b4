import { screen, fireEvent } from '@testing-library/react';
import { describe, it, vi, expect } from 'vitest';
import AddLineModal from '../AddLineModal';
import { render } from '@/utils/test-util/testUiComponent';
import { IntegrationTypes } from '@/constants';

// Mock translation function
describe('<AddLineModal />', () => {
  it('renders the modal with proper elements', () => {
    render(<AddLineModal opened={true} type={IntegrationTypes.Line} onClose={vi.fn} />);
    expect(screen.getByText('connectLineTitle')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'cancelLabel' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'next' })).toBeInTheDocument();
  });

  it('calls onClose on cancel button click', () => {
    render(<AddLineModal opened={true} type={IntegrationTypes.Line} onClose={vi.fn} />);

    const cancelButton = screen.getByRole('button', { name: 'cancelLabel' });
    fireEvent.click(cancelButton);
  });

  it('validates form and prevents progression if errors exist', async () => {
    render(<AddLineModal opened={true} type={IntegrationTypes.Line} onClose={vi.fn} />);

    const nextButton = screen.getByRole('button', { name: 'next' });
    fireEvent.click(nextButton);
  });
});
