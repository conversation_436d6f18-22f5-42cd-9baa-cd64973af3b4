import { screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import AddMultipleFormModal from '../AddMultipleFormModal';
import { render } from '@/utils/test-util/testUiComponent';
import { IntegrationTypes } from '@/constants';

vi.mock('@/services/api', () => ({
  IntegrationAPI: {
    updateIntegrationWorkspace: vi.fn(),
  },
}));

describe('AddMultipleFormModal', () => {
  const onCloseMock = vi.fn();
  const onSuccessMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderComponent = (opened = true, type = IntegrationTypes.Webhook) => {
    render(
      <AddMultipleFormModal
        opened={opened}
        type={type}
        onClose={onCloseMock}
        onSuccess={onSuccessMock}
      />
    );
  };

  it('renders modal correctly when opened', () => {
    renderComponent(true);

    expect(screen.getByText('emailNotificationsTitle')).toBeInTheDocument();
    expect(screen.getByLabelText('applyToAllForms')).toBeInTheDocument();
    expect(screen.getByText('cancelLabel')).toBeInTheDocument();
    expect(screen.getByText('addLabel')).toBeInTheDocument();
  });

  it('calls onClose when Cancel button is clicked', () => {
    renderComponent(true);

    const cancelButton = screen.getByText('cancelLabel');
    fireEvent.click(cancelButton);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });
});
