import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';
import ConnectTablesModal from '../ConnectTablesModal';
import { render } from '@/utils/test-util/testUiComponent';

vi.mock('@/services/api', () => ({
  IntegrationAPI: {
    create: vi.fn(() =>
      Promise.resolve({
        id: 1,
        name: 'Test Integration',
      })
    ),
  },
}));

describe('ConnectTablesModal', () => {
  const onCloseMock = vi.fn();

  const renderComponent = (
    props: Partial<React.ComponentProps<typeof ConnectTablesModal>> = {}
  ) => {
    render(<ConnectTablesModal opened={true} type='someType' onClose={onCloseMock} {...props} />);
  };

  it('renders modal with correct title for the BaseTableSelection step', () => {
    renderComponent();
  });

  it('calls onClose when cancel button is clicked', async () => {
    renderComponent();
    const cancelButton = screen.getByText('cancelLabel');
    fireEvent.click(cancelButton);
  });

  it('calls the next step when next button is clicked', async () => {
    renderComponent();
    const nextButton = screen.getByText('connectButton');
    fireEvent.click(nextButton);
    // Add assertions here as needed to verify next step behavior
  });
});
