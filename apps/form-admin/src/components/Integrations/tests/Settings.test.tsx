import { fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import Settings from '../Settings';

import { render } from '@/utils/test-util/testUiComponent';
import { Integration } from '@/types';
import { IntegrationTypes } from '@/constants';

vi.mock('@/services/api', async importOriginal => {
  const original = (await importOriginal()) as any;
  return {
    ...original,
    IntegrationAPI: {
      get: vi.fn(),
      listBases: vi.fn(() => []),
    },
  };
});

describe('Settings Component', () => {
  const mockIntegration: Integration = {
    id: 'integration_123',
    type: IntegrationTypes.Email,
    form_id: 'form_456',
    form_name: 'Example Form',
    settings: {
      to: ['<EMAIL>'],
      url: 'https://api.example.com/endpoint',
      headers: {
        Authorization: 'Bearer example-token',
        'Content-Type': 'application/json',
      },
      headersTemp: [
        { id: 'header_1', key: 'Authorization', value: 'Bearer example-token' },
        { id: 'header_2', key: 'Content-Type', value: 'application/json' },
      ],
      base_id: 'base_789',
      base_name: 'Example Base',
      table_name: 'Example Table',
      table_id: 'table_1011',
      mappings: [
        {
          question_id: 'question_1',
          field_type: 'text',
          field_name: 'Example Field',
          is_excluded: false,
          field_id: 'field_123',
        },
        {
          question_id: 'question_2',
          field_type: 'email',
          field_name: 'Email Field',
          is_excluded: true,
          field_id: 'field_456',
        },
      ],
      mappings_temp: [{ tempKey: 'tempValue1' }, { tempKey: 'tempValue2' }],
      channel_id: 'channel_123',
      channel_secret: 'channel_secret_key',
      access_token: 'access_token_key',
      account_id: 'account_456',
      liff_app_name: 'Example App',
      liff_id: 'liff_789',
      liff_mode: 'compact',
    },
    is_enabled: true,
    created_by: 'user_123',
    created_at: '2023-10-20T12:00:00Z',
    updated_at: '2023-10-21T12:00:00Z',
    allForm: false,
    form_ids: ['form_123', 'form_456'],
  };

  const fetchLatestDataMock = vi.fn();

  const defaultProps = {
    integration: mockIntegration,
    fetchLatestData: fetchLatestDataMock,
  };

  const renderComponent = (props = defaultProps) => {
    return render(<Settings {...props} />);
  };

  it('renders the Settings Email component successfully', () => {
    renderComponent({
      integration: {
        ...mockIntegration,
        type: IntegrationTypes.Email,
      },
      fetchLatestData: fetchLatestDataMock,
    });
  });

  it('renders the Settings Line component successfully', () => {
    renderComponent({
      integration: {
        ...mockIntegration,
        type: IntegrationTypes.Line,
      },
      fetchLatestData: fetchLatestDataMock,
    });
  });

  it('renders the Settings Table component successfully', () => {
    renderComponent({
      integration: {
        ...mockIntegration,
        type: IntegrationTypes.Tables,
      },
      fetchLatestData: fetchLatestDataMock,
    });
  });

  it('renders the Settings Webhook component successfully', () => {
    renderComponent({
      integration: {
        ...mockIntegration,
        type: IntegrationTypes.Webhook,
      },
      fetchLatestData: fetchLatestDataMock,
    });
  });

  it('navigates back to form builder when "Back to menu" is clicked', () => {
    const { getByText } = renderComponent();

    const backButton = getByText('backToMenuLabel');

    fireEvent.click(backButton);

    expect(fetchLatestDataMock).toHaveBeenCalled();
  });
});
