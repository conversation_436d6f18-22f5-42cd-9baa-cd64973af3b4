import { useFormContext } from '@/contexts';
import { useFormQuestions } from '@/hooks';
import { IntegrationAPI } from '@/services/api';
import { useAppSelector } from '@/store/hooks';
import type { Integration } from '@/types';
import { IntegrationUtils } from '@/utils';
import { Box, Container, Grid, LoadingOverlay, Text, rem } from '@mantine/core';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import useSWR from 'swr';
import MappingItem from '../ConnectTablesModal/FieldsMapping/MappingItem';
import BasesSelection from '../ConnectTablesModal/Selecting/BasesSelection';
import TablesSelection from '../ConnectTablesModal/Selecting/TablesSelection';

type Props = {
  updateAt?: string;
};

const TablesIntegrationSettings = ({ updateAt }: Props) => {
  const { t } = useTranslation('integrations');
  const currentFormId = useAppSelector((state) => state.builder.currentFormId);
  const params = {
    filter: JSON.stringify({ is_deleted: false, type: { $ne: 'line_id' } }),
  };
  const { data: questionsData } = useFormQuestions(currentFormId, params);

  const form = useFormContext();
  const formValues = form.getValues() as Integration;
  const baseId = formValues.settings.base_id || '';
  const tableId = formValues.settings.table_id || '';
  const [fields, setFields] = useState([]);

  const _mappingsTemp = IntegrationUtils.tranformTableMappingData(
    questionsData,
    formValues.settings?.mappings
  );

  useEffect(() => {
    if (questionsData?.length) {
      const _settings = { ...formValues.settings, mappings_temp: _mappingsTemp };
      // Re-initialize the form values to trigger the form validation
      form.setValues({ settings: _settings });
    }
  }, [questionsData]);

  const { data: bases, isValidating: isFetchingBases } = useSWR(
    '/integrations/tables',
    () => IntegrationAPI.listBases(),
    {
      revalidateOnMount: true,
      fallbackData: [],
    }
  );

  const { data: tables, isValidating: isFetchingTables } = useSWR(
    baseId ? `/integrations/tables?baseId=${baseId}` : null,
    () => IntegrationAPI.listTables(baseId || ''),
    {
      revalidateIfStale: true,
      fallbackData: [],
    }
  );

  const { isValidating: isFetchingFields } = useSWR(
    tableId && baseId ? [`/integrations/tables/${baseId}/${tableId}`, updateAt] : null,
    () => IntegrationAPI.retrieveTable(tableId, baseId),
    {
      revalidateIfStale: true,
      onSuccess: (data) => {
        if (data.fields) {
          setFields(
            data.fields.map((field) => ({
              label: field.name,
              value: JSON.stringify({ id: field.id, name: field.name, type: field.type }),
              type: field.type,
            }))
          );
        }
      },
    }
  );

  if (isFetchingBases) return <LoadingOverlay visible />;

  const onTableChange = () => {
    const _mappingsTemp = IntegrationUtils.tranformTableMappingData(
      questionsData,
      formValues.settings?.mappings_temp
    );
    form.setFieldValue('settings.mappings_temp', _mappingsTemp);
  };

  return (
    <Container className='w-full' fluid py={rem(24)}>
      <LoadingOverlay visible={isFetchingFields || isFetchingTables} />
      <Box component='div' mb={rem(20)} w={'70%'}>
        <Text c={'decaGrey.9'} fw={500}>
          {t('baseLabel')}
        </Text>
        <Text my={rem(8)} c='decaGrey.4' fz={rem(12)}>
          {t('baseDescription')}
        </Text>
        {!!bases?.length && <BasesSelection bases={bases} />}
      </Box>
      <Box component='div' w={'70%'}>
        <Text c={'decaGrey.9'} fw={500} mb={rem(8)}>
          {t('tableLabel')}
        </Text>
        {!!tables?.length && <TablesSelection tables={tables} onTableChange={onTableChange} />}
      </Box>

      <Text my={rem(28)}>{t('matchQuestionsTablesDesc')}</Text>

      <Box w={'85%'}>
        <Container fluid p={0}>
          <Grid>
            <Grid.Col span={6}>{t('formsLabel')}</Grid.Col>
            <Grid.Col span={6}>{t('tableLabel')}</Grid.Col>
          </Grid>

          <Grid>
            <Grid.Col span={6}>{t('numberOfFields', { count: questionsData?.length })}</Grid.Col>
            <Grid.Col span={6}>{t('tableDescription')}</Grid.Col>
          </Grid>

          {questionsData?.map((question, index) => (
            <Grid key={question.id}>
              <MappingItem index={index} question={question} fields={fields} />
            </Grid>
          ))}
        </Container>
      </Box>
    </Container>
  );
};

export default TablesIntegrationSettings;
