import { screen, fireEvent } from '@testing-library/react';
import { describe, it, vi, beforeEach, afterEach } from 'vitest';
import AddButton from './index';
import { render } from '@/utils/test-util/testUiComponent';

vi.mock('@tabler/icons-react', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    IconPlus: vi.fn(() => <svg data-testid='icon-plus'></svg>),
  };
});

describe('AddButton', () => {
  const onClickMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should render AddButton correctly', () => {
    render(<AddButton onClick={onClickMock} />);

    // Verify the IconPlus renders inside the ActionIcon
    const iconPlus = screen.getByTestId('icon-plus');
    expect(iconPlus).toBeInTheDocument();
  });

  it('should call onClick when AddButton is clicked', () => {
    render(<AddButton onClick={onClickMock} />);

    // Simulate click
    const actionIcon = screen.getByTestId('icon-plus');
    fireEvent.click(actionIcon);

    // Verify the onClickMock is called
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });
});
