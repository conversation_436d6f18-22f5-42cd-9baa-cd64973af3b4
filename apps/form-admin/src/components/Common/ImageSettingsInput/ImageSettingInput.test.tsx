import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import ImageSettingsInput from './ImageSettingsInput';
import { render } from '@/utils/test-util/testUiComponent';

vi.mock('@tabler/icons-react', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    IconCircleXFilled: props => (
      <span data-testid='mock-icon-x' {...props}>
        IconCircleXFilled
      </span>
    ),
    IconTrash: props => (
      <span data-testid='mock-icon-trash' {...props}>
        IconTrash
      </span>
    ),
  };
});

vi.mock('react-i18next', async importOriginal => {
  const original = (await importOriginal()) as any;
  return {
    ...original,
    useTranslation: () => ({
      t: (key: string) => key,
    }),
  };
});

describe('ImageSettingsInput Component', () => {
  const mockOnChange = vi.fn();
  const mockOnRemove = vi.fn();

  const defaultProps = {
    hasImage: false,
    label: 'Test Label',
    onChange: mockOnChange,
    onRemove: mockOnRemove,
    disabled: false,
    errorMessage: '',
    imageField: undefined,
  };

  const renderComponent = (props = {}) =>
    render(<ImageSettingsInput {...defaultProps} {...props} />);

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with label', () => {
    renderComponent();

    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  it('renders addImage button when hasImage is false', () => {
    renderComponent();

    expect(screen.getByText('addImage')).toBeInTheDocument();
  });

  it('renders changeImage and remove buttons when hasImage is true', () => {
    renderComponent({ hasImage: true });

    expect(screen.getByText('changeImage')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /trash/i })).toBeInTheDocument();
  });

  it('calls onChange when file input changes', () => {
    const { container } = renderComponent();

    const fileInput = container.querySelector('input[type="file"]');
    expect(fileInput).toBeTruthy();
    fireEvent.change(fileInput!, {
      target: { files: [new File(['test'], 'test.png', { type: 'image/png' })] },
    });

    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls onRemove when remove button is clicked', () => {
    renderComponent({ hasImage: true });

    const removeButton = screen.getByTestId('mock-icon-trash');
    fireEvent.click(removeButton);

    expect(mockOnRemove).toHaveBeenCalled();
  });

  it('disables buttons when disabled prop is true', () => {
    renderComponent({ disabled: true });

    const addButton = screen.getByRole('button');
    expect(addButton).toBeDisabled();
  });

  it('shows error dialog when errorMessage is present', async () => {
    renderComponent({ errorMessage: 'Test Error', imageField: new File(['test'], 'test.png') });

    await waitFor(() => {
      expect(screen.getByText('Test Error')).toBeInTheDocument();
    });
  });

  it('closes error dialog after 3 seconds', async () => {
    renderComponent({ errorMessage: 'Test Error', imageField: new File(['test'], 'test.png') });

    await waitFor(
      () => {
        expect(screen.queryByText('Test Error')).not.toBeInTheDocument();
      },
      { timeout: 3500 }
    );
  });
});
