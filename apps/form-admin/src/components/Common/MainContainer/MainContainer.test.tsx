import { screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import MainContainer from './index';
import { render } from '@/utils/test-util/testUiComponent';

describe('MainContainer', () => {
  it('renders the title correctly', () => {
    const title = 'Test Title';
    render(
      <MainContainer title={title}>
        <div>Test Content</div>
      </MainContainer>
    );

    const titleElement = screen.getByText(title);
    expect(titleElement).toBeInTheDocument();
  });

  it('renders the children correctly', () => {
    render(
      <MainContainer title='Sample Title'>
        <div>Test Content</div>
      </MainContainer>
    );

    const childrenElement = screen.getByText('Test Content');
    expect(childrenElement).toBeInTheDocument();
  });

  it('contains a divider', () => {
    render(
      <MainContainer title='Sample Title'>
        <div>Test Content</div>
      </MainContainer>
    );

    const divider = screen.getByRole('separator');
    expect(divider).toBeInTheDocument();
  });
});
