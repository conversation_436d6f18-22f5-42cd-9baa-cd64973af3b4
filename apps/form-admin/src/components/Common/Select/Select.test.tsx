import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import Select from './index';
import { SelectItem } from '@/types';
import { render } from '@/utils/test-util/testUiComponent';

describe('Select Component', () => {
  const sampleData: SelectItem[] = [
    { value: '1', label: 'Option 1', icon: null },
    { value: '2', label: 'Option 2', icon: null, description: 'Description 2' },
    { value: '3', label: 'Option 3', icon: null },
  ];

  const renderWithProvider = (props: any) => {
    return render(<Select {...props} />);
  };

  test('renders with default placeholder', () => {
    renderWithProvider({ data: sampleData });
    expect(screen.getByText('Please select')).toBeInTheDocument();
  });

  test('renders options correctly when the dropdown is opened', () => {
    renderWithProvider({ data: sampleData });

    // Simulate dropdown opening
    const button = screen.getByRole('button');
    fireEvent.click(button);

    // Check if dropdown options are rendered
    const option1 = screen.getByText('Option 1');
    const option2 = screen.getByText('Option 2');
    const option3 = screen.getByText('Option 3');

    expect(option1).toBeInTheDocument();
    expect(option2).toBeInTheDocument();
    expect(screen.getByText('Description 2')).toBeInTheDocument();
    expect(option3).toBeInTheDocument();
  });

  test('calls onChange callback when an option is selected', () => {
    const onChangeMock = vi.fn();

    renderWithProvider({ data: sampleData, onChange: onChangeMock });

    // Open dropdown and select an option
    fireEvent.click(screen.getByRole('button'));
    const option = screen.getByText('Option 1');
    fireEvent.click(option);

    // Check if onChange is called with the correct value
    expect(onChangeMock).toHaveBeenCalledWith('1');
  });

  test('renders the selected value correctly', () => {
    renderWithProvider({ data: sampleData, defaultValue: '2' });

    // Check if the default selected value is rendered
    waitFor(() => {
      expect(screen.getByText('Option 2')).toBeInTheDocument();
      expect(screen.getByText('Description 2')).toBeInTheDocument();
    });
  });

  test('does not open dropdown when disabled', () => {
    renderWithProvider({ data: sampleData, disabled: true });

    // Check if dropdown doesn't open when button is clicked
    const button = screen.getByRole('button');
    fireEvent.click(button);
    expect(screen.queryByText('Option 1')).not.toBeInTheDocument();
    expect(screen.queryByText('Option 2')).not.toBeInTheDocument();
  });
});
