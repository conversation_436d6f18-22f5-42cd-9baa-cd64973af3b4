import { fireEvent, screen, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import BuilderNavigationBar from './BuilderNavigationBar';
import { render } from '@/utils/test-util/testUiComponent';
import { FormBuilderSteps } from '@/types';

vi.mock('@tabler/icons-react', async importOriginal => {
  const originalModule = (await importOriginal()) as any;
  return {
    ...originalModule,
    IconHome: (props: any) => <div {...props} data-testid='mock-icon-home' />,
    IconStar: (props: any) => <div {...props} data-testid='mock-icon-star' />,
    IconStarFilled: (props: any) => <div {...props} data-testid='mock-icon-star-filled' />,
    IconLoader2: (props: any) => <div {...props} data-testid='mock-icon-loader' />,
    IconCheck: (props: any) => <div {...props} data-testid='mock-icon-check' />,
    IconEye: (props: any) => <div {...props} data-testid='mock-icon-eye' />,
  };
});

vi.mock('@/hooks', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    useFormData: vi.fn(() => ({
      form: {
        name: 'Mock Form Name',
        isPublished: false,
        toggleFavorite: vi.fn(),
        saveName: vi.fn(),
        openPreview: vi.fn(),
        togglePublish: vi.fn(),
      },
      mutate: vi.fn(),
    })),
  };
});

vi.mock('@/services/api', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    FormsAPI: {
      create: vi.fn(),
      updateForm: vi.fn(),
    },
  };
});

describe('BuilderNavigationBar', () => {
  beforeEach(() => {
    vi.spyOn(console, 'error').mockImplementation(() => {}); // Suppress error logs in tests
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderComponent = () =>
    render(<BuilderNavigationBar pages={[]} onStep={vi.fn()} step={FormBuilderSteps.Create} />);

  it('should render BuilderNavigationBar component', () => {
    renderComponent();
    expect(screen.getByText(/create/i)).toBeInTheDocument();
    expect(screen.getByText(/share/i)).toBeInTheDocument();
    expect(screen.getByText(/integrations/i)).toBeInTheDocument();
    expect(screen.getByText(/settings/i)).toBeInTheDocument();
    expect(screen.getByText(/results/i)).toBeInTheDocument();
  });

  it('should handle home button click', () => {
    renderComponent();
    const homeButton = screen.getByTestId('mock-icon-home');
    fireEvent.click(homeButton);
    expect(window.location.pathname).toBe('/forms/');
  });

  it('should toggle editing name', async () => {
    renderComponent();
    const editButton = screen.getByText('Mock Form Name');
    fireEvent.click(editButton);

    const inputBox = screen.getByRole('textbox');
    expect(inputBox).toBeInTheDocument();

    fireEvent.blur(inputBox, { target: { value: 'New Name' } });
    waitFor(() => {
      expect(screen.queryByRole('textbox')).not.toBeInTheDocument();
    });
  });

  it('should toggle favorite state', async () => {
    renderComponent();
    const favoriteButton = screen.getByTestId('mock-icon-star');
    fireEvent.click(favoriteButton);

    // Mock mutations should be called here
    expect(true).toBe(true); // Placeholder, verify correct mutation logic handling
  });

  it('should handle publish/unpublish toggle', async () => {
    renderComponent();
    const publishButton = screen.getByText(/publish/i); // Replace with dynamic text for publish/unpublish
    fireEvent.click(publishButton);
  });
});
