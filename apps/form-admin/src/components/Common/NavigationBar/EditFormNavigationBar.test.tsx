import { screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import EditFormNavigationBar from './EditFormNavigationBar';
import { render } from '@/utils/test-util/testUiComponent';

// Mock Tabler icons
vi.mock('@tabler/icons-react', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    IconColorFilter: () => <svg data-testid='icon-color-filter' />,
    IconHelp: () => <svg data-testid='icon-help' />,
    IconArrowBackUp: () => <svg data-testid='icon-arrow-back' />,
    IconArrowForwardUp: () => <svg data-testid='icon-arrow-forward' />,
  };
});

describe('EditFormNavigationBar', () => {
  vi.mock('@/hooks/useBuilderHistory', () => ({
    useBuilderHistory: () => ({
      undo: vi.fn(),
      redo: vi.fn(),
      canUndo: true,
      canRedo: false,
      undoTooltip: 'Undo Tooltip',
      redoTooltip: 'Redo Tooltip',
    }),
  }));

  it('renders correctly with all buttons', () => {
    render(<EditFormNavigationBar />);

    // Check if buttons are rendered
    expect(screen.getByText('common.pagetab.question')).toBeInTheDocument();
    expect(screen.getByText('common.pagetab.design')).toBeInTheDocument();
    expect(screen.getByTestId('icon-arrow-back')).toBeInTheDocument();
    expect(screen.getByTestId('icon-arrow-forward')).toBeInTheDocument();
  });

  it('calls undo function when undo button is clicked', () => {
    render(<EditFormNavigationBar />);
    const undoButton = screen.getByTestId('icon-arrow-back').closest('button');
    fireEvent.click(undoButton!);
  });

  it('does not call redo function when redo button is clicked and disabled', () => {
    render(<EditFormNavigationBar />);
    const redoButton = screen.getByTestId('icon-arrow-forward').closest('button');
    expect(redoButton).toBeDisabled();
    fireEvent.click(redoButton!);
  });

  it('dispatches action when a menu button is clicked', () => {
    render(<EditFormNavigationBar />);
    const questionButton = screen.getByText('common.pagetab.question');
    fireEvent.click(questionButton);
  });
});
