import { screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import '@testing-library/jest-dom';
import CopyBtn from './index';
import { render } from '@/utils/test-util/testUiComponent';

// Mock the icons
vi.mock('@tabler/icons-react', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    IconCircleCheckFilled: () => <span data-testid='mock-icon-circle-check'>IconCircleCheck</span>,
    IconCopy: props => (
      <span data-testid='mock-icon-copy' {...props}>
        IconCopy
      </span>
    ),
  };
});

describe('CopyBtn Component', () => {
  test('should render with default props (button type)', () => {
    render(<CopyBtn value='Sample Text' />);

    expect(screen.getByText('copy')).toBeInTheDocument();
    expect(screen.getByTestId('mock-icon-copy')).toBeInTheDocument();
  });

  test('should render as an icon when type is "icon"', () => {
    render(<CopyBtn value='Sample Text' type='icon' />);

    expect(screen.getByTestId('mock-icon-copy')).toBeInTheDocument();
  });

  test('should show dialog on copy action and close it after 3 seconds', async () => {
    render(<CopyBtn value='Sample Text' message='Copied Successfully' />);

    fireEvent.click(screen.getByText('copy'));

    await waitFor(() => {
      expect(screen.queryByText('Copied Successfully')).toBeInTheDocument();
    });
    await waitFor(
      () => {
        expect(screen.queryByText('Copied Successfully')).not.toBeInTheDocument();
      },
      {
        timeout: 4000,
      }
    );
  });

  test('custom label should render correctly', () => {
    render(<CopyBtn value='Sample Text' label='Custom Label' />);

    expect(screen.getByText('Custom Label')).toBeInTheDocument();
  });

  test('close dialog when close button is clicked', () => {
    render(<CopyBtn value='Sample Text' message='Copied Successfully' />);

    const button = screen.getByText('copy');
    fireEvent.click(button);

    expect(screen.queryByText('Copied Successfully')).not.toBeInTheDocument();
  });

  test('should show dialog on copy icon and close it after 3 seconds', async () => {
    render(<CopyBtn value='Sample Text' message='Copied Successfully' type='icon' />);

    fireEvent.click(screen.getByTestId('mock-icon-copy'));

    await waitFor(() => {
      expect(screen.queryByText('Copied Successfully')).toBeInTheDocument();
    });
    await waitFor(
      () => {
        expect(screen.queryByText('Copied Successfully')).not.toBeInTheDocument();
      },
      {
        timeout: 4000,
      }
    );
  });
});
