import { fireEvent, screen } from '@testing-library/react';
import { vi } from 'vitest';

import Pagination from './index';
import { render } from '@/utils/test-util/testUiComponent'; // Replace with the actual file name of the component

vi.mock('@tabler/icons-react', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    IconChevronLeft: () => <div data-testid='icon-chevron-left'>MockLeftIcon</div>,
    IconChevronRight: () => <div data-testid='icon-chevron-right'>MockRightIcon</div>,
  };
});

vi.mock('react-i18next', async importOriginal => {
  const origin = (await importOriginal()) as any;
  return {
    ...origin,
    useTranslation: vi.fn(() => ({
      t: (key: string) => key,
    })),
  };
});

describe('Pagination Component', () => {
  it('renders correctly', () => {
    render(
      <Pagination onPrev={vi.fn()} onNext={vi.fn()} prevDisabled={false} nextDisabled={false} />
    );

    expect(screen.getByText('prev')).toBeInTheDocument();
    expect(screen.getByText('next')).toBeInTheDocument();
    expect(screen.getByTestId('icon-chevron-left')).toBeInTheDocument();
    expect(screen.getByTestId('icon-chevron-right')).toBeInTheDocument();
  });

  it('disables previous button when `prevDisabled` is true', () => {
    render(
      <Pagination onPrev={vi.fn()} onNext={vi.fn()} prevDisabled={true} nextDisabled={false} />
    );

    const prevButton = screen.getByText('prev').parentElement;
    expect(prevButton).toHaveStyle({ pointerEvents: 'none' });
  });

  it('disables next button when `nextDisabled` is true', () => {
    render(
      <Pagination onPrev={vi.fn()} onNext={vi.fn()} prevDisabled={false} nextDisabled={true} />
    );

    const nextButton = screen.getByText('next').parentElement;
    expect(nextButton).toHaveStyle({ pointerEvents: 'none' });
  });

  it('calls `onPrev` when the previous button is clicked', () => {
    const onPrevMock = vi.fn();
    render(
      <Pagination onPrev={onPrevMock} onNext={vi.fn()} prevDisabled={false} nextDisabled={false} />
    );

    const prevButton = screen.getByText('prev').closest('div');
    fireEvent.click(prevButton!);
    expect(onPrevMock).toHaveBeenCalledTimes(1);
  });

  it('calls `onNext` when the next button is clicked', () => {
    const onNextMock = vi.fn();
    render(
      <Pagination onPrev={vi.fn()} onNext={onNextMock} prevDisabled={false} nextDisabled={false} />
    );

    const nextButton = screen.getByText('next').closest('div');
    fireEvent.click(nextButton!);
    expect(onNextMock).toHaveBeenCalledTimes(1);
  });
});
