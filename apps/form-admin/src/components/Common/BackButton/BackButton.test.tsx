import { describe, it, vi, expect } from 'vitest';
import { screen } from '@testing-library/react';
import BackButton from './index';
import { render } from '@/utils/test-util/testUiComponent'; // Adjust the path based on your file structure

// Mock the IconChevronLeft from Tabler
vi.mock('@tabler/icons-react', async () => {
  const actual = await vi.importActual<any>('@tabler/icons-react');
  return {
    ...actual,
    IconChevronLeft: () => <span data-testid='mock-icon-chevron-left' />,
  };
});

describe('BackButton Component', () => {
  it('renders without crashing', () => {
    render(<BackButton className='test-class' onClick={() => {}} />);

    // Asserts that the button renders with class
    expect(screen.getByRole('button')).toHaveClass('test-class');
  });

  it('renders the mocked IconChevronLeft', () => {
    render(<BackButton onClick={() => {}} />);

    // Assert that the mocked icon is rendered
    expect(screen.getByTestId('mock-icon-chevron-left')).toBeInTheDocument();
  });

  it('triggers the onClick event handler when clicked', async () => {
    const mockOnClick = vi.fn();
    render(<BackButton onClick={mockOnClick} />);

    const button = screen.getByRole('button');
    button.click();

    // Assert that the onClick handler was called
    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });
});
