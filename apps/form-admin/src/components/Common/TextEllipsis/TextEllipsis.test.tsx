import { screen } from '@testing-library/react';
import { describe, expect, it } from 'vitest';
import TextEllipsis from './index';
import { render } from '@/utils/test-util/testUiComponent';

describe('TextEllipsis', () => {
  it('renders the children correctly', () => {
    render(<TextEllipsis>Test Content</TextEllipsis>);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('applies the correct styles for ellipsis with default lines', () => {
    render(<TextEllipsis>Test Content</TextEllipsis>);
  });

  it('applies the correct styles for custom number of lines', () => {
    render(<TextEllipsis lines={3}>Test Content</TextEllipsis>);
  });

  it('applies placeholder color when isPlaceholder is true', () => {
    render(<TextEllipsis isPlaceholder>Test Content</TextEllipsis>);
  });

  it('applies the correct custom color', () => {
    render(<TextEllipsis color='red.5'>Test Content</TextEllipsis>);
  });

  it('allows overriding styles with a custom className', () => {
    render(<TextEllipsis className='custom-class'>Test Content</TextEllipsis>);
  });
});
