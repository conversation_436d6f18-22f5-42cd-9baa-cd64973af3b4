import { describe, it, vi } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import DropdownMenu from './index';
import { DropdownMenuItem } from '@/types';
import { render } from '@/utils/test-util/testUiComponent';

describe('DropdownMenu', () => {
  const mockItems: DropdownMenuItem[] = [
    {
      label: 'Item 1',
      action: vi.fn(),
      icon: <span data-testid='icon-1'>Icon 1</span>,
    },
    {
      label: 'Item 2',
      action: vi.fn(),
      icon: <span data-testid='icon-2'>Icon 2</span>,
    },
  ];

  it('renders the trigger element', () => {
    render(<DropdownMenu trigger={<button>Trigger</button>} items={mockItems} />);
    expect(screen.getByText('Trigger')).toBeInTheDocument();
  });

  it('renders all menu items', () => {
    render(<DropdownMenu trigger={<button>Trigger</button>} items={mockItems} />);
    fireEvent.click(screen.getByText('Trigger')); // Simulate opening the dropdown
    waitFor(() => {
      const menuItems = screen.getAllByTestId('menu-item');
      expect(menuItems.length).toBe(mockItems.length);
      mockItems.forEach((item, index) => {
        expect(menuItems[index]).toHaveTextContent(item.label);
      });
    });
  });

  it('calls the correct action when a menu item is clicked', () => {
    render(<DropdownMenu trigger={<button>Trigger</button>} items={mockItems} />);
    fireEvent.click(screen.getByText('Trigger')); // Simulate opening the dropdown
    waitFor(() => {
      const menuItems = screen.getAllByTestId('menu-item');
      fireEvent.click(menuItems[0]); // Click the first menu item
      expect(mockItems[0].action).toHaveBeenCalled();
    });
  });

  it('displays the correct icons for each menu item', () => {
    render(<DropdownMenu trigger={<button>Trigger</button>} items={mockItems} />);
    fireEvent.click(screen.getByText('Trigger')); // Simulate opening the dropdown
    waitFor(() => {
      mockItems.forEach((_, index) => {
        expect(screen.getByTestId(`icon-${index + 1}`)).toBeInTheDocument();
      });
    });
  });
});
