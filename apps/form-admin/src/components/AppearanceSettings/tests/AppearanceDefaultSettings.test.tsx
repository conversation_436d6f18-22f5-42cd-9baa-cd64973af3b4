import { screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import AppearanceDefaultSettings from '../AppearanceDefaultSettings';
import { render } from '@/utils/test-util/testUiComponent';
import { TestAppearanceWrapper } from '@/utils/test-util/appearanceForm';

describe('AppearanceDefaultSettings Component', () => {
  it('renders the general appearance settings', () => {
    render(
      <TestAppearanceWrapper>
        <AppearanceDefaultSettings />
      </TestAppearanceWrapper>
    );

    expect(screen.getByText(/appearance\.general/i)).toBeInTheDocument();
    expect(screen.getByText(/appearance\.accentColor/i)).toBeInTheDocument();
    expect(screen.getByText(/appearance\.defaultFont/i)).toBeInTheDocument();
    expect(screen.getByText('appearance.inputStyle')).toBeInTheDocument();
  });
});
