import { screen } from '@testing-library/react';
import { describe, it } from 'vitest';
import AppearanceSettings from '../AppearanceSettings';
import { render } from '@/utils/test-util/testUiComponent';

describe('AppearanceSettings Component', () => {
  it('should render without crashing', () => {
    render(<AppearanceSettings />);

    // Check if the header containing the title renders
    expect(screen.getByText('appearance.title')).toBeInTheDocument();
  });
});
