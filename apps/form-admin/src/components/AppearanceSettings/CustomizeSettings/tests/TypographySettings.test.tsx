import { screen } from '@testing-library/react';
import TypographySettings from '../TypographySettings';
import { render } from '@/utils/test-util/testUiComponent';
import { TestAppearanceWrapper } from '@/utils/test-util/appearanceForm';

// Mock useAppSelector

describe('TypographySettings', () => {
  it('renders the default font settings', () => {
    render(
      <TestAppearanceWrapper>
        <TypographySettings />
      </TestAppearanceWrapper>
    );
    expect(screen.getByText('appearance.customizeElement.typography')).toBeInTheDocument();
    expect(screen.getByText('appearance.defaultFont')).toBeInTheDocument();
  });
});
