import { screen } from '@testing-library/react';
import ColorSettings from '../ColorSettings';
import { render } from '@/utils/test-util/testUiComponent';
import { TestAppearanceWrapper } from '@/utils/test-util/appearanceForm';

describe('ColorSettings Component', () => {
  it('should render with default settings', () => {
    render(
      <TestAppearanceWrapper>
        <ColorSettings />
      </TestAppearanceWrapper>
    );

    expect(screen.getByText(/appearance.customizeElement.color/i)).toBeInTheDocument();
    expect(screen.getByText(/appearance.element.heading/i)).toBeInTheDocument();
    expect(screen.getByText(/appearance.element.paragraph/i)).toBeInTheDocument();
    expect(screen.getByText(/appearance.element.buttonBackground/i)).toBeInTheDocument();
    expect(screen.getByText(/appearance.element.buttonText/i)).toBeInTheDocument();
  });
});
