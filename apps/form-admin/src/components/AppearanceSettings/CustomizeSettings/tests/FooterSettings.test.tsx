import { screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import FooterSettings from '../FooterSettings';
import { TestAppearanceWrapper } from '@/utils/test-util/appearanceForm';
import { render } from '@/utils/test-util/testUiComponent';

describe('FooterSettings', () => {
  it('renders the title label with translated text', () => {
    render(
      <TestAppearanceWrapper>
        <FooterSettings />
      </TestAppearanceWrapper>
    );
    expect(screen.getByText('appearance.customizeElement.footer')).toBeInTheDocument();
  });
});
