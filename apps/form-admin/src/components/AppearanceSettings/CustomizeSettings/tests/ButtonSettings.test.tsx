import { screen, fireEvent } from '@testing-library/react';
import { describe, it } from 'vitest';
import ButtonSettings from '../ButtonSettings';
import { FormProvider, useForm } from 'react-hook-form';
import { render } from '@/utils/test-util/testUiComponent';
// assumed test i18n config

describe('ButtonSettings', () => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    const methods = useForm({
      defaultValues: {
        buttonStyle: {
          fullWidth: false,
          type: 'rounded',
          backgroundColor: '',
          textColor: '',
          fontSize: '',
          fontFamily: '',
        },
      },
    });

    return <FormProvider {...methods}>{children}</FormProvider>;
  };

  it('renders the default settings correctly', async () => {
    render(
      <Wrapper>
        <ButtonSettings />
      </Wrapper>
    );

    expect(screen.getByText('appearance.buttonStyle')).toBeInTheDocument();
    expect(screen.getByText('appearance.button.fullWidth')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toBeInTheDocument();
  });

  it('toggles fullWidth switch', async () => {
    render(
      <Wrapper>
        <ButtonSettings />
      </Wrapper>
    );

    const switchEl = screen.getByRole('switch');
    expect(switchEl).toBeInTheDocument();
    expect((switchEl as HTMLInputElement).checked).toBe(false);

    fireEvent.click(switchEl);
    expect((switchEl as HTMLInputElement).checked).toBe(true);
  });

  it('disables fields when not customized', () => {
    render(
      <Wrapper>
        <ButtonSettings />
      </Wrapper>
    );

    const switchEl = screen.getByRole('switch');
    expect((switchEl as HTMLInputElement).disabled).toBe(true);
  });
});
