import { screen } from '@testing-library/react';
import HeaderSettings from '../HeaderSettings';
import { TestAppearanceWrapper } from '@/utils/test-util/appearanceForm';
import { render } from '@/utils/test-util/testUiComponent';

describe('HeaderSettings', () => {
  it('renders Back button, title, SegmentedControl, and image input', () => {
    render(
      <TestAppearanceWrapper>
        <HeaderSettings />
      </TestAppearanceWrapper>
    );

    expect(screen.getByText('appearance.customizeElement.header')).toBeInTheDocument();
  });
});
