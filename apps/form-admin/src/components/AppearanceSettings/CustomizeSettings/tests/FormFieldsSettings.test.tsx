import { screen } from '@testing-library/react';
import FormFieldsSettings from '../FormFieldsSettings';
import { render } from '@/utils/test-util/testUiComponent';
import { TestAppearanceWrapper } from '@/utils/test-util/appearanceForm';

describe('FormFieldsSettings Component', () => {
  it('should render CustomizeSettings component with defaultSettings', () => {
    render(
      <TestAppearanceWrapper>
        <FormFieldsSettings />
      </TestAppearanceWrapper>
    );

    expect(screen.getByText('appearance.customizeElement.formFields')).toBeInTheDocument();
    expect(screen.getByText('appearance.formFieldStyle')).toBeInTheDocument();
  });
});
