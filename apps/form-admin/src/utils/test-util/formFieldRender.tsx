import { FieldType, FormBuilderData, FormLayoutType, GroupFieldType, ValidatorType } from '@/types';
import {
  AppearanceButtonStyle,
  AppearanceHeaderPosition,
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
  InputStyle,
} from '@/types/enum';

export const mockFormBuilderData: FormBuilderData = {
  name: 'Example Form',
  description: 'This is an example form with all properties.',
  backgroundImageUrl: 'https://example.com/background.jpg',
  appearance: {
    headingStyle: {
      fontFamily: 'Arial',
      fontSize: 20,
      color: '#000000',
    },
    paragraphStyle: {
      fontFamily: 'Arial',
      fontSize: 14,
      color: '#333333',
    },
    buttonStyle: {
      type: AppearanceButtonStyle.Rounded,
      fullWidth: false,
      backgroundColor: '#007BFF',
      textColor: '#FFFFFF',
      fontFamily: 'Arial',
      fontSize: 14,
    },
    formFieldStyle: {
      color: {
        question: '#000000',
        answer: '#333333',
        icon: '#555555',
        description: '#777777',
        fieldStroke: '#CCCCCC',
        placeholder: '#AAAAAA',
        fieldBackground: '#FFFFFF',
      },
      fontFamily: {
        question: 'Arial',
        text: 'Arial',
        answer: 'Arial',
      },
      fontSize: {
        question: 16,
        text: 14,
        answer: 14,
      },
    },
    defaultSettings: {
      color: '#000000',
      fontFamily: 'Arial',
      inputStyle: InputStyle.Classic,
    },
    customize: true,
    headerStyle: {
      position: AppearanceHeaderPosition.Inside,
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Left,
      isUsingText: false,
      text: '',
    },
    footerStyle: {
      logoImage: '',
      logoSize: AppearanceSettingsLogoSize.Medium,
      logoAlign: AppearanceSettingsLogoAlign.Middle,
      isUsingText: false,
      text: '',
    },
    // Add other appearance-related properties as needed
  },
  content: [
    {
      id: 'section-1',
      type: FieldType.Section,
      name: 'Personal Information',
      layout: {
        type: FormLayoutType.ImageTop,
        imageUrl: 'https://example.com/image.jpg',
        fieldWidth: '100%',
      },
      content: [
        {
          id: 'field-1',
          type: FieldType.ShortQA,
          label: 'Your Name',
          name: 'name',
          placeholder: 'Enter your name',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Name is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-2',
          type: FieldType.Email,
          label: 'Your Email',
          name: 'email',
          placeholder: 'Enter your email',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Email is required.',
            },
            {
              type: ValidatorType.Email,
              message: 'Invalid email format.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-3',
          type: FieldType.PhoneNumber,
          label: 'Your Phone Number',
          name: 'phone',
          placeholder: 'Enter your phone number',
          required: false,
          validators: [],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-4',
          type: FieldType.Date,
          label: 'Your Birthdate',
          name: 'birthdate',
          placeholder: 'Select your birthdate',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Birthdate is required.',
            },
          ],
          style: {
            width: '50%',
          },
        },
        {
          id: 'field-5',
          type: FieldType.Dropdown,
          label: 'Your Gender',
          name: 'gender',
          options: [
            { value: 'male', label: 'Male' },
            { value: 'female', label: 'Female' },
            { value: 'other', label: 'Other' },
          ],
          placeholder: 'Select your gender',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Gender is required.',
            },
          ],
          style: {
            width: '50%',
          },
        },
        {
          id: 'field-6',
          type: FieldType.LongQA,
          label: 'About Yourself',
          name: 'about',
          placeholder: 'Tell us something about yourself',
          required: false,
          validators: [],
          style: {
            width: '100%',
          },
        },

        {
          id: 'field-7',
          type: FieldType.MultipleChoice,
          label: 'Your Favorite Hobbies',
          name: 'hobbies',
          options: [
            { value: 'reading', label: 'Reading' },
            { value: 'traveling', label: 'Traveling' },
            { value: 'sports', label: 'Sports' },
            { value: 'music', label: 'Music' },
          ],
          placeholder: 'Select your hobbies',
          required: false,
          validators: [],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-8',
          type: FieldType.FileUploader,
          label: 'Upload Your Resume',
          name: 'resume',
          placeholder: 'Choose a file',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Resume is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-9',
          type: FieldType.Rating,
          label: 'Rate Your Experience',
          name: 'experience_rating',
          maxScale: 10,
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Rating is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-10',
          type: FieldType.Checkbox,
          label: 'Agree to Terms and Conditions',
          name: 'terms',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'You must agree to the terms and conditions.',
            },
          ],
          style: {
            width: '100%',
          },
          options: [],
        },
        {
          id: 'field-14',
          type: FieldType.DateTime,
          label: 'Schedule Meeting',
          name: 'meeting_datetime',
          placeholder: 'Select date and time',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Meeting schedule is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-11',
          type: FieldType.Heading,
          label: 'Welcome to the Form',
          name: 'heading',
          style: {
            width: '100%',
          },
          validators: [],
        },
        {
          id: 'field-12',
          type: FieldType.Paragraph,
          label: 'Introduction Paragraph',
          name: 'intro_paragraph',
          style: {
            width: '100%',
            fontSize: '16px',
          },
          validators: [],
        },
        {
          id: 'field-13',
          type: FieldType.Dropdown,
          label: 'Select Your Skill Level',
          name: 'skill_level',
          options: [
            { value: 'beginner', label: 'Beginner' },
            { value: 'intermediate', label: 'Intermediate' },
            { value: 'expert', label: 'Expert' },
          ],
          placeholder: 'Select your skill level',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Skill level selection is required.',
            },
          ],
          style: {
            width: '50%',
          },
        },

        {
          id: 'field-15',
          type: FieldType.Checkboxes,
          label: 'Select Your Preferred Languages',
          name: 'preferred_languages',
          options: [
            { value: 'javascript', label: 'JavaScript' },
            { value: 'python', label: 'Python' },
            { value: 'java', label: 'Java' },
            { value: 'csharp', label: 'C#' },
          ],
          placeholder: 'Choose your preferred languages',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'You must select at least one language.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-16',
          type: FieldType.Legal,
          label: 'Accept Privacy Policy',
          name: 'privacy_policy',
          description: 'Please review and accept the privacy policy.',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'You must accept the privacy policy.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-17',
          type: FieldType.OpinionScale,
          label: 'Satisfaction Rating',
          name: 'satisfaction',
          maxScale: 5,
          placeholder: 'Rate your satisfaction',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Rating is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-18',
          type: FieldType.MultipleChoice,
          label: 'Preferred Work Environment',
          name: 'work_environment',
          options: [
            { value: 'remote', label: 'Remote' },
            { value: 'onsite', label: 'On-site' },
            { value: 'hybrid', label: 'Hybrid' },
          ],
          placeholder: 'Select your work environment preference',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Work environment preference is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },

        {
          id: 'field-19',
          type: FieldType.PictureChoice,
          label: 'Choose Your Favorite Image',
          name: 'favorite_image',
          options: [
            { value: 'nature', label: 'Nature', imageUrl: 'https://example.com/nature.jpg' },
            { value: 'city', label: 'City', imageUrl: 'https://example.com/city.jpg' },
            { value: 'space', label: 'Space', imageUrl: 'https://example.com/space.jpg' },
          ],
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'You must choose an image.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-20',
          type: FieldType.DateSelector,
          label: 'Select a Date',
          name: 'date_selector',
          placeholder: 'Pick a date',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Date selection is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-21',
          type: FieldType.YesNo,
          label: 'Do You Agree?',
          name: 'agree',
          required: true,
          validators: [
            {
              type: ValidatorType.Required,
              message: 'An answer is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },

        {
          id: 'field-22',
          type: GroupFieldType.Address,
          label: 'Enter Your Address',
          name: 'address',
          required: true,
          fields: [
            {
              id: 'field-address-line1',
              type: FieldType.ShortQA,
              label: 'Address Line 1',
              name: 'address_line1',
              placeholder: 'Enter your address',
              required: true,
              validators: [
                {
                  type: ValidatorType.Required,
                  message: 'Address Line 1 is required.',
                },
              ],
              style: {
                width: '100%',
              },
            },
            {
              id: 'field-country',
              type: FieldType.Dropdown,
              label: 'Country',
              name: 'country',
              options: [
                { value: 'us', label: 'United States' },
                { value: 'ca', label: 'Canada' },
                { value: 'gb', label: 'United Kingdom' },
              ],
              placeholder: 'Select your country',
              required: true,
              validators: [
                {
                  type: ValidatorType.Required,
                  message: 'Country is required.',
                },
              ],
              style: {
                width: '50%',
              },
            },
          ],
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Address is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
        {
          id: 'field-23',
          type: GroupFieldType.Name,
          label: 'Enter Your Name',
          name: 'user_name',
          required: true,
          fields: [
            {
              id: 'field-first-name',
              type: FieldType.ShortQA,
              label: 'First Name',
              name: 'first_name',
              placeholder: 'Enter your first name',
              required: true,
              validators: [
                {
                  type: ValidatorType.Required,
                  message: 'First name is required.',
                },
              ],
              style: {
                width: '100%',
              },
            },
            {
              id: 'field-last-name',
              type: FieldType.ShortQA,
              label: 'Last Name',
              name: 'last_name',
              placeholder: 'Enter your last name',
              required: true,
              validators: [
                {
                  type: ValidatorType.Required,
                  message: 'Last name is required.',
                },
              ],
              style: {
                width: '100%',
              },
            },
          ],
          validators: [
            {
              type: ValidatorType.Required,
              message: 'Name field is required.',
            },
          ],
          style: {
            width: '100%',
          },
        },
      ],
    },
  ],
  integrations: [],
  screenshot: {
    original: 'https://example.com/screenshot.jpg',
    thumbnail: 'https://example.com/thumbnail.jpg',
    preview: 'https://example.com/preview.jpg',
  },
};
