import { FormProvider, useForm } from 'react-hook-form';
import {
  AppearanceButtonStyle,
  AppearanceHeaderPosition,
  AppearanceSettingsLogoAlign,
  AppearanceSettingsLogoSize,
  InputStyle,
} from '@/types/enum';

export const TestAppearanceWrapper = ({ children }: { children: React.ReactNode }) => {
  const methods = useForm({
    defaultValues: {
      headingStyle: {
        fontFamily: 'Arial',
        fontSize: 20,
        color: '#000000',
      },
      paragraphStyle: {
        fontFamily: 'Arial',
        fontSize: 14,
        color: '#333333',
      },
      buttonStyle: {
        type: AppearanceButtonStyle.Rounded,
        fullWidth: false,
        backgroundColor: '#007BFF',
        textColor: '#FFFFFF',
        fontFamily: 'Arial',
        fontSize: 14,
      },
      formFieldStyle: {
        color: {
          question: '#000000',
          answer: '#333333',
          icon: '#555555',
          description: '#777777',
          fieldStroke: '#CCCCCC',
          placeholder: '#AAAAAA',
          fieldBackground: '#FFFFFF',
        },
        fontFamily: {
          question: 'Arial',
          text: 'Arial',
          answer: 'Arial',
        },
        fontSize: {
          question: 16,
          text: 14,
          answer: 14,
        },
      },
      defaultSettings: {
        color: '#000000',
        fontFamily: 'Arial',
        inputStyle: InputStyle.Classic,
      },
      customize: false,
      headerStyle: {
        position: AppearanceHeaderPosition.Inside,
        logoImage: '',
        logoSize: AppearanceSettingsLogoSize.Medium,
        logoAlign: AppearanceSettingsLogoAlign.Left,
        isUsingText: false,
        text: '',
      },
      footerStyle: {
        logoImage: '',
        logoSize: AppearanceSettingsLogoSize.Medium,
        logoAlign: AppearanceSettingsLogoAlign.Middle,
        isUsingText: false,
        text: '',
      },
    },
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
};
