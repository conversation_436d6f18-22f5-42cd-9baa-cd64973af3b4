import { render as testingLibraryRender } from '@testing-library/react';
import { createTheme, MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import { themeConfigurations } from '@/constants/themeConfiguration';
import { i18nInstance } from '@/i18n';
import { I18nextProvider } from 'react-i18next';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import builder from '@/store/reducers/builder';
import headerNavigation from '@/store/reducers/headerNavigation';
import { historyMiddleware } from '@/store/middleware/historyMiddleware';
import { ModalsProvider } from '@mantine/modals';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {SWRConfig} from "swr";

const theme = createTheme({
  ...themeConfigurations,
  components: {
    Select: {
      defaultProps: {
        allowDeselect: false,
        withCheckIcon: false,
      },
    },
  },
});

export const render = (ui: React.ReactNode, optionalRootStore?: any) => {
  const store = configureStore({
    reducer: {
      builder,
      headerNavigation,
    },
    preloadedState: optionalRootStore,
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(historyMiddleware),
  } as any);
  return testingLibraryRender(<>{ui}</>, {
    wrapper: ({ children }: { children: React.ReactNode }) => (
      <MantineProvider stylesTransform={emotionTransform} theme={theme}>
        <MantineEmotionProvider>
          <ModalsProvider>
            <SWRConfig>
              <BrowserRouter window={window}>
                <I18nextProvider i18n={i18nInstance}>
                  <DndProvider backend={HTML5Backend}>
                    <Provider store={store}>{children}</Provider>
                  </DndProvider>
                </I18nextProvider>
              </BrowserRouter>
            </SWRConfig>
          </ModalsProvider>
        </MantineEmotionProvider>
      </MantineProvider>
    ),
  });
};
