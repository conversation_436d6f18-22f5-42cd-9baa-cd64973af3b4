import {describe, it} from 'vitest';
import BuilderMain from '../BuilderMain';
import {FormBuilderSteps} from '@/types';
import '@testing-library/jest-dom';
import {render} from "@/utils/test-util/testUiComponent";
import {mockFormBuilderData} from "@/utils/test-util/formFieldRender";
// Mock dependencies

describe('BuilderMain', () => {
  const renderBuilderMain = (step: FormBuilderSteps) => {
    // Mock the URLSearchParams used in `useSearchParams`
    render(<BuilderMain step={step} formData={mockFormBuilderData}/>);
  };

  it('renders the correct container for FormBuilderSteps.Create', () => {
    renderBuilderMain(FormBuilderSteps.Create);


  });

  // it('renders the correct container for FormBuilderSteps.Share', () => {
  //   renderBuilderMain(FormBuilderSteps.Share);
  //
  // });

  it('renders the correct container for FormBuilderSteps.Integrations', () => {
    renderBuilderMain(FormBuilderSteps.Integrations);

  });

  it('renders the correct container for FormBuilderSteps.Settings', () => {
    renderBuilderMain(FormBuilderSteps.Settings);

  });

  it('renders the correct container for FormBuilderSteps.Results', () => {
    renderBuilderMain(FormBuilderSteps.Results);

  });

});
