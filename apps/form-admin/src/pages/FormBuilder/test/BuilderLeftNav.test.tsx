import { describe, it, vi, beforeEach } from 'vitest';
import BuilderLeftNav from '../BuilderLeftNav/BuilderLeftNav';
import {
  BuilderRightMenuType,
  FormBuilderSteps,
  Integration,
  IntegrationSectionTypes,
  ResultTab,
  ShareAction,
} from '@/types';
import { render } from '@/utils/test-util/testUiComponent';
import { mockFormBuilderData } from '@/utils/test-util/formFieldRender';
import { AppearanceSettingsOptionType } from '@/types/enum';

vi.mock('@mantine/notifications', () => ({
  notifications: {
    show: vi.fn(),
  },
}));

describe('BuilderLeftNav Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders Create navigation correctly', () => {
    render(<BuilderLeftNav step={FormBuilderSteps.Create} />, {
      builder: {
        builderJson: mockFormBuilderData,
        selectingSectionId: mockFormBuilderData.content?.[0]!.id,
        selectingShareSection: ShareAction.Share,
        selectingResultSection: ResultTab.Results,
        rightMenuType: BuilderRightMenuType.Question,
        selectedAppearanceSettings: AppearanceSettingsOptionType.Default,
        formSaving: null,
        selectedIntegrationSection: IntegrationSectionTypes.Installed,
        selectedIntegrationItem: {} as Integration,
        resultHiddenFieldIds: [] as string[],
        resultPinnedColumnId: null,
        screenshot: null,
        currentViewId: 'default',
        isDragging: false,
        currentHistoryKey: '',
        selectingFieldId: '',
      },
    });
  });

  it('renders Share navigation correctly', () => {
    render(<BuilderLeftNav step={FormBuilderSteps.Share} />);
  });

  it('renders Results navigation correctly', () => {
    render(<BuilderLeftNav step={FormBuilderSteps.Results} />);
  });

  it('renders Integration navigation correctly', () => {
    render(<BuilderLeftNav step={FormBuilderSteps.Integrations} />);
  });
});
