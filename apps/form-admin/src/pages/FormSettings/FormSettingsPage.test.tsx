import FormSettingsPage from './index';
import { Mock, vi } from 'vitest';
import { FormsAPI } from '@/services/api';
import { render } from '@/utils/test-util/testUiComponent';
import { fireEvent } from '@testing-library/react';
import { mockFormBuilderData } from '@/utils/test-util/formFieldRender';
import {
  BuilderRightMenuType,
  Integration,
  IntegrationSectionTypes,
  ResultTab,
  ShareAction,
} from '@/types';
import { AppearanceSettingsOptionType } from '@/types/enum';

vi.mock('@/services/api', () => ({
  FormsAPI: {
    getForm: vi.fn(),
    updateForm: vi.fn(),
  },
}));

vi.mock('../utils/notification', () => ({
  showNotificationToast: vi.fn(),
}));

describe('FormSettingsPage', () => {
  const mockFormData = {
    name: 'Test Form',
    tags: ['tag1', 'tag2'],
    startAt: '2023-01-01T00:00:00Z',
    expiredAt: '2023-12-31T00:00:00Z',
    setting: {
      submission: {
        mode: 'test-mode',
        message: 'test-message',
        caption: 'test-caption',
        button: 'test-button',
        redirectUrl: 'https://example.com',
        enableBranding: true,
        limitResponse: true,
        limitNumber: 10,
        thankMessage: { emailQuestionId: '12345' },
      },
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the loading overlay when data is loading', async () => {
    (FormsAPI.getForm as Mock).mockResolvedValue(null);

    render(<FormSettingsPage />);
  });

  it('renders form data after loading', async () => {
    (FormsAPI.getForm as Mock).mockResolvedValue(mockFormData);

    render(<FormSettingsPage />);
  });

  it('handles tab change correctly', async () => {
    (FormsAPI.getForm as Mock).mockResolvedValue(mockFormData);

    const { getByText } = render(<FormSettingsPage />, {
      builder: {
        builderJson: mockFormBuilderData,
        selectingSectionId: mockFormBuilderData.content?.[0]!.id,
        selectingShareSection: ShareAction.Share,
        selectingResultSection: ResultTab.Results,
        rightMenuType: BuilderRightMenuType.Question,
        selectedAppearanceSettings: AppearanceSettingsOptionType.Default,
        formSaving: null,
        selectedIntegrationSection: IntegrationSectionTypes.Installed,
        selectedIntegrationItem: {} as Integration,
        resultHiddenFieldIds: [] as string[],
        resultPinnedColumnId: null,
        screenshot: null,
        currentViewId: 'default',
        isDragging: false,
        currentHistoryKey: '',
        selectingFieldId: '',
        currentFormId: 'test-id',
      },
    });

    // Simulate the API returning data

    // Assert that the default tab is rendered
    expect(getByText('postSubmissionLabel')).toBeInTheDocument();

    fireEvent.click(getByText('postSubmissionLabel'));
  });
});
