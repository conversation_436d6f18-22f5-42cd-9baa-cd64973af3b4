{"extends": ["@resola-ai/biome-config/biome"], "css": {"parser": {"cssModules": true}}, "formatter": {"enabled": true, "formatWithErrors": true}, "files": {"include": ["src/**/*.js", "!*.test.js"]}, "linter": {"enabled": true, "rules": {"recommended": true, "a11y": {"noSvgWithoutTitle": "off", "noRedundantAlt": "off", "useKeyWithClickEvents": "off", "useIframeTitle": "off", "noLabelWithoutControl": "off", "useAltText": "off"}, "correctness": {"useExhaustiveDependencies": "off", "noUnusedImports": "error", "useJsxKeyInIterable": "off"}, "suspicious": {"noArrayIndexKey": "off", "noExplicitAny": "off", "noEmptyInterface": "warn", "noAssignInExpressions": "off", "noEmptyBlockStatements": "off", "noImplicitAnyLet": "off", "noConsoleLog": "warn", "noDuplicateAtImportRules": "warn"}, "style": {"noNonNullAssertion": "off", "useNodejsImportProtocol": "off", "noParameterAssign": "off"}, "complexity": {"noForEach": "off", "noUselessSwitchCase": "warn", "useOptionalChain": "off", "noUselessFragments": "off"}, "security": {"noDangerouslySetInnerHtml": "off"}, "performance": {"noAccumulatingSpread": "off"}}}}