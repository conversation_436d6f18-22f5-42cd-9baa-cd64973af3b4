#!/bin/bash

# Read environment variables from .env file
if [ -f .env ]; then
    source .env
else
    echo "Error: .env file not found"
    exit 0
fi

# Move the index.html file to the root of the out directory
if [ -d "./out" ];
then
  echo "Postbuild for static mode"
  mv out/index/index.html out/index.html
  # Remove the index directory
  rm -rf out/index
  # Build the embed.v1.js file
  terser out/embed.js -o out/embed.v1.js --compress drop_console=true,drop_debugger=true --mangle --toplevel

  if [[ "$NEXT_PUBLIC_NODE_ENV" == "production" ]]; then
      sed -i 's|embed.js|embed.v1.js|g' out/embed-example.html
  fi

  # Remove the embed-example.html file on the production environment
  if [[ "$NEXT_PUBLIC_API_SERVER_URL" == *"deca.cloud"* ]]; then
      rm -f out/embed-example.html
  fi
fi


exit 0
