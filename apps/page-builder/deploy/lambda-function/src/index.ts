import { SQSEvent } from "aws-lambda";
import { z } from "zod";
import { BuildSiteRequestSchema } from "./schemas";
import { SiteBuilder, SiteBuilderService } from "./services";
import { logger, tracer, metrics } from "./powertools";
import { captureLambdaHandler } from "@aws-lambda-powertools/tracer/middleware";
import { injectLambdaContext } from "@aws-lambda-powertools/logger/middleware";
import { logMetrics } from "@aws-lambda-powertools/metrics/middleware";
import { LambdaInterface } from "@aws-lambda-powertools/commons/types";

// Lambda handler class implementing LambdaInterface
class LambdaHandler implements LambdaInterface {
    private siteBuilderService: SiteBuilderService;

    constructor() {
        this.siteBuilderService = new SiteBuilderService();
    }

    // Main handler method required by LambdaInterface
    async handler(event: SQSEvent, _context: unknown): Promise<void> {
        const record = event.Records[0];
        try {
            logger.info("Processing SQS event", {
                messageId: record.messageId,
                eventSource: record.eventSource,
            });

            const request = BuildSiteRequestSchema.parse(JSON.parse(record.body));
            logger.appendKeys({
                siteId: request.site_id,
                versionId: request.version_id,
                taskId: request.task_id,
            });
            const lambdaEnvs = this.siteBuilderService.getLambdaEnvVars();
            const accessToken = await this.siteBuilderService.decryptToken(request.eat);
            const envVars = {
                BUILD_SITE_ID: request.site_id,
                BUILD_VERSION_ID: request.version_id,
                BUILD_ACCESS_TOKEN: accessToken,
                NEXT_PUBLIC_ORG_ID: request.oid,
                NEXT_PUBLIC_SITE_ID: request.site_id,
                NEXT_PUBLIC_NODE_ENV: lambdaEnvs.NODE_ENV,
                NEXT_PUBLIC_API_SERVER_URL: lambdaEnvs.API_SERVER_URL,
            };
            const builder = new SiteBuilder(envVars);
            const buildResult = await builder.build();

            if (buildResult.success) {
                await this.siteBuilderService.handleSuccess(request, buildResult);
            } else {
                await this.siteBuilderService.handleFailure(request, buildResult);
            }
        } catch (error) {
            logger.error("Error processing SQS event", {
                error,
                messageId: record.messageId,
            });
            if (error instanceof z.ZodError) {
                logger.warn("Invalid request format", {
                    validationErrors: error.errors,
                });

                const request = BuildSiteRequestSchema.parse(JSON.parse(record.body));
                await this.siteBuilderService.handleFailure(request, {
                    success: false,
                    stderr: error.message,
                });
            }
            throw error;
        }
    }
}

// Initialize handler
const lambdaHandler = new LambdaHandler();

export const handleEvent = async (event: SQSEvent, context: any) => {
    // Dynamically import middy
    const { default: middy } = await import("@middy/core");
    // Create middleware stack
    const handler = middy(lambdaHandler.handler.bind(lambdaHandler))
        .use(captureLambdaHandler(tracer))
        .use(injectLambdaContext(logger))
        .use(logMetrics(metrics));

    // Execute handler
    return handler(event, context);
};
