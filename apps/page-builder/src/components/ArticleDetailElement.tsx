import { generateShortenUrl, getArticleDetail } from '@/api/site';
import { ArticleDetailElement as ArticleDetailElementUI } from '@resola-ai/ui/components/PageBuilder';

import { useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';

const ArticleDetailElement = (props: Record<string, any>) => {
  const [article, setArticle] = useState<any>();
  const [copied, setCopied] = useState<boolean>(false);

  const searchParams = useSearchParams();
  const baseId = searchParams.get('faq_base_id');
  const articleId = searchParams.get('faq_article_id');

  useEffect(() => {
    if (baseId && articleId) {
      const getDetail = async (baseId: string, articleId: string) => {
        await getArticleDetail(baseId, articleId).then(res => {
          if (res) {
            setArticle(res);
          }
        });
      };
      getDetail(baseId, articleId);
    }
  }, [baseId, articleId]);

  const fetchShortenUrl = async () => {
    const currentLink =
      window.top === window ? window.location.href : window['BASE_PARENT_URL'] || '';
    const shorten = await generateShortenUrl({
      url: currentLink,
      resource_id: (process.env.NEXT_PUBLIC_SITE_ID || process.env.BUILD_SITE_ID) as string,
      resource_type: 'site',
    });

    if (shorten?.id) {
      setCopied(true);
      setTimeout(() => {
        setCopied(false);
      }, 5000);
      if (window.top === window) {
        return `${window.location.protocol + '//' + window.location.host}/url?id=${shorten.id}`;
      } else {
        const link = new URL(currentLink);
        link.searchParams.delete('embed_state');
        link.searchParams.set('embed_id', shorten?.id);
        return link.toString();
      }
    }
    return '';
  };

  return (
    <ArticleDetailElementUI
      {...props}
      onShortenUrl={fetchShortenUrl}
      isCopied={copied}
      article={article}
    />
  );
};

export default ArticleDetailElement;
