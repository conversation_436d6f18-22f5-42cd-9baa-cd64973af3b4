import { getCategories } from '@/api/site';
import { ArticleType } from '@/types/enum';
import { CategoryTreeElement as CategoryTreeElementUI } from '@resola-ai/ui/components/PageBuilder';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const CategoryTreeElement = (props: Record<string, any>) => {
  const router = useRouter();
  const {
    siteId,
    padding,
    width,
    articleDetailSlug,
    categoryListSlug,
    styles,
    showRightDivider,
    dividerColor,
  } = props;
  const {
    element_id,
    faq_article_id = '',
    faq_category_id = '',
    faq_sub_category_id = '',
  } = router.query;

  const [categories, setCategories] = useState<any>([]);
  const [selectedCategories, setSelectedCategories] = useState();

  useEffect(() => {
    if (siteId) {
      const getSiteCategories = async (siteId: string) => {
        await getCategories(siteId).then(res => {
          if (res) {
            setCategories(res);
          }
        });
      };
      getSiteCategories(siteId);
    }
  }, [siteId]);

  useEffect(() => {
    const firstElement = categories?.[0];
    let elementMatchedArticleId = null;
    let elementMatchedElementId = null;
    if (faq_article_id) {
      categories.forEach(elementCategories => {
        if (!elementMatchedArticleId) {
          elementCategories?.categories?.forEach(category => {
            if (!elementMatchedArticleId) {
              if (category.subType === ArticleType.Category) {
                categories.data.forEach(subCategory => {
                  if (
                    subCategory.data.find(article => {
                      article.value === faq_article_id;
                    })
                  ) {
                    elementMatchedArticleId = elementCategories;
                  }
                });
              } else if (category.subType === ArticleType.Article) {
                if (category.data.find(article => article.value === faq_article_id)) {
                  elementMatchedArticleId = elementCategories;
                }
              }
            }
          });
        }
      });
    } else if (element_id) {
      elementMatchedElementId = categories.find(category => category.elementId === element_id);
    }

    setSelectedCategories(
      faq_article_id
        ? (elementMatchedArticleId ?? firstElement)
        : (elementMatchedElementId ?? firstElement)
    );
  }, [categories]);

  if (isEmpty(selectedCategories)) return null;

  return (
    <CategoryTreeElementUI
      padding={padding}
      width={width}
      categories={(selectedCategories as any)?.categories}
      selectedElement={element_id as string}
      selectedArticle={faq_article_id as string}
      selectedCategory={faq_category_id as string}
      selectedSubCategory={faq_sub_category_id as string}
      articleDetailSlug={articleDetailSlug}
      categoryListSlug={categoryListSlug}
      styles={styles}
      showRightDivider={showRightDivider}
      dividerColor={dividerColor}
    />
  );
};

export default CategoryTreeElement;
