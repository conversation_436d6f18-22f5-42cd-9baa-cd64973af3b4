import { getCategories } from '@/api/site';
import { CategoryListElement as CategoryListElementUI } from '@resola-ai/ui/components/PageBuilder';
import { ArticleType, Category, CategoryArticle } from '@resola-ai/ui/types/pageBuilder';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const CategoryListElement = (props: Record<string, any>) => {
  const router = useRouter();
  const {
    siteId,
    type,
    padding,
    backgroundColor,
    showDescription,
    showPicture,
    textColor,
    iconColor,
    containerBorderColor,
    articleDetailSlug,
  } = props;
  const { element_id, faq_category_id, faq_sub_category_id } = router.query;

  const [categories, setCategories] = useState<any>([]);
  const [selectedCategories, setSelectedCategories] = useState();
  const [selectedCategory, setSelectedCategory] = useState();
  const [selectedSubCategory, setSelectedSubCategory] = useState();

  useEffect(() => {
    if (siteId) {
      const getSiteCategories = async (siteId: string) => {
        await getCategories(siteId).then(res => {
          if (res) {
            setCategories(res);
          }
        });
      };
      getSiteCategories(siteId);
    }
  }, [siteId]);

  useEffect(() => {
    const currentCategories =
      categories.find(category => category.elementId === element_id) ?? categories?.[0] ?? null;
    const currentSelectedCategory = (selectedCategories as any)?.categories.find(
      category => category.id === faq_category_id
    );
    const currentSelectedSubCategory = (selectedCategory as any)?.data.find(
      category => category.id === faq_sub_category_id
    );
    setSelectedCategories(currentCategories);
    setSelectedCategory(currentSelectedCategory);
    setSelectedSubCategory(currentSelectedSubCategory);
  }, [categories]);

  const handleClick = (item: Category | CategoryArticle) => {
    if (item.type === ArticleType.Article && !item.subType) {
      window.location.href = `${articleDetailSlug}?faq_article_id=${(item as CategoryArticle).value}&faq_base_id=${(item as CategoryArticle).parentId}&element_id=${element_id}`;
    } else {
      window.location.href = `${router.query.slug}?element_id=${element_id}&faq_category_id=${faq_category_id}&faq_sub_category_id=${item.id}`;
    }
  };

  return (
    <CategoryListElementUI
      selectedCategory={faq_sub_category_id ? selectedSubCategory : (selectedCategory as any)}
      type={type}
      padding={padding}
      backgroundColor={backgroundColor}
      showDescription={showDescription}
      showPicture={showPicture}
      textColor={textColor}
      iconColor={iconColor}
      containerBorderColor={containerBorderColor}
      onClickArticle={handleClick}
    />
  );
};

export default CategoryListElement;
