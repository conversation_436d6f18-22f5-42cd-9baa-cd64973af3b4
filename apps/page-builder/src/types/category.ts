import { ArticleType } from './enum';

export interface Article {
  label: string;
  value: string;
  path: string[];
}

export interface CategoryArticle {
  label?: string;
  name: string;
  type: ArticleType.Article;
  description: string;
  id: string;
  value: string;
  parent_dir: string;
  parent_dir_path: string;
  parent_dir_breadcrumb_array: string[];
}

export interface Category {
  name: string;
  description: string;
  type: Omit<ArticleType, 'none'>;
  subType: ArticleType;
  id?: string;
  data: Category[] | CategoryArticle[];
  image?: string;
}

export interface CategoryFormValues {
  categories: Category[];
}
