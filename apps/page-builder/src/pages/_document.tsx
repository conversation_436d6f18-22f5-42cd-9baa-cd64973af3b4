import NextDocument, { Head, Html, Main, NextScript, DocumentContext } from 'next/document';
import createEmotionServer from '@emotion/server/create-instance';
import { ColorSchemeScript } from '@mantine/core';
import { createGetInitialProps } from '@mantine/emotion';
import { emotionCache } from '@/cache';
import { getSiteSetting } from '@/api/site';
import { getIntegrations } from '@/api/intergration';
import { getPage } from '@/api/page';

type CustomProps = {
  headerCode?: string;
  footerCode?: string;
  embedScriptUrl?: string;
};

export default function Document(props: CustomProps) {
  const { headerCode = '', footerCode = '', embedScriptUrl = '' } = props;

  return (
    <Html lang='en' suppressHydrationWarning>
      <Head>
        <ColorSchemeScript defaultColorScheme='auto' />
        <link
          href='https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap'
          rel='stylesheet'
        />
        <div dangerouslySetInnerHTML={{ __html: headerCode }} />
        {embedScriptUrl && <script src={embedScriptUrl} async defer />}
      </Head>
      <body>
        <Main />
        <NextScript />
        <div dangerouslySetInnerHTML={{ __html: footerCode }} />
      </body>
    </Html>
  );
}

const stylesServer = createEmotionServer(emotionCache);

Document.getInitialProps = async (ctx: DocumentContext) => {
  const initialProps = await createGetInitialProps(NextDocument, stylesServer)(ctx);
  const siteId = process.env.BUILD_SITE_ID as string;

  const _getIntegration = async () => {
    try {
      const integrations = await getIntegrations({ site_id: siteId, type: 'deca_chatwindow' });
      if (integrations?.response?.[0]) {
        const { script_url, is_enabled, options } = integrations?.response?.[0];
        // if not enabled, return empty string
        if (!is_enabled || !siteId || !ctx?.query?.slug) return '';

        const page = await getPage(siteId, ctx?.query?.slug as string);
        const pageScope = options?.page_scope || 'all_pages';
        if (pageScope === 'all_pages') {
          return script_url;
        } else {
          const pageIds = options?.page_ids;
          if (pageIds?.includes(page?.logical_id)) {
            return script_url;
          }
        }
      }

      return '';
    } catch (error) {
      return '';
    }
  };

  try {
    const [siteSetting, embedScriptUrl] = await Promise.all([
      getSiteSetting(siteId),
      _getIntegration(),
    ]);
    return {
      ...initialProps,
      headerCode: siteSetting?.html?.header || '',
      footerCode: siteSetting?.html?.footer || '',
      embedScriptUrl: embedScriptUrl || '',
    };
  } catch (error) {
    return {
      ...initialProps,
      headerCode: '',
      footerCode: '',
      embedScriptUrl: '',
    };
  }
};
