import { getDataByShortenId } from '@/api/site';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

const UrlPage = () => {
  const router = useRouter();
  const { id } = router.query;
  const [url, setUrl] = useState<string>();

  useEffect(() => {
    if (id) {
      const getShortenLink = async (id: string) => {
        const result = await getDataByShortenId(String(id));
        setUrl(result?.url || '');
      };
      getShortenLink(String(id));
    }
  }, [id]);

  useEffect(() => {
    if (typeof window !== 'undefined' && url) {
      if (window.top === window) {
        window.location.href = url;
      } else {
        window.top?.postMessage({ type: 'deca-redirect-to', redirectTo: url }, '*');
      }
    }
  }, [url]);

  return null;
};

export default UrlPage;
