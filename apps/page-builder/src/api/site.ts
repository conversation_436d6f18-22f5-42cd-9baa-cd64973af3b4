import { snakeToCamel } from '@/utils/data-transform';
import { axiosService } from '@resola-ai/services-shared';

export const getCategories = async (siteId: string) => {
  const response = await axiosService.instance.get(
    `/sites/${siteId}/data?data_type=faq_categories`
  );
  const categories = response.data.response || [];
  return categories.map(item => snakeToCamel(item));
};

export const getSiteData = async (siteId: string, dataType: string) => {
  const siteData = await axiosService.instance.get(`/sites/${siteId}/data?data_type=${dataType}`);
  return siteData.data.response;
};

export const getSite = async (siteId: string) => {
  const siteData = await axiosService.instance.get(`/sites/${siteId}`);
  return siteData.data.response;
};

export const getSiteSetting = async (siteId: string) => {
  const siteData = await axiosService.instance.get(`/sites/${siteId}/setting`);
  return siteData.data.response;
};

export const searchArticles = async (data: any) => {
  try {
    const articleData = await axiosService.instance.post('/integrations/kb/search', data);
    return articleData?.data?.response?.data;
  } catch (error) {
    console.error('Error fetching articles:', error);
    return [];
  }
};

export const getArticleDetail = async (base_id: string, article_id: string) => {
  const articleData = await axiosService.instance.get(
    `/integrations/kb/bases/${base_id}/articles/${article_id}`
  );
  return articleData.data.response;
};

export const generateShortenUrl = async (data: any) => {
  try {
    const shortenData = await axiosService.instance.post('/general/shorten', data);
    return shortenData?.data?.response;
  } catch (error) {
    console.error('Error fetching shorten data:', error);
    return [];
  }
};

export const getDataByShortenId = async (shortenId: string) => {
  const shortenData = await axiosService.instance.get(`/general/shorten/${shortenId}`);
  return shortenData.data.response;
};
