import { useRouter } from 'next/router';
import { useEffect, useRef } from 'react';

/**
 * Simple URL binding hook for embedded Next.js apps
 * Automatically syncs iframe URL changes with parent page URL
 */
export function useUrlBinding(enabled: boolean = true) {
  const router = useRouter();
  const lastUrlRef = useRef<string>('');
  const isEmbeddedRef = useRef<boolean>(false);

  useEffect(() => {
    // Check if we're in an iframe
    isEmbeddedRef.current = window.self !== window.top;

    if (!enabled || !isEmbeddedRef.current) return;

    // Function to notify parent of URL changes
    const notifyParent = (url: string) => {
      try {
        window.parent.postMessage(
          {
            type: 'deca-iframe-url-change',
            url: url,
          },
          '*'
        );
      } catch (e) {
        console.error('Error notifying parent of URL change:', e);
        // Ignore cross-origin errors
      }
    };

    // Function to check and notify URL changes
    const checkUrlChange = () => {
      const currentUrl = window.location.href;
      if (currentUrl !== lastUrlRef.current) {
        lastUrlRef.current = currentUrl;
        notifyParent(currentUrl);
      }
    };

    // Set initial URL
    lastUrlRef.current = window.location.href;

    // Notify parent immediately
    notifyParent(window.location.href);

    // Listen to Next.js router events
    const handleRouteChange = () => {
      // Small delay to ensure URL is fully updated
      setTimeout(checkUrlChange, 50);
    };

    router.events.on('routeChangeComplete', handleRouteChange);
    router.events.on('hashChangeComplete', handleRouteChange);

    // Cleanup
    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
      router.events.off('hashChangeComplete', handleRouteChange);
    };
  }, [enabled, router.events]);

  useEffect(() => {
    const handleMessage = event => {
      if (!event.data || event.data.type !== 'deca-parent-url') return;
      window['BASE_PARENT_URL'] = event.data.url;
    };
    window.addEventListener('message', handleMessage);

    return () => {
      window.removeEventListener('message', handleMessage);
      window['BASE_PARENT_URL'] = '';
    };
  }, []);

  return {
    isEmbedded: isEmbeddedRef.current,
  };
}
