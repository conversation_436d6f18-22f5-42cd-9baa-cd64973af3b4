import React from 'react';
import { render, screen } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import createCache from '@emotion/cache';

// Mock the text editor context
const mockT = jest.fn((key: string, options?: any) => {
  if (key === 'asset.upload.error.filesizeexceeded') {
    return `File size must be less than ${options?.LIMIT_SIZE}MB`;
  }
  if (key === 'asset.upload.error.filetypenotallowed') {
    return 'File type not allowed';
  }
  if (key === 'asset.upload.error.filecountexceeded') {
    return `Maximum ${options?.LIMIT_FILE_PER_UPLOAD} file(s) allowed`;
  }
  return key;
});

jest.mock('../../../context', () => ({
  useTextEditorContext: () => ({
    t: mockT,
  }),
}));

// Mock the upload asset context
const mockUseUploadAssetContext = jest.fn();
jest.mock('../../../../uploadAssetContext', () => ({
  useUploadAssetContext: () => mockUseUploadAssetContext(),
}));

// Mock the debounced state hook
jest.mock('@mantine/hooks', () => ({
  useDebouncedState: jest.fn(() => [[], jest.fn()]),
}));

// Mock the ErrorDetail component
jest.mock('../ErrorDetail', () => {
  return function MockErrorDetail({ code, message }: { code: string; message: string }) {
    return (
      <div data-testid={`error-detail-${code}`}>
        <span data-testid={`error-code-${code}`}>{code}</span>
        <span data-testid={`error-message-${code}`}>{message}</span>
      </div>
    );
  };
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const cache = createCache({ key: 'test' });
  return (
    <MantineEmotionProvider cache={cache}>
      <MantineProvider>{children}</MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('DisplayError', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseUploadAssetContext.mockReturnValue({
      rejectErrors: {},
      LIMIT_SIZE: 5 * 1024 * 1024, // 5MB in bytes
      LIMIT_FILE_PER_UPLOAD: 1,
    });
  });

  describe('Utility function', () => {
    it('should convert bytes to megabytes correctly', () => {
      // Test the convertBytesToMegaBytes function logic
      const convertBytesToMegaBytes = (bytes: number) => {
        return bytes / (1024 * 1024);
      };

      expect(convertBytesToMegaBytes(1024 * 1024)).toBe(1); // 1MB
      expect(convertBytesToMegaBytes(5 * 1024 * 1024)).toBe(5); // 5MB
      expect(convertBytesToMegaBytes(10 * 1024 * 1024)).toBe(10); // 10MB
    });
  });

  describe('Error code processing', () => {
    it('should remove hyphens from error codes for translation', () => {
      const errorCode = 'file-size-exceeded';
      const processedCode = errorCode.split('-').join('');
      expect(processedCode).toBe('filesizeexceeded');
    });

    it('should handle multiple hyphens in error codes', () => {
      const errorCode = 'file-type-not-allowed';
      const processedCode = errorCode.split('-').join('');
      expect(processedCode).toBe('filetypenotallowed');
    });
  });

  describe('Translation function calls', () => {
    it('should call translation function with correct parameters', () => {
      // Test the translation logic without rendering
      const errorCode = 'file-size-exceeded';
      const processedCode = errorCode.split('-').join('');
      const LIMIT_SIZE = 5 * 1024 * 1024;
      const LIMIT_FILE_PER_UPLOAD = 1;

      const translationKey = 'asset.upload.error.' + processedCode;
      const translationOptions = {
        LIMIT_SIZE: LIMIT_SIZE / (1024 * 1024),
        LIMIT_FILE_PER_UPLOAD: LIMIT_FILE_PER_UPLOAD,
      };

      mockT(translationKey, translationOptions);

      expect(mockT).toHaveBeenCalledWith('asset.upload.error.filesizeexceeded', {
        LIMIT_SIZE: 5,
        LIMIT_FILE_PER_UPLOAD: 1,
      });
    });
  });

  describe('Error data processing', () => {
    it('should filter out false values from rejectErrors', () => {
      const rejectErrors = {
        'file-size-exceeded': true,
        'file-type-not-allowed': false,
        'file-count-exceeded': true,
      };

      const array = Object.entries(rejectErrors)
        .map(([key, value]: [string, boolean]) => {
          if (value) {
            return key;
          }
          return null;
        })
        .filter((error: string | null) => error !== null)
        .flat(1);

      expect(array).toEqual(['file-size-exceeded', 'file-count-exceeded']);
    });

    it('should remove duplicates from error array', () => {
      const array = ['file-size-exceeded', 'file-size-exceeded', 'file-type-not-allowed'];
      const uniqueSet = new Set(array);
      const data = Array.from(uniqueSet);

      expect(data).toEqual(['file-size-exceeded', 'file-type-not-allowed']);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty rejectErrors object', () => {
      const rejectErrors = {};
      const array = Object.entries(rejectErrors)
        .map(([key, value]: [string, boolean]) => {
          if (value) {
            return key;
          }
          return null;
        })
        .filter((error: string | null) => error !== null)
        .flat(1);

      expect(array).toEqual([]);
    });

    it('should handle all false values in rejectErrors', () => {
      const rejectErrors = {
        'file-size-exceeded': false,
        'file-type-not-allowed': false,
      };

      const array = Object.entries(rejectErrors)
        .map(([key, value]: [string, boolean]) => {
          if (value) {
            return key;
          }
          return null;
        })
        .filter((error: string | null) => error !== null)
        .flat(1);

      expect(array).toEqual([]);
    });
  });
});
