// Import the utility function from the DisplayError component
// Since it's not exported, we'll test it by recreating the logic

describe('convertBytesToMegaBytes', () => {
  const convertBytesToMegaBytes = (bytes: number) => {
    return bytes / (1024 * 1024);
  };

  describe('Conversion accuracy', () => {
    it('should convert 1MB correctly', () => {
      const bytes = 1024 * 1024; // 1MB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(1);
    });

    it('should convert 5MB correctly', () => {
      const bytes = 5 * 1024 * 1024; // 5MB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(5);
    });

    it('should convert 10MB correctly', () => {
      const bytes = 10 * 1024 * 1024; // 10MB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(10);
    });

    it('should convert 100MB correctly', () => {
      const bytes = 100 * 1024 * 1024; // 100MB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(100);
    });
  });

  describe('Edge cases', () => {
    it('should handle zero bytes', () => {
      const result = convertBytesToMegaBytes(0);
      expect(result).toBe(0);
    });

    it('should handle negative bytes', () => {
      const result = convertBytesToMegaBytes(-1024 * 1024);
      expect(result).toBe(-1);
    });

    it('should handle very large byte values', () => {
      const bytes = 1024 * 1024 * 1024; // 1GB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(1024);
    });

    it('should handle decimal results', () => {
      const bytes = 1024 * 1024 + 512 * 1024; // 1.5MB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(1.5);
    });

    it('should handle very small byte values', () => {
      const bytes = 1024; // 1KB in bytes
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBe(1 / 1024); // 0.0009765625
    });
  });

  describe('Precision', () => {
    it('should maintain precision for fractional megabytes', () => {
      const bytes = 1024 * 1024 + 1024; // 1MB + 1KB
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBeCloseTo(1.0009765625, 10);
    });

    it('should handle very precise calculations', () => {
      const bytes = 1024 * 1024 + 1; // 1MB + 1 byte
      const result = convertBytesToMegaBytes(bytes);
      expect(result).toBeCloseTo(1.0000009537, 10);
    });
  });

  describe('Common file sizes', () => {
    it('should convert common image file sizes', () => {
      const testCases = [
        { bytes: 500 * 1024, expected: 500 / 1024 }, // 500KB
        { bytes: 2 * 1024 * 1024, expected: 2 }, // 2MB
        { bytes: 5 * 1024 * 1024, expected: 5 }, // 5MB
        { bytes: 10 * 1024 * 1024, expected: 10 }, // 10MB
      ];

      testCases.forEach(({ bytes, expected }) => {
        const result = convertBytesToMegaBytes(bytes);
        expect(result).toBe(expected);
      });
    });

    it('should convert common video file sizes', () => {
      const testCases = [
        { bytes: 50 * 1024 * 1024, expected: 50 }, // 50MB
        { bytes: 100 * 1024 * 1024, expected: 100 }, // 100MB
        { bytes: 500 * 1024 * 1024, expected: 500 }, // 500MB
        { bytes: 1024 * 1024 * 1024, expected: 1024 }, // 1GB
      ];

      testCases.forEach(({ bytes, expected }) => {
        const result = convertBytesToMegaBytes(bytes);
        expect(result).toBe(expected);
      });
    });
  });

  describe('Performance', () => {
    it('should handle large numbers efficiently', () => {
      const startTime = performance.now();

      for (let i = 0; i < 1000; i++) {
        convertBytesToMegaBytes(i * 1024 * 1024);
      }

      const endTime = performance.now();
      const executionTime = endTime - startTime;

      // Should complete in reasonable time (less than 100ms)
      expect(executionTime).toBeLessThan(100);
    });
  });
});
