import { Flex, Text, ThemeIcon, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconFileAlert, IconX } from '@tabler/icons-react';
import { motion } from 'framer-motion';
import React, { useCallback } from 'react';
import { useUploadAssetContext } from '../../../../uploadAssetContext';
import { Colors } from '@resola-ai/ui/constants';

const useStyles = createStyles((theme) => ({
  motionContainer: {
    display: 'flex',
    gap: '50px',
    backgroundColor: Colors.decaRed[0],
    width: '100%',
    justifyContent: 'space-between',
    padding: '10px',
    alignItems: 'flex-start',
    marginTop: rem(10),
    borderRadius: rem(8),
  },
  iconContainer: {
    width: '20px',
    height: '20px',
    flexShrink: 0,
    display: 'flex',
    alignItems: 'center',
  },
  themeIcon: {
    cursor: 'pointer',
    border: 0,
    backgroundColor: 'transparent',
    display: 'flex',
    alignItems: 'flex-start',
    alignSelf: 'flex-start',
  },
}));

interface Props {
  code: string;
  message: string;
}

const ErrorDetail: React.FC<Props> = ({ code, message }) => {
  const { classes } = useStyles();
  const { rejectErrors, setRejectErrors } = useUploadAssetContext();

  const hideHandler = useCallback(() => {
    setRejectErrors((prev: Record<string, boolean>) => {
      const newRejectErrors = { ...prev };
      newRejectErrors[code] = false;
      return newRejectErrors;
    });
  }, [setRejectErrors, code]);

  return (
    <motion.div
      className={classes.motionContainer}
      animate={{ opacity: rejectErrors[code] ? 1 : 0 }}
    >
      <Flex align='flex-start'>
        <div className={classes.iconContainer}>
          <IconFileAlert
            color={Colors.decaRed[9]}
            // 20x20
            width={20}
            height={20}
          />
        </div>
        <Text size={'12px'} c={Colors.decaRed[9]} ml={'sm'}>
          {/* if the message has \n then split it and render each line in a new line */}
          {message.split('\n').map((item, key) => {
            return (
              <span key={key}>
                {item}
                <br />
              </span>
            );
          })}
        </Text>
      </Flex>
      <ThemeIcon onClick={hideHandler} variant='default' className={classes.themeIcon}>
        <IconX
          color={Colors.decaGrey[6]}
          // 10x10
          size='1.25rem'
          stroke={2}
        />
      </ThemeIcon>
    </motion.div>
  );
};

export default ErrorDetail;
