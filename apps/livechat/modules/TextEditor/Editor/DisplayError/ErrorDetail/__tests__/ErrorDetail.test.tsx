import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { MantineEmotionProvider } from '@mantine/emotion';
import createCache from '@emotion/cache';
import ErrorDetail from '../index';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, className, animate, ...props }: any) => (
      <div
        className={className}
        data-testid='motion-div'
        data-animate={JSON.stringify(animate)}
        {...props}
      >
        {children}
      </div>
    ),
  },
}));

// Mock the upload asset context
const mockSetRejectErrors = jest.fn();
jest.mock('../../../../../uploadAssetContext', () => ({
  useUploadAssetContext: () => ({
    rejectErrors: {
      'file-size-exceeded': true,
      'file-type-not-allowed': false,
    },
    setRejectErrors: mockSetRejectErrors,
  }),
}));

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const cache = createCache({ key: 'test' });
  return (
    <MantineEmotionProvider cache={cache}>
      <MantineProvider>{children}</MantineProvider>
    </MantineEmotionProvider>
  );
};

describe('ErrorDetail', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('should render with correct props', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      expect(screen.getByTestId('motion-div')).toBeInTheDocument();
      expect(screen.getByText('File size exceeded')).toBeInTheDocument();
    });

    it('should render with multiline message', () => {
      const multilineMessage = 'Line 1\nLine 2\nLine 3';
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message={multilineMessage} />
        </TestWrapper>
      );

      expect(screen.getByText('Line 1')).toBeInTheDocument();
      expect(screen.getByText('Line 2')).toBeInTheDocument();
      expect(screen.getByText('Line 3')).toBeInTheDocument();
    });
  });

  describe('User interactions', () => {
    it('should call setRejectErrors when close button is clicked', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      // Find the close button by its container class instead of role
      const closeButton = screen
        .getByTestId('motion-div')
        .querySelector('[class*="ThemeIcon-root"]');
      expect(closeButton).toBeInTheDocument();

      if (closeButton) {
        fireEvent.click(closeButton);
        expect(mockSetRejectErrors).toHaveBeenCalledWith(expect.any(Function));
      }
    });

    it('should update rejectErrors correctly when close button is clicked', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      const closeButton = screen
        .getByTestId('motion-div')
        .querySelector('[class*="ThemeIcon-root"]');
      expect(closeButton).toBeInTheDocument();

      if (closeButton) {
        fireEvent.click(closeButton);

        // Get the function passed to setRejectErrors
        const updateFunction = mockSetRejectErrors.mock.calls[0][0];

        // Test the function with a mock previous state
        const mockPrevState = {
          'file-size-exceeded': true,
          'file-type-not-allowed': true,
        };

        const result = updateFunction(mockPrevState);

        expect(result).toEqual({
          'file-size-exceeded': false,
          'file-type-not-allowed': true,
        });
      }
    });
  });

  describe('Animation', () => {
    it('should have correct animation props', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      const motionDiv = screen.getByTestId('motion-div');
      const animateData = JSON.parse(motionDiv.getAttribute('data-animate') || '{}');

      expect(animateData).toEqual({ opacity: 1 });
    });

    it('should handle animation data correctly', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      const motionDiv = screen.getByTestId('motion-div');
      const animateData = JSON.parse(motionDiv.getAttribute('data-animate') || '{}');

      expect(animateData).toHaveProperty('opacity');
      expect(typeof animateData.opacity).toBe('number');
    });
  });

  describe('Styling', () => {
    it('should have correct CSS classes', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      const motionDiv = screen.getByTestId('motion-div');
      // Check that the motion div has a class (Mantine generates dynamic class names)
      expect(motionDiv.className).toBeTruthy();
      expect(motionDiv.className.length).toBeGreaterThan(0);
    });

    it('should render close button with correct styling', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='File size exceeded' />
        </TestWrapper>
      );

      const closeButton = screen
        .getByTestId('motion-div')
        .querySelector('[class*="ThemeIcon-root"]');
      expect(closeButton).toBeInTheDocument();
      expect(closeButton?.className).toContain('ThemeIcon-root');
    });
  });

  describe('Edge cases', () => {
    it('should handle empty message', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='' />
        </TestWrapper>
      );

      expect(screen.getByTestId('motion-div')).toBeInTheDocument();
    });

    it('should handle message with only newlines', () => {
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message='\n\n\n' />
        </TestWrapper>
      );

      expect(screen.getByTestId('motion-div')).toBeInTheDocument();
    });

    it('should handle special characters in message', () => {
      const specialMessage = 'Error with special chars: !@#$%^&*()';
      render(
        <TestWrapper>
          <ErrorDetail code='file-size-exceeded' message={specialMessage} />
        </TestWrapper>
      );

      expect(screen.getByText(specialMessage)).toBeInTheDocument();
    });
  });
});
