import React from 'react';
import { render, act } from '@testing-library/react';
import { ConversationContextProvider, useConversationContext } from './conversationContext';
import { IMessage } from '@resola-ai/models';
import { sendCustomEvent } from '@resola-ai/utils';

// Mock dependencies
jest.mock('@resola-ai/utils', () => ({
  sendCustomEvent: jest.fn(),
  createCustomEventListener: jest.fn(() => jest.fn()),
  addParamsToCurrentUrl: jest.fn(),
  createLastMessageFromMessage: jest.fn((message) => ({
    id: message.id,
    created: message.created,
    sender: message.sender,
    data: message.data,
  })),
}));

jest.mock('../services/api', () => ({
  __esModule: true,
  default: {
    getConversationById: jest.fn(),
    changeConversationStatus: jest.fn(),
    getUnreadConversationStatus: jest.fn(),
  },
}));

jest.mock('../hooks/useConversationPaging', () => ({
  __esModule: true,
  default: () => ({
    next: undefined,
    conversations: [],
    conversationsParams: { status: 'new' },
    isEmpty: false,
    loading: false,
    isReachingEnd: false,
    showLoadingMore: false,
    setNext: jest.fn(),
    refetchConversations: jest.fn(),
    refetchConversListByNewParams: jest.fn(),
    updateStatusConversationInAll: jest.fn(),
    updateOrCreateConvInAllConvers: jest.fn(),
    updateExistingConversationInAllConversationsHandler: jest.fn(),
  }),
}));

jest.mock('./userContext', () => ({
  useUserContext: () => ({
    userProfile: { id: 'user1', name: 'Test User' },
    userId: 'user1',
  }),
}));

jest.mock('./teamContext', () => ({
  useTeamContext: () => ({
    teamList: [],
  }),
}));

jest.mock('next/router', () => ({
  useRouter: () => ({
    query: {},
    push: jest.fn(),
  }),
}));

jest.mock('../utils/queryParam', () => ({
  addParamsToCurrentUrl: jest.fn(),
}));
jest.mock('../utils/path', () => ({
  getIsNotMainPathValueAsPath: jest.fn(() => false),
}));

// Helper component to consume the context
const Consumer: React.FC = () => {
  const context = useConversationContext();
  return (
    <div>
      <span data-testid='currentConversationId'>{context.currentConversationId}</span>
      <span data-testid='currentConversationStatus'>
        {context.currentConversation?.status || 'none'}
      </span>
    </div>
  );
};

// Mock message data
const createMockMessage = (senderType: 'enduser' | 'operator' | 'system'): IMessage => ({
  id: 'msg1',
  conversationId: 'conv1',
  created: new Date().toISOString(),
  sender: {
    id: senderType === 'enduser' ? 'enduser1' : 'operator1',
    type: senderType,
    name: senderType === 'enduser' ? 'End User' : 'Operator',
  },
  data: {
    id: 'data1',
    text: 'Test message',
    type: 'text',
  },
});

describe('ConversationContext - receiveAddMessageToConversations', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should change status to inProgress when operator sends message to new conversation', async () => {
    const mockRefetchCurrentConversation = jest.fn();

    // Mock the hook to return our test functions
    jest.doMock('../hooks/useConversationPaging', () => ({
      __esModule: true,
      default: () => ({
        next: undefined,
        conversations: [],
        conversationsParams: { status: 'new' },
        isEmpty: false,
        loading: false,
        isReachingEnd: false,
        showLoadingMore: false,
        setNext: jest.fn(),
        refetchConversations: jest.fn(),
        refetchConversListByNewParams: jest.fn(),
        updateStatusConversationInAll: jest.fn(),
        updateOrCreateConvInAllConvers: jest.fn(),
        updateExistingConversationInAllConversationsHandler: jest.fn(),
      }),
    }));

    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Simulate receiving a message from an operator
    const operatorMessage = createMockMessage('operator');

    await act(async () => {
      // Trigger the event that would call receiveAddMessageToConversations
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: operatorMessage,
      });
    });

    // Wait for the setTimeout to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Verify that the status was changed to inProgress
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: operatorMessage }
    );
  });

  it('should NOT change status to inProgress when end user sends message to new conversation', async () => {
    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Simulate receiving a message from an end user
    const endUserMessage = createMockMessage('enduser');

    await act(async () => {
      // Trigger the event that would call receiveAddMessageToConversations
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: endUserMessage,
      });
    });

    // Wait for the setTimeout to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Verify that the event was triggered but status should NOT change
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: endUserMessage }
    );

    // The status should remain 'new' since the message was from an end user
    expect(getByTestId('currentConversationStatus')).toHaveTextContent('none');
  });

  it('should NOT change status when conversation is already inProgress', async () => {
    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Simulate receiving a message from an operator to an already inProgress conversation
    const operatorMessage = createMockMessage('operator');

    await act(async () => {
      // Trigger the event that would call receiveAddMessageToConversations
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: operatorMessage,
      });
    });

    // Wait for the setTimeout to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Verify that the event was triggered
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: operatorMessage }
    );
  });

  it('should handle system messages without changing status', async () => {
    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Simulate receiving a system message
    const systemMessage = createMockMessage('system');

    await act(async () => {
      // Trigger the event that would call receiveAddMessageToConversations
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: systemMessage,
      });
    });

    // Wait for the setTimeout to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Verify that the event was triggered
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: systemMessage }
    );
  });
});

describe('ConversationContext - syncStatusParamsToCurrConverOrInProgress', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should sync conversation status to filter params when status differs', async () => {
    const mockRefetchConversListByNewParams = jest.fn();

    jest.doMock('../hooks/useConversationPaging', () => ({
      __esModule: true,
      default: () => ({
        next: undefined,
        conversations: [],
        conversationsParams: { status: 'new' },
        isEmpty: false,
        loading: false,
        isReachingEnd: false,
        showLoadingMore: false,
        setNext: jest.fn(),
        refetchConversations: jest.fn(),
        refetchConversListByNewParams: mockRefetchConversListByNewParams,
        updateStatusConversationInAll: jest.fn(),
        updateOrCreateConvInAllConvers: jest.fn(),
        updateExistingConversationInAllConversationsHandler: jest.fn(),
      }),
    }));

    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Trigger the sync function
    await act(async () => {
      sendCustomEvent('deca-livechat-update-status-conversation-to-conversation-context', {});
    });

    // Wait for the setTimeout to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 900));
    });

    // Verify that the sync function was called
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-update-status-conversation-to-conversation-context',
      {}
    );
  });

  it('should NOT automatically change status to inProgress for new conversations', async () => {
    const mockRefetchConversListByNewParams = jest.fn();

    jest.doMock('../hooks/useConversationPaging', () => ({
      __esModule: true,
      default: () => ({
        next: undefined,
        conversations: [],
        conversationsParams: { status: 'new' },
        isEmpty: false,
        loading: false,
        isReachingEnd: false,
        showLoadingMore: false,
        setNext: jest.fn(),
        refetchConversations: jest.fn(),
        refetchConversListByNewParams: mockRefetchConversListByNewParams,
        updateStatusConversationInAll: jest.fn(),
        updateOrCreateConvInAllConvers: jest.fn(),
        updateExistingConversationInAllConversationsHandler: jest.fn(),
      }),
    }));

    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Trigger the sync function
    await act(async () => {
      sendCustomEvent('deca-livechat-update-status-conversation-to-conversation-context', {});
    });

    // Wait for the setTimeout to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 900));
    });

    // Verify that the sync function was called but should NOT change status to inProgress
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-update-status-conversation-to-conversation-context',
      {}
    );

    // The status should remain unchanged
    expect(getByTestId('currentConversationStatus')).toHaveTextContent('none');
  });
});

describe('ConversationContext - Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle end user message without changing tab from 未対応 to 対応中', async () => {
    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // Simulate receiving a message from an end user
    const endUserMessage = createMockMessage('enduser');

    await act(async () => {
      // Trigger the event that would call receiveAddMessageToConversations
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: endUserMessage,
      });
    });

    // Wait for any timeouts to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    });

    // Verify that the event was triggered
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: endUserMessage }
    );

    // The conversation should remain in the 未対応 (new) status
    expect(getByTestId('currentConversationStatus')).toHaveTextContent('none');
  });

  it('should change tab from 未対応 to 対応中 only when operator responds', async () => {
    const { getByTestId } = render(
      <ConversationContextProvider>
        <Consumer />
      </ConversationContextProvider>
    );

    // First, simulate receiving a message from an end user (should NOT change status)
    const endUserMessage = createMockMessage('enduser');

    await act(async () => {
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: endUserMessage,
      });
    });

    // Wait for any timeouts to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // The conversation should still be in 未対応 status
    expect(getByTestId('currentConversationStatus')).toHaveTextContent('none');

    // Now simulate receiving a message from an operator (should change status)
    const operatorMessage = createMockMessage('operator');

    await act(async () => {
      sendCustomEvent('deca-livechat-add-message-in-conversation-to-conversation-context', {
        message: operatorMessage,
      });
    });

    // Wait for any timeouts to execute
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 600));
    });

    // Verify that both events were triggered
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: endUserMessage }
    );
    expect(sendCustomEvent).toHaveBeenCalledWith(
      'deca-livechat-add-message-in-conversation-to-conversation-context',
      { message: operatorMessage }
    );
  });
});
