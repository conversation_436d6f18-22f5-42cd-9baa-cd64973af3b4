{"auto_reply.title": "自動応答設定", "auto_reply.out_of_working.title": "営業時間外の自動返信", "auto_reply.out_of_working.description.off": "自動返信をONにすると、お客様からメッセージを受信した際に、設定メッセージを自動で送信します。", "auto_reply.out_of_working.description.on": "オンにすると、営業状況が「準備中」の際、お客様からのお問い合わせに設定したメッセージが自動で送信されます。", "auto_reply.send_message_to_awaiting_user.title": "待機中のお客様への自動応答", "auto_reply.send_message_to_awaiting_user.description": "オンにすると、有人チャット開始から指定した時間が経過しても担当者が未割り当てのお問い合わせに対し、設定したメッセージが自動で送信されます。", "auto_reply.send_message_to_awaiting_user.setting_time.first": "この自動メッセージは", "auto_reply.send_message_to_awaiting_user.setting_time.first.1": "さらに", "auto_reply.send_message_to_awaiting_user.setting_time.first.later_part": "分後にお客様に送信されます", "auto_reply.send_message_to_awaiting_user.setting_time.first.later_part.1": "分後", "auto_reply.send_message_to_awaiting_user.add_new_setting_time.button": "追加する", "auto_reply.send_message_to_awaiting_user.option_send.option_1.first_part": "担当者が返信するまで", "auto_reply.send_message_to_awaiting_user.option_send.option_1.later_part": "回まで繰り返す", "auto_reply.send_message_to_awaiting_user.option_send.option_2": "繰り返さない", "required_field": "この項目は必須です", "save_btn": "保存する", "character": "文字", "automationList.title": "オートメーションリスト", "automationList.description": "オートメーションは、予め設定したルールに従ってお問い合わせに対応する機能です。これにより、リアルタイムでのチャット対応が難しい場合でも、自動化されたプロセスがお問い合わせに初期の対応を提供できます。", "action.create": "新規オートメーション作成", "action.edit": "オートメーション編集", "delete_confirm_message": "本当にこのオートメーションを削除しますか？", "delete_button": "削除する", "cancel_button": "キャンセル", "no_automation": "作成されたオートメーションはありません", "trigger_not_set": "オートメーションを作成するには、\n少なくとも1つのトリガーと1つのアクションが必要です", "active": "有効", "inactive": "無効", "true_branch": "合致する", "false_branch": "合致しない", "channel_default_ocs": "セルフチャンネル", "ocs_channel_label": "LINE {number} 本店用", "condition.whenNewMessageCome": "有人チャット開始時", "condition.whenOperatorAssigned": "チャットの返信があった時", "condition.whenChatIsClosed": "有人チャット終了時", "backToAutomationList": "オートメーションリストに戻る", "detail.defaultName": "オートメーション", "conditionNode.label.user": "ユーザーはリピーターに該当する場合", "conditionNode.label.user.yes": "ユーザーがリピーターに該当する場合", "conditionNode.label.user.no": "ユーザーがリピーターに該当しない場合", "conditionNode.label.user.crm.label": "ユーザーはCRMの列に該当する場合", "conditionNode.label.conversation_original": "チャットのチャネルが\n{name}に該当する場合", "conditionNode.label.conversation.first": "チャットのチャネルが", "conditionNode.label.conversation.last": "に該当する場合", "detail.trigger.label": "トリガー", "detail.trigger.select.label": "トリガーを選択", "detail.finish": "終了", "detail.save": "保存する", "detail.trigger.cancel": "キャンセル", "detail.trigger.save": "保存する", "detail.trigger.create": "作成する", "detail.trigger.title": "トリガー選択", "detail.trigger.description": "このオートメーションをトリガーする方法を選択", "action.add.new.condition": "条件を追加", "action.add.new.action": "アクションを追加", "action.add.new.branch": "分岐", "detail.condition.title": "条件を選択", "detail.condition.select.label": "条件", "detail.condition.select.subject.label": "対象", "detail.condition.select.subject.option.1": "ユーザー", "detail.condition.select.subject.option.2": "チャット", "detail.condition.select.condition.label": "条件項目", "detail.condition.select.condition.option.1": "リピーター", "detail.condition.select.condition.option.2": "チャット", "detail.condition.select.condition.option.2.sub.1": "チャネル", "detail.condition.select.condition.option.2.sub.2": "担当チーム", "detail.condition.select.condition.option.2.sub.3": "担当者", "detail.condition.select.condition.option.2.sub.4": "担当者の在席状況", "detail.condition.select.condition.option.2.sub.4.radio.1": "在席", "detail.condition.select.condition.option.2.sub.4.radio.2": "離席", "detail.condition.select.condition.option.2.sub.5": "自動終了された会話", "detail.condition.select.condition.option.2.sub.5.radio.1": "はい", "detail.condition.select.condition.option.2.sub.5.radio.2": "いいえ", "detail.condition.select.condition.option.2.sub.6": "CRM", "detail.condition.select.condition.option.2.sub.6.link_text": "CRMをインストールする", "detail.condition.select.condition.option.2.sub.6._column_select": "列", "detail.condition.select.condition.option.2.sub.6._column_error": "この列はCRMから削除されました", "detail.condition.select.condition.option.2.sub.6._value_placeholder": "自由入力", "detail.condition.radio.value.label": "値", "detail.condition.radio.value.radio.label.1": "はい", "detail.condition.radio.value.radio.label.2": "いいえ", "detail.condition.select.placeholder": "条件を選択してください", "detail.condition.1": "ユーザーがリピーターである", "detail.condition.2": "ユーザーIDが次のいずれかと等しい", "detail.condition.3": "チャネルが次のいずれかと等しい", "detail.condition.4": "チャットステータス次のいずれかと等しい", "detail.condition.5": "チャットメッセージに次の言葉が含まれる", "detail.condition.add.more": "もう1つ条件を追加", "detail.action.title": "アクションを選択", "detail.action.select.label": "アクション", "detail.action.select.placeholder": "アクションを選択してください", "detail.action.1": "指定のチームにアサイン", "detail.action.2": "指定のオペレーターにアサイン", "detail.action.3": "チャットを送信", "detail.condition.filter.label": "フィルター", "detail.action.select.label.1": "割り当て", "detail.action.select.label.2": "チャットを送信", "detail.action.button.label.1": "担当者を選択する", "detail.action.message.missing_params": "担当者割り当ては必須です", "detail.action.selected_info.title": "選択済み：", "detail.action.selected_info.team": "チーム：{name}", "detail.action.selected_info.assignee": "担当者：{name}", "detail.action.select_2.label": "チャネル", "detail.action.text_editor.label": "メッセージ", "detail.action.text_editor.placeholder": "メッセージを入力してください", "detail.action.enable_period.label": "日時設定を利用する", "detail.action.select_3.day_after.label": "日後", "detail.action.select_3.day_after.description": "（最大30日まで設定可能です）", "detail.action.select_3.hour_after.label": "時間後", "detail.action.select_3.day_hour_after.label_1": "日後の", "detail.action.select_3.day_hour_after.label_2": "時", "detail.action.select_3.day_hour_after.label_3": "分", "detail.action.unspecified_assignee": "指定なし", "detail.action.no_result_search_operator": "一致する結果はありません", "detail.action.search_count_message": "「{search}」の検索結果　{number}件", "detail.action.panel_assign.title": "担当者を選択", "detail.action.panel_assign.description": "チームを選択した上で担当者を選択してください。", "detail.action.panel_assign.search_placeholder": "検索", "detail.action.panel_assign.clear_search": "クリア", "condition_type.and": "すべての条件に合致する（AND）", "condition_type.or": "少なくとも一つの条件に合致する（OR）", "action_label_assign": "{name}にアサイン", "action_label_no_assign": "アサイン", "action_label_send_message": "{time} 後に{channel}本店用にチャットを送信", "action_label_send_message_only": "チャットを送信", "please_select_one_option": "オプションを 1 つ選択してください", "exceed_limit_warning": "アイテムの数が上限に達したため、 \n新たに追加することができません", "close": "閉じる", "action.assign.select_box_title": "割り当て設定", "action.assign.select_box.option_1": "チームに割り当て", "action.assign.select_box.option_2": "担当者に割り当て", "action.assign.select_box.option_3": "前回の担当者に再割り当て", "action.button.select_team.title": "チームを選択する", "action.assign_method.title": "担当者の割り当て方式", "action.assign_method.option_1": "ラウンドロビン", "action.assign_method.option_1.tooltip": "順番に一人ずつオペレーターを割\nり当てます", "action.assign_method.option_2": "ランダム", "action.assign_method.option_2.tooltip": "対応可能なオペレーターの中からランダムに選択し割り当てます", "action.assign_method.option_3": "対応件数順", "action.assign_method.option_3.tooltip": "未対応および対応中の件数が最も少ないオペレーターから順に割り当てます", "action.assign_method.option_4.title": "保留中を優先", "action.assign_method.option_4": "保留中の会話を優先割り当て", "action.assign_method.option_4.tooltip": "新しい会話を作成する際、未割り当ての保留中の会話があれば優先的に割り当てる", "action.assign_method.option_5": "指定なし", "action.assign_method.option_5.tooltip": "担当者を指定せずにチームに割り当てます", "action.assign_method.option_6": "会話終了時に未割り当ての会話を自動で割り当てる", "action.assign_method.option_6.tooltip": "ONにすると、現在の会話を終了した後に未割り当ての会話がある場合、自動的に次の会話が割り当てられます", "action.assign_team.max_conversation.title": "対応件数の制限", "action.assign_team.max_conversation.description": "担当者ごとの問い合わせ数を", "action.assign_team.max_conversation.limit_label": "件までに制限する", "action.webhook.title": "Webhook", "action.webhook.option_label": "Webhookに送信", "team_panel.title": "チームを選択", "team_panel.team_title": "チーム", "team_panel.operator_title": "担当者", "action.assign.select_box.option_3.checkbox_title": "前回の担当者が存在しない場合は割り当てを行わない", "action.node.operator_or_team.selected": "{name}に割り当て", "action.error.missing_team_id": "チームの選択は必須です", "action.error.missing_operator_id": "担当者の選択は必須です", "condition.node.team_selected.label_original": "チャットの担当チームが\n{name}に該当する場合", "condition.node.team_selected.label.first": "チャットの担当チームが", "condition.node.team_selected.label.last": "に該当する場合", "condition.node.operator_selected.label_original": "チャットの担当者が\n{name}に該当する場合", "condition.node.operator_selected.label.first": "チャットの担当者が", "condition.node.operator_selected.label.last": "に該当する場合", "condition.node.presence_assignee_selected.label.true": "チャットの担当者の在席状況が\n在席の場合", "condition.node.presence_assignee_selected.label.false": "チャットの担当者の在席状況が\n離席の場合", "condition.node.auto_closed_conversation.label.true": "チャットが自動終了\n会話の場合", "condition.node.auto_closed_conversation.label.false": "チャットが自動終了しない\n会話の場合", "closeRoomBtn": "終了", "wrapUpBtn": "後処理", "confirmCloseConversationMessage": "本当に会話を終了しますか？", "explainMessageCloseConversation": "会話が終了すると、「終了済み」 タブに表示されます", "confirmClosedButton": "終了する", "cancelClosedButton": "キャンセル", "noPermission": "この会話を表示する権限がありません。自分を割り当てるか、チームを割り当ててください", "new": "ユーザーが有人チャットを開始しました", "completed": "終了済み", "unsupported.placeholder": "このファイル形式は現在サポートされていないため、送信できませんでした。", "unsupported.sticker.placeholder": "ユーザーがスタンプを送信しましたが、受信できませんでした。", "unsupported.image.placeholder": "ユーザーが画像を送信しましたが、受信できませんでした。", "activity.completed": "チャットが{operatorName}によって終了されました", "activity.assigned.you": "あなたは担当者になりました", "activity.assigned.operator": "{operatorName}が担当者になりました", "activity.assigned.operator.unknown": "{operatorName}は担当者の割り当てから解除されました", "activity.assigned.team": "{teamName}に割り当てられました", "activity.inwrapup": "{name}によって後処理が開始されました", "activity.inwrapup.system": "自動終了設定によって後処理が開始されました", "ai.summary.tooltip": "会話をAIで要約する", "ai.summary.content.title": "[AI概要]", "bot": "ボット", "create_new_conversation": "新しく有人チャットを開始する", "confirm_move_to_new_conversation": "このユーザーは現在有人チャット接続中です。\n該当のお問い合わせに移動してもよろしいですか？", "goto_other_conversation": "移動する", "confirmWrapUpConversation": "後処理をはじめますか？", "explainWrapUpConversation": "後処理をはじめると、ユーザーとの会話を再開する\nことはできませんのでご注意ください。", "wrapUpButton": "後処理をはじめる", "conversationCompletedByUser": "チャットがユーザーによって終了されました", "conversationCompletedBySystem": "会話が自動終了設定によって終了されました", "autoCloseTooltipText": "自動終了", "autoCloseMenuChangeTime": "自動終了する時間を変更", "autoCloseMenuToggleOn": "自動終了をオンにする", "autoCloseMenuToggleOff": "自動終了をオフにする", "autoCloseMenuTitle": "自動終了する時間を変更", "autoCloseMenuTextOne": "このお問い合わせは最後のメッセージから", "autoCloseMenuTextTwo": "に自動終了します", "minutes": "分後", "hours": "時間後", "filter.new": "未対応", "filter.processing": "対応中", "filter.done": "終了済み", "platform.line": "LINE", "platform.web": "Webコーポレート", "no.conversation": "該当のお問い合わせはありません", "wrapUpBadge": "後処理", "noAssignee": "担当者なし", "hasAssigneeName": "担当者：{name}", "errorMessageSelfAssignAlreadyTaken": "この会話はすでに他の担当者に\n割り当てられています", "errorMessageSelfAssignAlreadyTakenCaseAnother": "この会話はすでに他の担当者に 割り当てられています", "placeholder": "テキストを入力してください", "sendMessage": "送信", "aiAutoCorrect": "AI自動調整", "dropZone.description": "ドラッグ＆ドロップで画像を追加できます", "dropZone.warning1": "複数ファイルをアップロードできますが、", "dropZone.warning2": "アップロードできるファイルのタイプ・サイズには制限があります。", "dropZone.warning3": "画像：png, jpg, jpeg, svg (1ファイル5MB以下)", "asset.upload.error.filetoolarge": "ファイルが{LIMIT_SIZE}MBを超えています", "asset.upload.error.fileinvalidtype": "このファイルはサポートされていない形式のようです。PNG, JPG, JPEG, SVG, PDF, MP4, MOV, 3GP, AVI 形式のファイルを選択してください", "asset.upload.error.filenotsupport": "このファイルはサポートされていない形式のようです。PNG, JPG, JPEG, SVG, PDF, MP4, MOV, 3GP, AVI 形式のファイルを選択してください", "asset.upload.error.toomanyfiles": "ファイルが多すぎます。一度に{LIMIT_FILE_PER_UPLOAD}個以上のファイルをアップロードすることはできません", "aiAutoAdjustmentTitle": "AI自動リライト", "autoAdjustment": "ニュアンス", "freeToSpecifyTitle": "自由指定", "freeToEnterPlaceHolder": "ご自由に入力してください", "letter": "文字", "save": "保存する", "saved": "保存しました", "editorAutoReplyPlaceholder": "お問い合わせいただきありがとうございます。誠に申し訳ございませんが、ただいま準備中となっておりますため、ご返信までにお時間をいただいております。", "tag.correction.label": "校正", "tag.correction.value": "CORRECT_SENTENCE", "tag.polite.label": "丁寧に", "tag.polite.value": "TONE_POLITE", "tag.friendly.label": " 親しみやすく", "tag.friendly.value": "TONE_FRIENDLY", "tag.softer.label": "柔らかく", "tag.softer.value": "TONE_SOFTER", "tag.brighter.label": "明るく", "tag.brighter.value": "TONE_BRIGHTER", "tag.longer.label": "長く", "tag.longer.value": "MAKE_SENTENCE_LONGER", "tag.shorter.label": "短く", "tag.shorter.value": "MAKE_SENTENCE_SHORTER", "reply.organization": "公式アカウントで返信", "reply.team": "チームで返信", "reply.user": "担当者で返信", "externalService.pageTitle": "外部サービス連携", "tab.integrated.tools": "連携済み", "tab.all.tools": "すべてのサービス", "tool.integrated": "連携済み", "no.integrated.tool": "連携済みツールはありません。", "all.tools.list": "すべてのツール", "take.a.look.available.tooks": "よりご利用になりたいツールをお選びください。", "button.integrated": "連携する", "button.cancel": "キャンセル", "cooperation": "連携", "lineIntegrationDescription": "LINE公式アカウントを連携し、メッセージを送受信できるようにします。", "webhookIntegrationDescription": "Webhook を使用して、会話履歴といったイベントをを外部ソースにリアルタイムで送信できます。", "lineSettingDescription": "LINE DevelopersからチャネルIDとチャネルシークレットをコピーして、こちらに貼り付けてください。", "channelNameLabel": "チャネル名", "channelNamePlaceholder": "管理用の名称です。ユーザーには公開されません。", "yourUserIdLabel": "ユーザーID", "webhookUrlLabel": "ウェブフックURL", "channelIDLabel": "チャネル ID", "channelSecretLabel": "チャネルシークレット", "accessTokenLabel": "アクセストークン", "basicIDLabel": "ボットベーシックID", "copy": "コピーする", "cancelToolPackage": "連携を解除する", "cancelConfirmMessage": "本当に連携を解除しますか？", "button.unlock": "解除する", "error.msg.field.required": "必須項目を入力してください", "error.msg.field.channelName.duplicated": "このチャネル名はすでに使用されています", "count.characters": "残り{totalChars}文字", "error.msg.field.channelId.duplicated": "このチャネルIDはすでに使用されています", "webook_error_modal_description_first": "この Webhook はオートメーションによって使用されているようです。\n連携を解除することはできません。", "webook_error_modal_description_link": "オートメーションページ", "webook_error_modal_description_last": "をご確認ください。", "webook_error_modal_btn": "閉じる", "webhook.back_link_title": "外部サービス連携に戻る", "webhook.page_title": "Webhook連携", "webhook.page_description": "リクエストを送信する外部サービスの詳細を入力してください", "webhook.field.name.label": "名前", "webhook.field.name.placeholder": "管理用の名称です。ユーザーには公開されません", "webhook.field.method.label": "メソッド", "webhook.field.url.label": "URL", "webhook.field.header.label": "ヘッダ", "webhook.field.header.placeholder.key": "キー", "webhook.field.header.placeholder.value": "値", "webhook.field.payload.label": "ペイロード", "webhook.field.payload.option.transform": "規定", "webhook.field.payload.option.custom": "カスタム", "webhook.field.payload.textarea.custom.placeholder": "ペイロードを入力してください", "webhook.field.transform.label": "データ変換", "webhook.field.transform.button.label": "変換する", "webhook.field.transform.modal.title": "ペイロードのサンプル", "webhook.field.transform.modal.edit": "編集", "webhook.field.transform.modal.environment_variable": "変数", "webhook.field.transform.modal.edit.placeholder": "ここを編集してください...", "webhook.field.transform.custom_payload.edit.placeholder": "ここにカスタム ペイロードを入力してください。", "webhook.field.transform.modal.preview": "プレビュー", "webhook.field.auth0.checkbox.label": "OAuth2", "webhook.field.auth0.checkbox.description": "利用する", "webhook.field.auth0.grant_type.label": "認証タイプ", "webhook.field.auth0.grant_type.option_password": "パスワード", "webhook.field.auth0.client_id.label": "クライアントID", "webhook.field.auth0.client_id.placeholder": "クライアントID", "webhook.field.auth0.client_secret.label": "クライアントシークレット", "webhook.field.auth0.client_secret.placeholder": "クライアントシークレット", "webhook.field.auth0.user_id.label": "ユーザーID", "webhook.field.auth0.user_id.placeholder": "ユーザーID", "webhook.field.auth0.password.label": "パスワード", "webhook.field.auth0.password.placeholder": "パスワード", "webhook.field.auth0.scope.label": "スコープ", "webhook.field.auth0.scope.placeholder": "eg: read:org", "webhook.field.auth0.access_token_url.label": "アクセストークンURL", "webhook.field.auth0.access_token_url.placeholder": "https://", "webhook.field.auth0.access_token_path.label": "アクセストークンパス", "webhook.field.auth0.access_token_path.placeholder": " ", "webhook.button.test.label": "テスト送信", "webhook.button.save.label": "連携する", "webhook.remove_integration.label": "連携を解除する", "conversation.created": "有人チャット開始時", "conversation.message.received": "チャットの返信があった時", "conversation.closed": "有人チャット終了時", "requiredField": "この項目は必須です。", "wrongFormat": "有効なデータを入力してください", "char": "文字", "warning_exit": "行った変更が保存されない可能性があります。", "success_label": "成功", "error_label": "エラー", "error_description": "URLへの接続を試みましたが失敗しました", "integration_type": "連携タイプ", "chatwindow": "チャットウィンドウ", "modal.transformer.variable.placeholder": "カスタム項目", "modal.transformer.variable.variable_key": "変数", "modal.transformer.variable.variable_value": "値", "modal.transformer.variable.variable_value_runtime_content": "処理時に値を取得", "modal.transformer.variable.button_add_row": "追加する", "modal.transformer.variable.button_add_row.error_tooltip": "変数の数が上限に達したため、\n新たに追加することができません ", "modal.transformer.variable.error_same_variable_name": "この名前はすでに使用されています", "modal.transformer.variable.error_enter_required_field": "必須項目を入力してください", "profile": "プロフィール", "logout": "ログアウト", "accountSettings": "プロフィール", "decaChatbot": "DECA チャットボット", "decaChatwindow": "DECA チャットウィンドウ", "decaKnowledgeBase": "DECA ナレッジベース", "decaLiveChat": "DECA 有人チャット", "decaCRM": "DECA CRM", "decaForms": "DECA フォーム", "decaTables": "DECA テーブル", "decaAIWidgets": "DECA AIスタジオ", "decaPages": "DECA ページ", "decaAiStudio": "DECA AIスタジオ", "inbox.title": "受信トレイ", "inbox.myassigned.title": "自分", "inbox.myassigned.assigned.title": "すべて", "inbox.myassigned.bookmark.title": "ブックマーク", "team_detail.user.status.title": "ステータス", "inbox.myassigned.online.title": "在席", "inbox.myassigned.offline.title": "離席中", "inbox.team.title": "チーム", "inbox.team.edit.title": "チームを編集", "inbox.team.manage.title": "メンバーを管理", "inbox.team.unassigned.title": "担当者なし", "inbox.team.all.title": "すべて", "settings.title_menu": "設定", "settings.team_user_menu_label": "チームとユーザー管理", "settings.business_status_menu_label": "営業状況管理", "settings.auto_reply_settings_label": "自動応答設定", "settings.external_tool_integration_label": "外部サービス連携", "settings.notification_settings": "通知設定", "settings.function_settings": "機能設定", "settings.personal_settings": "個人設定", "settings.auto_assignment": "自動割り当て", "settings.auto_assignment_modal": "ただいま、未割り当ての会話はありません", "personal.setting.title": "個人設定", "conversation.setting.title": "会話設定", "notification.with_sound": "音で通知", "notification.sound": "通知音", "notification.sound.label": "通知音 {index}", "notification.sound.option.none": "通知しない", "notification.sound.option.first": "初回メッセージのみ", "notification.sound.option.all": "すべてのメッセージ", "notification.sound.option.sound": "通知音 ", "method.send_message.title": "メッセージの送信方法", "method.send_message.enter": "Enter", "method.send_message.enter_shift": "Shift + Enter", "title": "通知設定", "description": "お問い合わせに関する通知を受け取る方法をお好みに合わせて変更できます。", "notification.ticket_assign_to_me_label": "自分に割り当てられたお問い合わせ", "notification.ticket_assign_to_team_label": "自分のチームに割り当てられたお問い合わせ", "notification.ticket_assign_to_unknown_label": "担当者が割り当てられていないお問い合わせ", "notification.ticket_pic_change_or_assign_to_me_label": "担当者が変更され自分に割り当てられた時", "notification.type.in_app": "アプリ内通知", "notification.type.email": "メール通知", "notification.notify.when_new_message": "すべてのメッセージ", "notification.notify.when_start_conversation": "開始時のみ", "operation_title": "営業状況管理", "operation_status_title": "現在の営業状況", "operation_status_description": "オンにすると、チャットを受け付けます。", "operation_auto_change_status_title": "営業状況の自動変更", "operation_auth_change_status_description": "オンにすると、以下の「営業時間の設定」および「休日の設定」に応じて、自動で「現在の営業状況」が変更されます。", "operation_business_hour_title": "営業時間の設定", "operation_business_hour_collective_setting": "一括設定", "apply_to_all_button": "すべてに適用", "hour": "時", "minute": "分", "day.monday": "月", "day.tuesday": "火", "day.wednesday": "水", "day.thursday": "木", "day.friday": "金", "day.saturday": "土", "day.sunday": "日", "operation_holiday_setting_title": "休日の設定", "operation_holiday_setting_description": "以下のボタンを押して休日を追加することができます。登録された休日は営業状況が「準備中（休日設定）」となります。", "add_holiday_button": "休日を追加する", "add_holiday_title": "休日を追加", "input_holiday_name_label": "休日名", "input_holiday_default_value": "休日", "input_date_time_label": "日付と時間選択", "date_time_selected": "日付選択済み：", "add_label": "追加する", "date_label": "日付", "edit_label": "編集", "delete_label": "削除", "edit_holiday_label": "休日を編集", "save_label": "保存する", "delete_holiday_confirm_message": "この休日を削除してもよろしいですか?", "delete_btn_label": "削除する", "from": "から", "to": "まで", "operation_auto_reply_setting_title": "営業時間外の自動返信", "operation_auto_reply_setting_description_on": "オンにすると、営業状況が「準備中」の際、お客様からのお問い合わせに設定したメッセージが自動で送信されます。", "operation_auto_reply_setting_description_off": "自動返信をONにすると、お客様からメッセージを受信した際に、設定メッセージを自動で送信します。", "start_stop_services_title": "サービスの起動・停止", "status_open": "営業中", "status_preparing": "準備中", "operation_business_status_description": "オフにすると、営業状況が「準備中」となり、チャット受け付けを停止します。緊急時などにご利用ください。", "apply": "適用する", "error_choose_date_range": "日付を選択してください", "holiday_setting": "(休日設定)", "invalid_time_input": "開始時間よりも後の時間を終了時間として設定してください", "notification.event.message.new": "{user}さんからお問い合わせが届いています。", "user.anonymous": "ゲスト", "notification.event.conversation.created": "{user}さんが会話を開始しました", "notification.event.conversation.status.updated": "{user}さんが会話のステータスを{status}に更新しました", "notification.event.conversation.closed": "{user}さんが会話を終了しました。", "conversation.status.new": "未対応", "conversation.status.inProgress": "対応中", "conversation.status.completed": "終了済み", "notification.event.conversation.operator.assigned.title": "ユーザー: {name}", "notification.event.conversation.operator.assigned.description": "{operator}さんにお問い合わせが割り当てられました。", "notification.event.conversation.operator.no_data": "オペレーターがお問い合わせが割り当てられません", "chatbot": "チャットボット", "chatbot_title_modal": "チャットボット顧客情報", "customer_name": "顧客名", "email": "メール", "phone_number": "電話番号", "company": "会社", "memo": "メモ", "custom_items": "カスタム項目", "labels": "ラベル", "more": "もっと見る", "chatbot_history": "チャットボット履歴", "readmore": "もっと読み込み", "no_message_history": "メッセージ履歴がありません", "search_placeholder": "読み込んだ内容から検索", "widget_from_chatbot": "このウィジェットを表示するには\nLIBEROとの連携が必要です", "change_warning": "行った変更が保存されない可能性があります。", "search_label": "検索", "search_result_count": "検索結果：{count}", "no_search_result": "一致する検索結果がありません", "warningResult": "過去3か月間のメッセージが検索対象です", "preBtn": "前へ", "nextBtn": "次へ", "main.title": "お問い合わせの担当者がいません", "main.description": "担当者になる場合は、下のボタンを押してください", "main.submit": "担当者になる", "popup.selfAssign.title": "このお問い合わせを担当しますか？", "popup.selfAssignTeam.description1": "複数のチームに所属しているようです。割り当てたいチームを選択してください", "popup.selfAssignTeam.description2": "割り当てたいチームを選択してください", "popup.selfAssignTeam.label": "担当チーム", "popup.selfAssignTeam.placeholder": "担当チームを選択してください", "popup.selfAssign.cancel": "キャンセル", "popup.selfAssign.submit": "担当する", "popup.selfAssign.description": "他の人を割り当てたい場合は、「対応状況」より変更してください", "popup.selfAssignTeam.noTeam": "あなたはどのチームにも所属していません", "pageTitle": "チームとユーザー管理", "tab.team_list_label": "チーム ", "tab.user_list_label": "ユーザー", "tab.team_list.description": "チームを作成し接客チャットの担当者を自動で振り分ける際に活用できます", "tab.team_list.button_add_team_label": "チームを作成する", "tab.team_list.input_placeholder": "チーム名で検索", "tab.team_list.member_count_label": "（{count}人）", "tab.team_list.button_disabled": "無効", "tab.team_list.create_team_modal.title": "新規チーム作成", "tab.team_list.edit_team_modal.title": "チーム名と説明文編集", "tab.team_list.create_team_modal.name": "チーム名", "tab.team_list.create_team_modal.name.placeholder": "チーム名を入力してください。ユーザーに公開されます", "tab.team_list.create_team_modal.description": "説明", "tab.team_list.create_team_modal.description.placeholder": "このチームについての説明を記載してください", "tab.team_list.success_create_team_modal.name": "「{name}」を作成しました！", "tab.team_list.success_create_team_modal.description": "現在はあなたのみがこのチームに属しています", "tab.user_list.description": "こちらでユーザーの招待、所属チーム、および権限を管理できます", "tab.user_list.button_add_user_label": "ユーザーを招待する", "tab.user_list.input_placeholder": "ユーザー名で検索", "tab.user_list.table.user_name": "氏名", "tab.user_list.table.user_mail": "メール", "tab.user_list.table.user_role": "権限", "tab.user_list.table.team_name": "所属チーム", "tab.user_list.table.edit_label": "編集", "tab.user_list.pagination.forward": "前へ", "tab.user_list.pagination.next": "次へ", "tab.user_list.user_role.operator": "オペレーター", "tab.user_list.user_role.admin": "マネージャー", "field_required_message": "この項目は必須です", "only_input_number_chars": "この入力は {number} 文字に制限されています", "close_label": "閉じる", "confirm_label": "確認する", "add_member_button_label": "メンバーを追加する", "modal.add_team_member_title": "チームメンバーを追加", "modal.add_team_member_description": "メンバーを選択してください", "add_member_button": "追加する", "back_to_team_list_label": "チームとユーザー管理に戻る", "tooltip.edit_label": "編集する", "tooltip.delete_label": "削除する", "check_box_disable_team_label": "有効", "modal_confirm_disable_team_title": "チームを無効にします。\n全ての操作が有効にするまで \nできなくなります", "modal_confirm_disable_team_button_confirm": "無効にする", "modal_edit_member_title": "ユーザー編集", "modal_edit_member_team_count": "所属チーム ({number}人)", "modal_save_button_label": "保存する", "title_remove_user_from_team": "本当にこのユーザーをチームから削除しますか？削除されたユーザーはチームのお問い合わせを参照できなくなります", "content_remove_user_from_team": "割り当てられたお問い合わせがある場合は、「担当者なし」のメニューに移動します", "remove_button": "チームから削除する", "no_data_label": "データなし", "duplicate_team_name": " この{team}はすでに使用されています", "no_team": "チームが作成されていません。新しいチームを作成してください", "search_no_result": "一致する検索結果がありません", "no_user": "ユーザーが招待されていません", "image_label": "画像", "image_des": "この画像はお客様に表示されます", "update_image_button": "画像を変更する", "image_upload_des": "対応ファイル形式: png (最大1MBまで。250px x 250px推奨)", "image_exceed_size": "ファイルサイズの上限は{number}MBです", "functionSettings": "機能設定", "autoConvAssign": "会話の自動割り当て", "showAutoBtnIfOff": "受信トレイに自動割り当てボタンを表示する", "showAutoBtnIfOffDesc": "OFFにするとボタンが非表示となります", "convEndMethod": "会話終了方法", "canEndWithoutReply": "返信せずに終了できる", "canEndWithoutReplyDesc": "OFFにすると返信してから会話終了できます", "default": "デフォルト", "changeAutoClose": "会話の自動終了", "changeAutoCloseTextOne": "新しいメッセージが送信されない場合、最後のメッセージから", "changeAutoCloseTextTwo": "後に会話を自動で終了する", "autoSendMessageBeforeComplete_1": "会話が自動で終了する", "autoSendMessageBeforeComplete_2": "前にお客様にメッセージを送信する", "autoSendMessageBeforeComplete_explain": "送信メッセージの時間は、自動終了の時間より短く設定する必要があります", "autoSendMessageBeforeComplete_defaultMessage": "お問い合わせいただき、誠にありがとうございました。この会話は3分後に自動的に終了いたします。今後ともどうぞよろしくお願い申し上げます。", "createNewConversationLabel": "会話の新規作成", "createNewConversationOption": "「終了済み」ステータスの会話に「新しく有人チャットを開始する」ボタンを表示する", "createNewConversationText": "OFFにするとボタンが非表示となります", "aiSummaryFunction": "AI要約機能の設定", "editPrompt": "プロンプトを編集する", "button.save": "保存する", "button.saved": "保存しました！", "editSummaryPrompts": "AI要約機能のプロンプトを編集", "defaultAIPrompt": "あなたはカスタマーサポート担当者です。以下の会話はカスタマーサポート担当者とお客様との会話のやり取りです。以下の会話のやり取りの内容を日本語で要約してください。", "aiSummaryPromptPlaceholder": "プロンプトを入力してください（必須）", "requiredEntryField": "この項目は入力が必須です", "characterCount": "文字", "reset": "リセットする", "maxConversationSettingsTitle": "同時接続ユーザーの上限", "maxConversationSettingsDescription": "有人チャットに同時に接続できるユーザー数を設定できます。1〜100の数値を入力してください。", "teamListDefaultTeamTooltip": "デフォルトチームのため、退出したりメンバーを削除したりすることはできません。", "label.customer": "対応状況", "label.customer.name": "担当者", "label.customer.team": "担当チーム", "label.customer.lastTimeChat": "最後のチャットから", "label.customer.lastTimeChatCompleted": "最後のチャット", "label.customer.equivelantTime": "対応時間", "label.customer.convoCount": "会話回数", "label.customer.convoCountTag": "回目", "label.customer.convoCountTagOne": "初回", "label.customer.convoCountTagTwo": "2回目", "label.customer.convoCountTagThree": "3回目", "label.customer.convoCountTagTen": "10回以上", "label.customer.note": "メモ", "label.customer.note.placeholder": "メモを入力してください（自動保存）", "label.ai": "AI 返信アシスト", "label.ai.btn.copy": "返信欄にコピー", "label.ai.btn.manualTrigger": "実行", "label.ai.defaultText": "お客様からのメッセージを受け取った際に、AIは最適な返信を自動生成し、こちらに表示します。", "label.add_plugin.button_add_plugin": "ウィジェットを追加", "modal_operator.title": "担当者変更", "modal_operator.description": "こちらで担当者が変更することができます。割り当てられると、担当者に通知されます", "modal_operator.input_placeholder": "検索", "modal_operator.button_cancel": "キャンセル", "modal_operator.button_confirm": "変更する", "modal_operator.label.no_data_matched": "一致する結果はありません", "modal_operator.button.clear": "クリア", "modal_operator.count_matched": "「__search__」の検索結果　__number__件", "chat.detail.latestChat": "経過", "chat.detail.elapsed": "経過", "chat.detail.operator.unassigned.title": "担当者なし", "chat.detail.team.unassigned.title": "なし", "chat.detail.no.assignee": "指定なし", "activity_widget_title": "アクティビティ", "chatbot_activity_title": "チャットボット開始", "livechat_activity_title": "有人チャット開始", "crm_widget_title": "CRM", "see_more": "もっと見る", "no_activity_yet": "活動はありません", "search_placeholder_activity": "読み込んだ内容から検索", "end_message": "メッセージはここまでです", "templates.title": "返信テンプレート", "conversation.operator.assigned.you": "あなたは担当者になりました", "conversation.operator.assigned": "{name}は担当者になりました", "conversation.operator.assigned.unknown": "{name}は担当者の割り当てから解除されました", "conversation.team.assigned": "{name}チームが担当者になりました", "conversation.completed": "チャットが{name}によって終了されました", "conversation_completed": "チャットが{name}によって終了されました", "conversation.completed.system": "会話が自動終了設定によって終了されました", "conversation.new": "ユーザーが有人チャットを開始しました", "conversation.isCreated": "ユーザーが有人チャットを開始しました", "memo.title": "コメント", "memo.length": "文字", "memo.placeholder": "コメントを追加", "edit": "編集する", "delete": "削除する", "cancel": "キャンセル", "delete_message": "本当にこのコメントを削除しますか？", "comment_count": "コメント{count}件", "no_comment": "コメントなし", "back_to_original_page": "会話履歴に戻る", "widget.title": "ウィジェット", "widget.tab.installed": "インストール済み", "widget.tab.all": "すべてのウィジェット", "widget.aiautoreplytitle": "AI自動返信アシスト", "widget.chatbottitle": "チャットボットウィジェット", "widget.activitytitle": "アクティビティウィジェット", "widget.crm.title": "CRM", "widget.form.name": "名前", "widget.form.name.placeholder": "名前を入力してください", "widget.form.name.chatbot.placeholder": "管理用の名称です", "widget.form.name.activity.placeholder": "placeholder-activity", "widget.form.description": "説明", "widget.form.description.placeholder": "管理用の説明です", "widget.form.prompt": "プロンプト", "widget.form.prompt.placeholder": "プロンプトを入力してください", "widget.form.configuration": "自動設定", "widget.form.option.auto.executed": "会話の都度自動で実行", "widget.form.option.manual.executed": "ボタンを押して実行", "widget.form.option.display.executed": "表示のみ", "widget.form.option.update.executed": "表示およびウィジェット上で情報を更新", "widget.cancel": "キャンセル", "widget.save": "保存する", "widget.form.install.title": "ウィジェットのインストール", "widget.install": "インストールする", "widget.no.available": "インストール済みウィジェットはありません。", "widget.uninstall": "アンインストール", "widget.uninstall.title": "この{name}を", "widget.uninstall.confirm": "アンインストールしますか？", "widget.select.widget.to.use": "よりご利用になりたいウィジェットをお選びください。", "edit_user.change_status.confirm_text": "このメンバーの在席ステータスを\n変更してもよろしいですか？", "edit_user.change_status.confirm.button": "変更を確定する", "version": "バージョン", "ai_widget_description": "AI自動返信アシストは、メッセージの返信を効率的かつ効果的にサポートするツールです", "rebot_widget_description": "チャットボットウィジェットはLIBEROと連携を行っている場合、LIBEROのユーザー情報を表示できるツールです", "activity_widget_description": "アクティビティウィジェットは、ユーザーの行動履歴を参照できるツールです。現在のバージョンでは、有人チャット上の過去の問い合わせおよびチャットボットとの会話履歴を参照できます。", "crm_widget_description": "CRMウィジェットは、DECA CRMと連携を行っている場合、DECA CRMの情報を表示できるツールです。", "crm_widget.display_content.title": "表示内容", "crm_widget.display_content.crm_object.title": "有人チャットに表示するCRMのオブジェクトを選択してください", "crm_widget.display_content.crm_fields.title": "有人チャットに表示するCRMの列を選択してください", "crm_widget.display_content.crm_fields.tooltip": "こちらの列は、有人チャットのオートメーションに適用できます。\nCRMからまたはここから列を削除すると、関連するオートメーションが無効になります。", "crm_widget.display_content.crm_identified_field.title": "CRMユーザーを識別するには、CRMの列を選択してください", "crm_widget.display_content.crm_identified_field.placeholder": "選択してください", "crm_widget.display_content.crm_view.title": "有人チャットに表示するCRMのビューを選択してください", "crm_widget.display_content.crm_view.placeholder": "上記のオブジェクトと列を選択してください", "crm_widget.display_content.crm_view.description": "選択したビューが CRM から削除されている場合は、デフォルトのビューが表示されます。", "crm_widget.search_no_match": "一致する検索結果がありません", "crm_widget.removed_column_error": "CRMから列が削除されました", "crm_widget.warning_removed_column_error": "この列はCRMから削除されました", "crm_widget.default_message.empty_data": "CRM上で顧客情報が見つかりません", "crm_widget.default_group_name": "その他", "crm_automation.action_node.warning": "チャットによる割り当ては、他の割り当て設定によって上書きされる場合があります。", "crm_widget.sync_button": "同期", "crm_widget.input.placeholder": "選択してください", "crm.translate.default.title": "CRM顧客情報", "crm.translate.personalInfo": "個人情報", "crm.translate.personalInfo.name": "氏名", "crm.translate.personalInfo.furiganaName": "氏名（ふりがな）", "crm.translate.personalInfo.phone": "電話番号", "crm.translate.personalInfo.email": "メールアドレス", "crm.translate.lineInfo": "LINE情報", "crm.translate.lineInfo.name": "LINEユーザー名", "crm.translate.lineInfo.friendRegistrationDate": "LINE友だち登録日", "crm.translate.lineInfo.friendAddingMethod": "LINE友だち追加経路", "crm.translate.lineInfo.friendBlocked": "LINE友だちブロック", "crm.translate.onlineReceptionInfo": "オンライン接客情報", "crm.translate.onlineReceptionInfo.consultationDate": "オンライン相談実施日", "crm.translate.onlineReceptionInfo.segmentCustomerAnswer": "セグメント（顧客回答）", "crm.translate.onlineReceptionInfo.segmentRounderInput": "セグメント（ラウンダ入力）", "crm.translate.onlineReceptionInfo.genderCustomerAnswer": "性別（顧客回答）", "crm.translate.blank_text": "未記入", "crm.translate.yes_text": "はい", "crm.translate.no_text": "いいえ", "new_message_received": "新着メッセージ"}