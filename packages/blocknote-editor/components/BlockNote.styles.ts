import { themeConfigurations } from '../constants';

// Define a type for the import.meta to avoid any assertions
type ImportMeta = {
  env?: {
    VITE_CLIENT_CDN_PREFIX?: string;
  };
};

const clientCDNPrefix = (import.meta as ImportMeta).env?.VITE_CLIENT_CDN_PREFIX;

/**
 * ShadCN UI styles for BlockNote editor
 * This CSS contains the complete ShadCN UI utility classes and component styles
 * Note: This is generated/compiled CSS - consider moving to a separate .css file for better maintainability
 */
// SonarQube/ESLint: Allow escaped character in CSS template literal for valid CSS class names
export const shadcnStyleCSS = String.raw`
.bn-shadcn *,.bn-shadcn :before,.bn-shadcn :after{box-sizing:border-box;border-width:0;border-style:solid;border-color:#e5e7eb}.bn-shadcn :before,.bn-shadcn :after{--tw-content: ""}.bn-shadcn html,.bn-shadcn :host{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;-o-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji",Segoe UI Symbol,"Noto Color Emoji";font-feature-settings:normal;font-variation-settings:normal;-webkit-tap-highlight-color:transparent}.bn-shadcn body{margin:0;line-height:inherit}.bn-shadcn hr{height:0;color:inherit;border-top-width:1px}.bn-shadcn abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}.bn-shadcn h1,.bn-shadcn h2,.bn-shadcn h3,.bn-shadcn h4,.bn-shadcn h5,.bn-shadcn h6{font-size:inherit;font-weight:inherit}.bn-shadcn a{color:inherit;text-decoration:inherit}.bn-shadcn b,.bn-shadcn strong{font-weight:bolder}.bn-shadcn code,.bn-shadcn kbd,.bn-shadcn samp,.bn-shadcn pre{font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-feature-settings:normal;font-variation-settings:normal;font-size:1em}.bn-shadcn small{font-size:80%}.bn-shadcn sub,.bn-shadcn sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}.bn-shadcn sub{bottom:-.25em}.bn-shadcn sup{top:-.5em}.bn-shadcn table{text-indent:0;border-color:inherit;border-collapse:collapse}.bn-shadcn button,.bn-shadcn input,.bn-shadcn optgroup,.bn-shadcn select,.bn-shadcn textarea{font-family:inherit;font-feature-settings:inherit;font-variation-settings:inherit;font-size:100%;font-weight:inherit;line-height:inherit;letter-spacing:inherit;color:inherit;margin:0;padding:0}.bn-shadcn button,.bn-shadcn select{text-transform:none}.bn-shadcn button,.bn-shadcn input:where([type=button]),.bn-shadcn input:where([type=reset]),.bn-shadcn input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}.bn-shadcn :-moz-focusring{outline:auto}.bn-shadcn :-moz-ui-invalid{box-shadow:none}.bn-shadcn progress{vertical-align:baseline}.bn-shadcn ::-webkit-inner-spin-button,.bn-shadcn ::-webkit-outer-spin-button{height:auto}.bn-shadcn [type=search]{-webkit-appearance:textfield;outline-offset:-2px}.bn-shadcn ::-webkit-search-decoration{-webkit-appearance:none}.bn-shadcn ::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}.bn-shadcn summary{display:list-item}.bn-shadcn blockquote,.bn-shadcn dl,.bn-shadcn dd,.bn-shadcn h1,.bn-shadcn h2,.bn-shadcn h3,.bn-shadcn h4,.bn-shadcn h5,.bn-shadcn h6,.bn-shadcn hr,.bn-shadcn figure,.bn-shadcn p,.bn-shadcn pre{margin:0}.bn-shadcn fieldset{margin:0;padding:0}.bn-shadcn legend{padding:0}.bn-shadcn ol,.bn-shadcn ul,.bn-shadcn menu{list-style:none;margin:0;padding:0}.bn-shadcn dialog{padding:0}.bn-shadcn textarea{resize:vertical}.bn-shadcn input::-moz-placeholder,.bn-shadcn textarea::-moz-placeholder{opacity:1;color:#9ca3af}.bn-shadcn input::placeholder,.bn-shadcn textarea::placeholder{opacity:1;color:#9ca3af}.bn-shadcn button,.bn-shadcn [role=button]{cursor:pointer}.bn-shadcn :disabled{cursor:default}.bn-shadcn img,.bn-shadcn svg,.bn-shadcn video,.bn-shadcn canvas,.bn-shadcn audio,.bn-shadcn iframe,.bn-shadcn embed,.bn-shadcn object{display:block;vertical-align:middle}.bn-shadcn img,.bn-shadcn video{max-width:100%;height:auto}.bn-shadcn [hidden]{display:none}.bn-block-outer{line-height:1.5;transition:margin .2s}.bn-block{display:flex;flex-direction:column}.bn-block-content{padding:3px 0;display:flex;transition:font-size .2s;width:100%}.bn-block-content.ProseMirror-selectednode>*,.ProseMirror-selectednode>.bn-block-content>*{border-radius:4px;outline:4px solid rgb(100,160,255)}.bn-block-content:before{content:"";margin-right:0;transition:all .2s;height:0;overflow:visible}.bn-inline-content{width:100%}.bn-block-group .bn-block-group{margin-left:24px}.bn-block-group .bn-block-group>.bn-block-outer{position:relative}.bn-block-group .bn-block-group>.bn-block-outer:not([data-prev-depth-changed]):before{content:" ";display:inline;position:absolute;left:-20px;height:100%;transition:all .2s .1s}.bn-block-group .bn-block-group>.bn-block-outer[data-prev-depth-change="-2"]:before{height:0}.bn-inline-content code{font-family:monospace}[data-prev-depth-change="1"]{--x: 1}[data-prev-depth-change="2"]{--x: 2}[data-prev-depth-change="3"]{--x: 3}[data-prev-depth-change="4"]{--x: 4}[data-prev-depth-change="5"]{--x: 5}[data-prev-depth-change="-1"]{--x: -1}[data-prev-depth-change="-2"]{--x: -2}[data-prev-depth-change="-3"]{--x: -3}[data-prev-depth-change="-4"]{--x: -4}[data-prev-depth-change="-5"]{--x: -5}.bn-block-outer[data-prev-depth-change]{margin-left:calc(10px * var(--x))}.bn-block-outer[data-prev-depth-change] .bn-block-outer[data-prev-depth-change]{margin-left:0}[data-content-type=heading]{--level: 3em}[data-content-type=heading][data-level="2"]{--level: 2em}[data-content-type=heading][data-level="3"]{--level: 1.3em}[data-content-type=heading][data-level="4"]{--level: 1em}[data-content-type=heading][data-level="5"]{--level: .9em}[data-content-type=heading][data-level="6"]{--level: .8em}[data-prev-level="1"]{--prev-level: 3em}[data-prev-level="2"]{--prev-level: 2em}[data-prev-level="3"]{--prev-level: 1.3em}[data-prev-level="4"]{--prev-level: 1em}[data-prev-level="5"]{--prev-level: .9em}[data-prev-level="6"]{--prev-level: .8em}.bn-block-outer[data-prev-type=heading]>.bn-block>.bn-block-content{font-size:var(--prev-level);font-weight:700}.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=heading],.bn-block-outer:not([data-prev-type])>.bn-block>div[data-type=modification]>div[data-type=modification]>.bn-block-content[data-content-type=heading]{font-size:var(--level);font-weight:700}[data-content-type=quote] blockquote{border-left:2px solid rgb(125,121,122);color:#7d797a;margin:0;padding-left:1em}.bn-block-content:before{margin-right:0;content:""}.bn-block-content[data-content-type=numberedListItem]:before{display:flex;justify-content:center;min-width:24px;padding-right:4px}[data-content-type=numberedListItem]{--index: attr(data-index)}[data-prev-type=numberedListItem]{--prev-index: attr(data-prev-index)}.bn-block-outer[data-prev-type=numberedListItem]:not([data-prev-index=none])>.bn-block>.bn-block-content:before{content:var(--prev-index) "."}.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=numberedListItem]:before,.bn-block-outer:not([data-prev-type])>.bn-block>div[data-type=modification]>.bn-block-content[data-content-type=numberedListItem]:before{content:var(--index) "."}.bn-block-content[data-content-type=bulletListItem]:before{display:flex;justify-content:center;min-width:24px;padding-right:4px}.bn-block-content[data-content-type=checkListItem]>div{display:flex;width:100%}.bn-block-content[data-content-type=checkListItem]>div>div{display:flex;justify-content:center;min-width:24px;padding-right:4px}.bn-block-content[data-content-type=checkListItem]>div>div>input{margin:0;cursor:pointer}.bn-block-content[data-content-type=checkListItem][data-checked=true] .bn-inline-content{text-decoration:line-through}.bn-block-content[data-text-alignment=center]{justify-content:center}.bn-block-content[data-text-alignment=right]{justify-content:flex-end}.bn-block-content:has(.bn-toggle-wrapper)>div{display:flex;flex-direction:column;gap:4px}.bn-block:has(>.bn-block-content>div>.bn-toggle-wrapper[data-show-children=false])>.bn-block-group,.bn-block:has(>.react-renderer>.bn-block-content>div>.bn-toggle-wrapper[data-show-children=false])>.bn-block-group{display:none}.bn-toggle-wrapper{display:flex;align-items:center}.bn-toggle-button{color:var(--bn-colors-editor-text);padding:3px}.bn-toggle-button>svg{width:18px;height:18px}.bn-toggle-wrapper[data-show-children=true] .bn-toggle-button{transform:rotate(90deg)}.bn-toggle-add-block-button{font-size:16px;color:var(--bn-colors-side-menu);font-weight:400;margin-left:22px;padding-inline:2px;width:-moz-fit-content;width:fit-content}.bn-toggle-button,.bn-toggle-add-block-button{background:none;border:none;border-radius:var(--bn-border-radius-small);cursor:pointer;display:flex;-webkit-user-select:none;-moz-user-select:none;user-select:none}.bn-toggle-button:hover,.bn-toggle-add-block-button:hover{background-color:var(--bn-colors-hovered-background)}.bn-block-outer[data-prev-type=bulletListItem]>.bn-block>.bn-block-content:before{content:"•"}.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=bulletListItem]:before,.bn-block-outer:not([data-prev-type])>.bn-block>div[data-type=modification]>.bn-block-content[data-content-type=bulletListItem]:before{content:"•"}[data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer[data-prev-type=bulletListItem]>.bn-block>.bn-block-content:before{content:"◦"}[data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=bulletListItem]:before,[data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer:not([data-prev-type])>.bn-block>div[data-type=modification]>.bn-block-content[data-content-type=bulletListItem]:before{content:"◦"}[data-content-type=bulletListItem]~.bn-block-group [data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer[data-prev-type=bulletListItem]>.bn-block>.bn-block-content:before{content:"▪"}[data-content-type=bulletListItem]~.bn-block-group [data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=bulletListItem]:before,[data-content-type=bulletListItem]~.bn-block-group [data-content-type=bulletListItem]~.bn-block-group>.bn-block-outer:not([data-prev-type])>.bn-block>div[data-type=modification]>.bn-block-content[data-content-type=bulletListItem]:before{content:"▪"}.bn-block-content[data-content-type=codeBlock]{position:relative;background-color:#161616;color:#fff;border-radius:8px}.bn-block-content[data-content-type=codeBlock]>pre{white-space:pre;overflow-x:auto;margin:0;width:100%;-moz-tab-size:2;-o-tab-size:2;tab-size:2;padding:24px}.bn-block-content[data-content-type=codeBlock]>div{outline:none!important}.bn-block-content[data-content-type=codeBlock]>div>select{outline:none!important;-webkit-appearance:none;-moz-appearance:none;appearance:none;-webkit-user-select:none;-moz-user-select:none;user-select:none;border:none;cursor:pointer;background-color:transparent;position:absolute;top:8px;left:18px;font-size:.8em;color:#fff;opacity:0;transition:opacity .3s;transition-delay:1s}.bn-block-content[data-content-type=codeBlock]>div>select>option{color:#000}.bn-block-content[data-content-type=codeBlock]:hover>div>select,.bn-block-content[data-content-type=codeBlock]>div>select:focus{opacity:.5;transition-delay:.1s}.bn-block-content[data-content-type=pageBreak]>div{width:100%;height:0;border-top:dotted rgb(125,121,122) 2px;margin-block:11px}@media print{.bn-block-content[data-content-type=pageBreak]>div{page-break-after:always}}[data-file-block] .bn-file-block-content-wrapper{cursor:pointer;display:flex;flex-direction:column;-webkit-user-select:none;-moz-user-select:none;user-select:none}[data-file-block] .bn-file-block-content-wrapper:has(.bn-add-file-button),[data-file-block] .bn-file-block-content-wrapper:has(.bn-file-name-with-icon){width:100%}[data-file-block] .bn-add-file-button{align-items:center;background-color:#f2f1ee;border-radius:4px;color:#7d797a;display:flex;gap:10px;padding:12px}.bn-editor[contenteditable=true] [data-file-block] .bn-add-file-button:hover,[data-file-block] .bn-file-name-with-icon:hover,.ProseMirror-selectednode .bn-file-name-with-icon{background-color:#e1e1e1}[data-file-block] .bn-add-file-button-icon,[data-file-block] .bn-file-icon{width:24px;height:24px}[data-file-block] .bn-add-file-button-text{font-size:.9rem}[data-file-block] .bn-file-name-with-icon{border-radius:4px;display:flex;gap:4px;padding:4px}[data-file-block] .bn-file-caption{font-size:.8em;padding-block:4px;word-break:break-word}[data-file-block] .bn-file-caption:empty{padding-block:0}[data-file-block] .bn-resize-handle{position:absolute;width:8px;height:30px;background-color:#000;border:1px solid white;border-radius:4px;cursor:ew-resize}[data-file-block] .bn-visual-media-wrapper{display:flex;align-items:center;position:relative;max-width:100%}[data-file-block] .bn-visual-media{border-radius:4px;width:100%}[data-content-type=audio]>.bn-file-block-content-wrapper,.bn-audio{width:100%}.bn-inline-content:has(>.ProseMirror-trailingBreak:only-child):before{pointer-events:none;height:0;position:absolute;font-style:italic}[data-text-color=gray]{color:#9b9a97}[data-text-color=brown]{color:#64473a}[data-text-color=red]{color:#e03e3e}[data-text-color=orange]{color:#d9730d}[data-text-color=yellow]{color:#dfab01}[data-text-color=green]{color:#4d6461}[data-text-color=blue]{color:#0b6e99}[data-text-color=purple]{color:#6940a5}[data-text-color=pink]{color:#ad1a72}[data-background-color=gray]{background-color:#ebeced}[data-background-color=brown]{background-color:#e9e5e3}[data-background-color=red]{background-color:#fbe4e4}[data-background-color=orange]{background-color:#f6e9d9}[data-background-color=yellow]{background-color:#fbf3db}[data-background-color=green]{background-color:#ddedea}[data-background-color=blue]{background-color:#ddebf1}[data-background-color=purple]{background-color:#eae4f2}[data-background-color=pink]{background-color:#f4dfeb}[data-text-alignment=left]{justify-content:flex-start!important;text-align:left!important}[data-text-alignment=center]{justify-content:center!important;text-align:center!important}[data-text-alignment=right]{justify-content:flex-end!important;text-align:right!important}[data-text-alignment=justify]{justify-content:flex-start!important;text-align:justify!important}.bn-block-column-list{display:flex;flex-direction:row}.bn-block-column{flex:1;padding:12px 20px;overflow-x:auto}.bn-block-column:first-child{padding-left:0}.bn-block-column:last-child{padding-right:0}.bn-thread-mark:not([data-orphan=true]){background:#ffc80026}.bn-thread-mark .bn-thread-mark-selected{background:#ffc80040}.ProseMirror .tableWrapper{overflow-x:auto}.ProseMirror table{border-collapse:collapse;table-layout:fixed;width:100%;overflow:hidden}.ProseMirror td,.ProseMirror th{vertical-align:top;box-sizing:border-box;position:relative}.ProseMirror td:not([data-colwidth]):not(.column-resize-dragging),.ProseMirror th:not([data-colwidth]):not(.column-resize-dragging){min-width:var(--default-cell-min-width)}.ProseMirror .column-resize-handle{position:absolute;right:-2px;top:0;bottom:0;width:4px;z-index:20;background-color:#adf;pointer-events:none}.ProseMirror.resize-cursor{cursor:ew-resize;cursor:col-resize}.ProseMirror .selectedCell:after{z-index:2;position:absolute;content:"";left:0;right:0;top:0;bottom:0;background:#c8c8ff66;pointer-events:none}.bn-editor{outline:none;padding-inline:54px;--N800: #172b4d;--N40: #dfe1e6}.bn-comment-editor{width:100%;padding:0}.bn-comment-editor .bn-editor{padding:0}.bn-root{box-sizing:border-box}.bn-root *,.bn-root *:before,.bn-root *:after{box-sizing:inherit}.bn-default-styles p,.bn-default-styles h1,.bn-default-styles h2,.bn-default-styles h3,.bn-default-styles h4,.bn-default-styles h5,.bn-default-styles h6,.bn-default-styles li{margin:0;padding:0;font-size:inherit;min-width:2px!important}.bn-default-styles{font-size:16px;font-weight:400;font-family:Inter,SF Pro Display,-apple-system,BlinkMacSystemFont,Open Sans,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.bn-table-drop-cursor{position:absolute;z-index:20;background-color:#adf;pointer-events:none}.bn-drag-preview{position:absolute;top:0;left:0;padding:10px;opacity:.001}.bn-editor .bn-collaboration-cursor__base{position:relative}.bn-editor .bn-collaboration-cursor__base .bn-collaboration-cursor__caret{position:absolute;width:2px;top:1px;bottom:-2px;left:-1px}.bn-editor .bn-collaboration-cursor__base .bn-collaboration-cursor__label{pointer-events:none;border-radius:0 1.5px 1.5px 0;font-size:12px;font-style:normal;font-weight:600;line-height:normal;left:0;overflow:hidden;position:absolute;white-space:nowrap;-webkit-user-select:none;-moz-user-select:none;user-select:none;color:transparent;max-height:5px;max-width:4px;padding:0;top:-1px;transition:all .2s}.bn-editor .bn-collaboration-cursor__base[data-active] .bn-collaboration-cursor__label{color:#0d0d0d;max-height:1.1rem;max-width:20rem;padding:.1rem .3rem;top:-17px;left:0;border-radius:3px 3px 3px 0;transition:all .2s}.bn-editor [data-content-type=table] .tableWrapper{position:relative;top:-16px;left:-16px;min-width:calc(100% + 16px);padding-bottom:16px;overflow-y:hidden}.bn-editor [data-content-type=table] .tableWrapper-inner{padding:16px}.bn-editor [data-content-type=table] table{width:auto!important;word-break:break-word}.bn-editor [data-content-type=table] th,.bn-editor [data-content-type=table] td{border:1px solid #ddd;padding:5px 10px}.bn-editor [data-content-type=table] th{font-weight:700;text-align:left}.ProseMirror td,.ProseMirror th{min-width:auto!important}.ProseMirror td:not([colwidth]):not(.column-resize-dragging),.ProseMirror th:not([colwidth]):not(.column-resize-dragging){min-width:var(--default-cell-min-width)!important}.prosemirror-dropcursor-block{transition-property:top,bottom;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.prosemirror-dropcursor-vertical{transition-property:left,right}[data-show-selection]{background-color:highlight;padding:2px 0}.bn-container{--bn-colors-editor-text: #3f3f3f;--bn-colors-editor-background: #ffffff;--bn-colors-menu-text: #3f3f3f;--bn-colors-menu-background: #ffffff;--bn-colors-tooltip-text: #3f3f3f;--bn-colors-tooltip-background: #efefef;--bn-colors-hovered-text: #3f3f3f;--bn-colors-hovered-background: #efefef;--bn-colors-selected-text: #ffffff;--bn-colors-selected-background: #3f3f3f;--bn-colors-disabled-text: #afafaf;--bn-colors-disabled-background: #efefef;--bn-colors-shadow: #cfcfcf;--bn-colors-border: #efefef;--bn-colors-side-menu: #cfcfcf;--bn-colors-highlights-gray-text: #9b9a97;--bn-colors-highlights-gray-background: #ebeced;--bn-colors-highlights-brown-text: #64473a;--bn-colors-highlights-brown-background: #e9e5e3;--bn-colors-highlights-red-text: #e03e3e;--bn-colors-highlights-red-background: #fbe4e4;--bn-colors-highlights-orange-text: #d9730d;--bn-colors-highlights-orange-background: #f6e9d9;--bn-colors-highlights-yellow-text: #dfab01;--bn-colors-highlights-yellow-background: #fbf3db;--bn-colors-highlights-green-text: #4d6461;--bn-colors-highlights-green-background: #ddedea;--bn-colors-highlights-blue-text: #0b6e99;--bn-colors-highlights-blue-background: #ddebf1;--bn-colors-highlights-purple-text: #6940a5;--bn-colors-highlights-purple-background: #eae4f2;--bn-colors-highlights-pink-text: #ad1a72;--bn-colors-highlights-pink-background: #f4dfeb;--bn-font-family: "Inter", "SF Pro Display", -apple-system, BlinkMacSystemFont, "Open Sans", "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;--bn-border-radius: 6px;--bn-shadow-medium: 0 4px 12px var(--bn-colors-shadow);--bn-shadow-light: 0 2px 6px var(--bn-colors-border);--bn-border: 1px solid var(--bn-colors-border);--bn-border-radius-small: max(var(--bn-border-radius) - 2px, 1px);--bn-border-radius-medium: var(--bn-border-radius);--bn-border-radius-large: max(var(--bn-border-radius) + 2px, 1px)}.bn-container[data-color-scheme=dark]{--bn-colors-editor-text: #cfcfcf;--bn-colors-editor-background: #1f1f1f;--bn-colors-menu-text: #cfcfcf;--bn-colors-menu-background: #1f1f1f;--bn-colors-tooltip-text: #cfcfcf;--bn-colors-tooltip-background: #161616;--bn-colors-hovered-text: #cfcfcf;--bn-colors-hovered-background: #161616;--bn-colors-selected-text: #cfcfcf;--bn-colors-selected-background: #0f0f0f;--bn-colors-disabled-text: #3f3f3f;--bn-colors-disabled-background: #161616;--bn-colors-shadow: #0f0f0f;--bn-colors-border: #161616;--bn-colors-side-menu: #7f7f7f;--bn-colors-highlights-gray-text: #bebdb8;--bn-colors-highlights-gray-background: #9b9a97;--bn-colors-highlights-brown-text: #8e6552;--bn-colors-highlights-brown-background: #64473a;--bn-colors-highlights-red-text: #ec4040;--bn-colors-highlights-red-background: #be3434;--bn-colors-highlights-orange-text: #e3790d;--bn-colors-highlights-orange-background: #b7600a;--bn-colors-highlights-yellow-text: #dfab01;--bn-colors-highlights-yellow-background: #b58b00;--bn-colors-highlights-green-text: #6b8b87;--bn-colors-highlights-green-background: #4d6461;--bn-colors-highlights-blue-text: #0e87bc;--bn-colors-highlights-blue-background: #0b6e99;--bn-colors-highlights-purple-text: #8552d7;--bn-colors-highlights-purple-background: #6940a5;--bn-colors-highlights-pink-text: #da208f;--bn-colors-highlights-pink-background: #ad1a72}.bn-container{font-family:var(--bn-font-family)}.bn-editor{background-color:var(--bn-colors-editor-background);border-radius:var(--bn-border-radius-large);color:var(--bn-colors-editor-text)}.bn-react-node-view-renderer{display:flex;flex-direction:column;width:100%}.bn-block-group .bn-block:not(:has(.bn-toggle-wrapper)) .bn-block-group .bn-block-outer:not([data-prev-depth-changed]):before{border-left:1px solid var(--bn-colors-side-menu)}.bn-inline-content:has(>.ProseMirror-trailingBreak):before{color:var(--bn-colors-side-menu)}.bn-container .bn-color-icon{align-items:center;border:var(--bn-border);border-radius:var(--bn-border-radius-small);display:flex;justify-content:center}.bn-error-text{color:red;font-size:12px}[data-text-color=gray]{color:var(--bn-colors-highlights-gray-text)}[data-text-color=brown]{color:var(--bn-colors-highlights-brown-text)}[data-text-color=red]{color:var(--bn-colors-highlights-red-text)}[data-text-color=orange]{color:var(--bn-colors-highlights-orange-text)}[data-text-color=yellow]{color:var(--bn-colors-highlights-yellow-text)}[data-text-color=green]{color:var(--bn-colors-highlights-green-text)}[data-text-color=blue]{color:var(--bn-colors-highlights-blue-text)}[data-text-color=purple]{color:var(--bn-colors-highlights-purple-text)}[data-text-color=pink]{color:var(--bn-colors-highlights-pink-text)}[data-background-color=gray]{background-color:var(--bn-colors-highlights-gray-background)}[data-background-color=brown]{background-color:var(--bn-colors-highlights-brown-background)}[data-background-color=red]{background-color:var(--bn-colors-highlights-red-background)}[data-background-color=orange]{background-color:var(--bn-colors-highlights-orange-background)}[data-background-color=yellow]{background-color:var(--bn-colors-highlights-yellow-background)}[data-background-color=green]{background-color:var(--bn-colors-highlights-green-background)}[data-background-color=blue]{background-color:var(--bn-colors-highlights-blue-background)}[data-background-color=purple]{background-color:var(--bn-colors-highlights-purple-background)}[data-background-color=pink]{background-color:var(--bn-colors-highlights-pink-background)}.bn-side-menu{height:30px}.bn-side-menu[data-block-type=heading][data-level="1"]{height:78px}.bn-side-menu[data-block-type=heading][data-level="2"]{height:54px}.bn-side-menu[data-block-type=heading][data-level="3"]{height:37px}.bn-side-menu[data-block-type=file]{height:38px}.bn-side-menu[data-block-type=audio]{height:60px}.bn-side-menu[data-url=false]{height:54px}.bn-threads-sidebar{border-radius:var(--bn-border-radius-medium);display:flex;flex-direction:column;gap:10px;overflow:auto}.bn-thread-expand-prompt .mantine-Text-root,.bn-thread .bn-header-text{color:var(--bn-colors-menu-text)}.bn-threads-sidebar .bn-thread .bn-editor{background-color:transparent}.bn-threads-sidebar .bn-thread.selected{background-color:#f5f9fd;border:2px solid #c2dcf8}.dark .bn-threads-sidebar .bn-thread.selected{background-color:#20242a;border:2px solid #23405b}.bn-container .bn-absolute{position:absolute}.bn-container .bn-relative{position:relative}.bn-container .bn-left-2{left:.5rem}.bn-container .bn-right-0{right:0}.bn-container .bn-top-0{top:0}.bn-container .bn-z-10{z-index:10}.bn-container .bn-z-50{z-index:50}.bn-container .bn--mx-1{margin-left:-.25rem;margin-right:-.25rem}.bn-container .bn-my-1{margin-top:.25rem;margin-bottom:.25rem}.bn-container .bn-ml-1{margin-left:.25rem}.bn-container .bn-ml-auto{margin-left:auto}.bn-container .bn-mt-1{margin-top:.25rem}.bn-container .bn-mt-2{margin-top:.5rem}.bn-container .bn-flex{display:flex}.bn-container .bn-inline-flex{display:inline-flex}.bn-container .bn-hidden{display:none}.bn-container .bn-size-7{width:1.75rem;height:1.75rem}.bn-container .bn-h-10{height:2.5rem}.bn-container .bn-h-11{height:2.75rem}.bn-container .bn-h-2{height:.5rem}.bn-container .bn-h-3{height:.75rem}.bn-container .bn-h-3\.5{height:.875rem}.bn-container .bn-h-4{height:1rem}.bn-container .bn-h-6{height:1.5rem}.bn-container .bn-h-7{height:1.75rem}.bn-container .bn-h-9{height:2.25rem}.bn-container .bn-h-[var(--radix-select-trigger-height)]{height:var(--radix-select-trigger-height)}.bn-container .bn-h-auto{height:auto}.bn-container .bn-h-fit{height:-moz-fit-content;height:fit-content}.bn-container .bn-h-full{height:100%}.bn-container .bn-h-px{height:1px}.bn-container .bn-max-h-96{max-height:24rem}.bn-container .bn-w-10{width:2.5rem}.bn-container .bn-w-2{width:.5rem}.bn-container .bn-w-3\.5{width:.875rem}.bn-container .bn-w-32{width:8rem}.bn-container .bn-w-4{width:1rem}.bn-container .bn-w-72{width:18rem}.bn-container .bn-w-80{width:20rem}.bn-container .bn-w-[300px]{width:300px}.bn-container .bn-w-fit{width:-moz-fit-content;width:fit-content}.bn-container .bn-w-full{width:100%}.bn-container .bn-min-w-6{min-width:1.5rem}.bn-container .bn-min-w-[8rem]{min-width:8rem}.bn-container .bn-min-w-[var(--radix-select-trigger-width)]{min-width:var(--radix-select-trigger-width)}.bn-container .bn-max-w-none{max-width:none}.bn-container .bn-flex-1{flex:1 1 0%}@keyframes bn-pulse{50%{opacity:.5}}.bn-container .bn-animate-pulse{animation:bn-pulse 2s cubic-bezier(.4,0,.6,1) infinite}@keyframes bn-spin{to{transform:rotate(360deg)}}.bn-container .bn-animate-spin{animation:bn-spin 1s linear infinite}.bn-container .bn-cursor-default{cursor:default}.bn-container .bn-cursor-pointer{cursor:pointer}.bn-container .bn-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.bn-container .bn-flex-row{flex-direction:row}.bn-container .bn-flex-col{flex-direction:column}.bn-container .bn-flex-wrap{flex-wrap:wrap}.bn-container .bn-flex-nowrap{flex-wrap:nowrap}.bn-container .bn-items-start{align-items:flex-start}.bn-container .bn-items-center{align-items:center}.bn-container .bn-justify-center{justify-content:center}.bn-container .bn-justify-between{justify-content:space-between}.bn-container .bn-gap-1{gap:.25rem}.bn-container .bn-gap-2{gap:.5rem}.bn-container .bn-gap-3{gap:.75rem}.bn-container .bn-gap-4{gap:1rem}.bn-container .bn-gap-6{gap:1.5rem}.bn-container :is(.bn-space-y-1\.5>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(.375rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.375rem * var(--tw-space-y-reverse))}.bn-container :is(.bn-space-y-2>:not([hidden])~:not([hidden])){--tw-space-y-reverse: 0;margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));margin-bottom:calc(.5rem * var(--tw-space-y-reverse))}.bn-container .bn-overflow-auto{overflow:auto}.bn-container .bn-overflow-hidden{overflow:hidden}.bn-container .bn-whitespace-nowrap{white-space:nowrap}.bn-container .bn-whitespace-pre-wrap{white-space:pre-wrap}.bn-container .bn-rounded-full{border-radius:9999px}.bn-container .bn-rounded-lg{border-radius:var(--radius)}.bn-container .bn-rounded-md{border-radius:calc(var(--radius) - 2px)}.bn-container .bn-rounded-sm{border-radius:calc(var(--radius) - 4px)}.bn-container .bn-border{border-width:1px}.bn-container .bn-border-b{border-bottom-width:1px}.bn-container .bn-border-none{border-style:none}.bn-container .bn-border-input{border-color:hsl(var(--input))}.bn-container .bn-border-transparent{border-color:transparent}.bn-container .bn-bg-accent{background-color:hsl(var(--accent))}.bn-container .bn-bg-background{background-color:hsl(var(--background))}.bn-container .bn-bg-card{background-color:hsl(var(--card))}.bn-container .bn-bg-destructive{background-color:hsl(var(--destructive))}.bn-container .bn-bg-muted{background-color:hsl(var(--muted))}.bn-container .bn-bg-neutral-400{--tw-bg-opacity: 1;background-color:rgb(163 163 163 / var(--tw-bg-opacity, 1))}.bn-container .bn-bg-popover{background-color:hsl(var(--popover))}.bn-container .bn-bg-primary{background-color:hsl(var(--primary))}.bn-container .bn-bg-secondary{background-color:hsl(var(--secondary))}.bn-container .bn-bg-transparent{background-color:transparent}.bn-container .bn-fill-current{fill:currentColor}.bn-container .bn-p-0{padding:0}.bn-container .bn-p-1{padding:.25rem}.bn-container .bn-p-2{padding:.5rem}.bn-container .bn-p-3{padding:.75rem}.bn-container .bn-p-4{padding:1rem}.bn-container .bn-p-6{padding:1.5rem}.bn-container .bn-px-2{padding-left:.5rem;padding-right:.5rem}.bn-container .bn-px-2\.5{padding-left:.625rem;padding-right:.625rem}.bn-container .bn-px-3{padding-left:.75rem;padding-right:.75rem}.bn-container .bn-px-4{padding-left:1rem;padding-right:1rem}.bn-container .bn-px-5{padding-left:1.25rem;padding-right:1.25rem}.bn-container .bn-px-8{padding-left:2rem;padding-right:2rem}.bn-container .bn-py-0\.5{padding-top:.125rem;padding-bottom:.125rem}.bn-container .bn-py-1{padding-top:.25rem;padding-bottom:.25rem}.bn-container .bn-py-1\.5{padding-top:.375rem;padding-bottom:.375rem}.bn-container .bn-py-2{padding-top:.5rem;padding-bottom:.5rem}.bn-container .bn-pl-8{padding-left:2rem}.bn-container .bn-pr-2{padding-right:.5rem}.bn-container .bn-pt-0{padding-top:0}.bn-container .bn-pt-4{padding-top:1rem}.bn-container .bn-text-2xl{font-size:1.5rem;line-height:2rem}.bn-container .bn-text-base{font-size:1rem;line-height:1.5rem}.bn-container .bn-text-sm{font-size:.875rem;line-height:1.25rem}.bn-container .bn-text-xs{font-size:.75rem;line-height:1rem}.bn-container .bn-font-bold{font-weight:700}.bn-container .bn-font-medium{font-weight:500}.bn-container .bn-font-semibold{font-weight:600}.bn-container .bn-italic{font-style:italic}.bn-container .bn-leading-none{line-height:1}.bn-container .bn-tracking-tight{letter-spacing:-.025em}.bn-container .bn-tracking-widest{letter-spacing:.1em}.bn-container .bn-text-accent-foreground{color:hsl(var(--accent-foreground))}.bn-container .bn-text-card-foreground{color:hsl(var(--card-foreground))}.bn-container .bn-text-destructive{color:hsl(var(--destructive))}.bn-container .bn-text-destructive-foreground{color:hsl(var(--destructive-foreground))}.bn-container .bn-text-foreground{color:hsl(var(--foreground))}.bn-container .bn-text-gray-400{--tw-text-opacity: 1;color:rgb(156 163 175 / var(--tw-text-opacity, 1))}.bn-container .bn-text-muted-foreground{color:hsl(var(--muted-foreground))}.bn-container .bn-text-popover-foreground{color:hsl(var(--popover-foreground))}.bn-container .bn-text-primary{color:hsl(var(--primary))}.bn-container .bn-text-primary-foreground{color:hsl(var(--primary-foreground))}.bn-container .bn-text-secondary-foreground{color:hsl(var(--secondary-foreground))}.bn-container .bn-underline-offset-4{text-underline-offset:4px}.bn-container .bn-opacity-50{opacity:.5}.bn-container .bn-opacity-60{opacity:.6}.bn-container .bn-shadow-lg{--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / .1), 0 4px 6px -4px rgb(0 0 0 / .1);--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container .bn-shadow-md{--tw-shadow: 0 4px 6px -1px rgb(0 0 0 / .1), 0 2px 4px -2px rgb(0 0 0 / .1);--tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container .bn-shadow-none{--tw-shadow: 0 0 #0000;--tw-shadow-colored: 0 0 #0000;box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container .bn-shadow-sm{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container .bn-outline-none{outline:2px solid transparent;outline-offset:2px}.bn-container .bn-ring-offset-background{--tw-ring-offset-color: hsl(var(--background))}.bn-container .bn-transition-all{transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}.bn-container .bn-transition-colors{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s}@keyframes enter{0%{opacity:var(--tw-enter-opacity, 1);transform:translate3d(var(--tw-enter-translate-x, 0),var(--tw-enter-translate-y, 0),0) scale3d(var(--tw-enter-scale, 1),var(--tw-enter-scale, 1),var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0))}}@keyframes exit{to{opacity:var(--tw-exit-opacity, 1);transform:translate3d(var(--tw-exit-translate-x, 0),var(--tw-exit-translate-y, 0),0) scale3d(var(--tw-exit-scale, 1),var(--tw-exit-scale, 1),var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0))}}.bn-container .bn-animate-in{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.bn-container .bn-fade-in-0{--tw-enter-opacity: 0}.bn-container .bn-zoom-in-95{--tw-enter-scale: .95}.bn-shadcn{--background: 0 0% 100%;--foreground: 222.2 84% 4.9%;--card: 0 0% 100%;--card-foreground: 222.2 84% 4.9%;--popover: 0 0% 100%;--popover-foreground: 222.2 84% 4.9%;--primary: 222.2 47.4% 11.2%;--primary-foreground: 210 40% 98%;--secondary: 210 40% 96.1%;--secondary-foreground: 222.2 47.4% 11.2%;--muted: 210 40% 96.1%;--muted-foreground: 215.4 16.3% 46.9%;--accent: 210 40% 96.1%;--accent-foreground: 222.2 47.4% 11.2%;--destructive: 0 84.2% 60.2%;--destructive-foreground: 210 40% 98%;--border: 214.3 31.8% 91.4%;--input: 214.3 31.8% 91.4%;--ring: 222.2 84% 4.9%;--radius: .5rem}.bn-shadcn.dark{--background: 222.2 84% 4.9%;--foreground: 210 40% 98%;--card: 222.2 84% 4.9%;--card-foreground: 210 40% 98%;--popover: 222.2 84% 4.9%;--popover-foreground: 210 40% 98%;--primary: 210 40% 98%;--primary-foreground: 222.2 47.4% 11.2%;--secondary: 217.2 32.6% 17.5%;--secondary-foreground: 210 40% 98%;--muted: 217.2 32.6% 17.5%;--muted-foreground: 215 20.2% 65.1%;--accent: 217.2 32.6% 17.5%;--accent-foreground: 210 40% 98%;--destructive: 0 62.8% 30.6%;--destructive-foreground: 210 40% 98%;--border: 217.2 32.6% 17.5%;--input: 217.2 32.6% 17.5%;--ring: 212.7 26.8% 83.9%}.bn-shadcn *{border-color:hsl(var(--border))}.bn-shadcn .bn-editor{background-color:hsl(var(--background));color:hsl(var(--foreground))}.bn-shadcn .bn-editor a{color:revert;-webkit-text-decoration:revert;text-decoration:revert}.bn-shadcn .bn-editor:focus-visible{outline:none}.bn-shadcn .bn-side-menu{align-items:center;display:flex;justify-content:center}.bn-shadcn .bn-side-menu .bn-button{padding:0;height:24px}.bn-shadcn .bn-select{max-height:var(--radix-select-content-available-height)}.bn-shadcn .bn-menu-dropdown{max-height:var(--radix-dropdown-menu-content-available-height)}.bn-shadcn .bn-color-picker-dropdown{overflow:auto}.bn-shadcn .bn-suggestion-menu{height:-moz-fit-content;height:fit-content;max-height:inherit}.bn-shadcn .bn-suggestion-menu-item[aria-selected=true],.bn-shadcn .bn-suggestion-menu-item:hover{background-color:hsl(var(--accent))}.bn-shadcn .bn-grid-suggestion-menu{background:var(--bn-colors-menu-background);border-radius:var(--bn-border-radius-large);box-shadow:var(--bn-shadow-medium);display:grid;gap:7px;height:-moz-fit-content;height:fit-content;justify-items:center;max-height:inherit;overflow-y:auto;padding:20px}.bn-shadcn .bn-grid-suggestion-menu-item{align-items:center;border-radius:var(--bn-border-radius-large);cursor:pointer;display:flex;font-size:24px;height:32px;justify-content:center;margin:2px;padding:4px;width:32px}.bn-shadcn .bn-grid-suggestion-menu-item[aria-selected=true],.bn-shadcn .bn-grid-suggestion-menu-item:hover{background-color:var(--bn-colors-hovered-background)}.bn-shadcn .bn-grid-suggestion-menu-empty-item,.bn-shadcn .bn-grid-suggestion-menu-loader{align-items:center;color:var(--bn-colors-menu-text);display:flex;font-size:14px;font-weight:500;height:32px;justify-content:center}.bn-shadcn .bn-grid-suggestion-menu-loader span{background-color:hsl(var(--accent))}.bn-shadcn .bn-extend-button-add-remove-columns{cursor:col-resize}.bn-shadcn .bn-extend-button-add-remove-rows{cursor:row-resize}.bn-shadcn .bn-toolbar{overflow-x:auto;max-width:100vw}.bn-shadcn .bn-comment-actions-wrapper{display:flex;justify-content:flex-end}.bn-shadcn .bn-table-cell-handle{padding:0 4px;height:12px}.bn-shadcn .bn-thread .bn-resolved-text{font-size:14px;font-style:italic}.bn-shadcn .bn-combobox-error{color:var(--bn-colors-highlights-red-background);font-weight:700}.bn-container .file:bn-border-0::file-selector-button{border-width:0px}.bn-container .file:bn-bg-transparent::file-selector-button{background-color:transparent}.bn-container .file:bn-text-sm::file-selector-button{font-size:.875rem;line-height:1.25rem}.bn-container .file:bn-font-medium::file-selector-button{font-weight:500}.bn-container .placeholder:bn-text-muted-foreground::-moz-placeholder{color:hsl(var(--muted-foreground))}.bn-container .placeholder:bn-text-muted-foreground::placeholder{color:hsl(var(--muted-foreground))}.bn-container .hover:bn-bg-accent:hover{background-color:hsl(var(--accent))}.bn-container .hover:bn-bg-destructive\/80:hover{background-color:hsl(var(--destructive) / .8)}.bn-container .hover:bn-bg-destructive\/90:hover{background-color:hsl(var(--destructive) / .9)}.bn-container .hover:bn-bg-muted:hover{background-color:hsl(var(--muted))}.bn-container .hover:bn-bg-primary\/80:hover{background-color:hsl(var(--primary) / .8)}.bn-container .hover:bn-bg-primary\/90:hover{background-color:hsl(var(--primary) / .9)}.bn-container .hover:bn-bg-secondary\/80:hover{background-color:hsl(var(--secondary) / .8)}.bn-container .hover:bn-bg-transparent:hover{background-color:transparent}.bn-container .hover:bn-text-accent-foreground:hover{color:hsl(var(--accent-foreground))}.bn-container .hover:bn-text-muted-foreground:hover{color:hsl(var(--muted-foreground))}.bn-container .hover:bn-underline:hover{text-decoration-line:underline}.bn-container .focus:bn-bg-accent:focus{background-color:hsl(var(--accent))}.bn-container .focus:bn-text-accent-foreground:focus{color:hsl(var(--accent-foreground))}.bn-container .focus:bn-outline-none:focus{outline:2px solid transparent;outline-offset:2px}.bn-container .focus:bn-ring-2:focus{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.bn-container .focus:bn-ring-ring:focus{--tw-ring-color: hsl(var(--ring))}.bn-container .focus:bn-ring-offset-2:focus{--tw-ring-offset-width: 2px}.bn-container .focus-visible:bn-outline-none:focus-visible{outline:2px solid transparent;outline-offset:2px}.bn-container .focus-visible:bn-ring-2:focus-visible{--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow, 0 0 #0000)}.bn-container .focus-visible:bn-ring-ring:focus-visible{--tw-ring-color: hsl(var(--ring))}.bn-container .focus-visible:bn-ring-offset-2:focus-visible{--tw-ring-offset-width: 2px}.bn-container .disabled:bn-pointer-events-none:disabled{pointer-events:none}.bn-container .disabled:bn-cursor-not-allowed:disabled{cursor:not-allowed}.bn-container .disabled:bn-opacity-50:disabled{opacity:.5}.bn-container :is(.bn-peer:disabled~.peer-disabled:bn-cursor-not-allowed){cursor:not-allowed}.bn-container :is(.bn-peer:disabled~.peer-disabled:bn-opacity-70){opacity:.7}.bn-container .data-[disabled]:bn-pointer-events-none[data-disabled]{pointer-events:none}.bn-container .data-[side=bottom]:bn-translate-y-1[data-side=bottom]{--tw-translate-y: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container .data-[side=left]:bn--translate-x-1[data-side=left]{--tw-translate-x: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container .data-[side=right]:bn-translate-x-1[data-side=right]{--tw-translate-x: .25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container .data-[side=top]:bn--translate-y-1[data-side=top]{--tw-translate-y: -.25rem;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.bn-container .data-[state=active]:bn-bg-background[data-state=active]{background-color:hsl(var(--background))}.bn-container .data-[state=on]:bn-bg-accent[data-state=on],.bn-container .data-[state=open]:bn-bg-accent[data-state=open]{background-color:hsl(var(--accent))}.bn-container .data-[state=active]:bn-text-foreground[data-state=active]{color:hsl(var(--foreground))}.bn-container .data-[state=on]:bn-text-accent-foreground[data-state=on]{color:hsl(var(--accent-foreground))}.bn-container .data-[disabled]:bn-opacity-50[data-disabled]{opacity:.5}.bn-container .data-[state=active]:bn-shadow-sm[data-state=active]{--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / .05);--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow, 0 0 #0000),var(--tw-ring-shadow, 0 0 #0000),var(--tw-shadow)}.bn-container .data-[state=open]:bn-animate-in[data-state=open]{animation-name:enter;animation-duration:.15s;--tw-enter-opacity: initial;--tw-enter-scale: initial;--tw-enter-rotate: initial;--tw-enter-translate-x: initial;--tw-enter-translate-y: initial}.bn-container .data-[state=closed]:bn-animate-out[data-state=closed]{animation-name:exit;animation-duration:.15s;--tw-exit-opacity: initial;--tw-exit-scale: initial;--tw-exit-rotate: initial;--tw-exit-translate-x: initial;--tw-exit-translate-y: initial}.bn-container .data-[state=closed]:bn-fade-out-0[data-state=closed]{--tw-exit-opacity: 0}.bn-container .data-[state=open]:bn-fade-in-0[data-state=open]{--tw-enter-opacity: 0}.bn-container .data-[state=closed]:bn-zoom-out-95[data-state=closed]{--tw-exit-scale: .95}.bn-container .data-[state=open]:bn-zoom-in-95[data-state=open]{--tw-enter-scale: .95}.bn-container .data-[side=bottom]:bn-slide-in-from-top-2[data-side=bottom]{--tw-enter-translate-y: -.5rem}.bn-container .data-[side=left]:bn-slide-in-from-right-2[data-side=left]{--tw-enter-translate-x: .5rem}.bn-container .data-[side=right]:bn-slide-in-from-left-2[data-side=right]{--tw-enter-translate-x: -.5rem}.bn-container .data-[side=top]:bn-slide-in-from-bottom-2[data-side=top]{--tw-enter-translate-y: .5rem}.bn-container :is(.[&>span]:bn-line-clamp-1>span){overflow:hidden;display:-webkit-box;-webkit-box-orient:vertical;-webkit-line-clamp:1}
`;

export const interStyleCSS = clientCDNPrefix
  ? `/* Generated using https://google-webfonts-helper.herokuapp.com/fonts/inter?subsets=latin */

/* inter-100 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 100;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-100.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-100.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-200 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 200;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-200.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-200.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-300 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 300;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-300.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-300.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-regular - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 400;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-regular.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-regular.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-500 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 500;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-500.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-500.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-600 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 600;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-600.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-600.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-700 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 700;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-700.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-700.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-800 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 800;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-800.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-800.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
/* inter-900 - latin */
@font-face {
  font-family: "Inter";
  font-style: normal;
  font-weight: 900;
  src: local(""),
    url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-900.woff2") format("woff2"),
    /* Chrome 26+, Opera 23+, Firefox 39+ */
      url("${clientCDNPrefix}/inter-v12-latin/inter-v12-latin-900.woff") format("woff"); /* Chrome 6+, Firefox 3.6+, IE 9+, Safari 5.1+ */
}
`
  : '';

// Ignore rem for this inline css to work on Client Website when Chatwindow ebmedded
export const inlineEditorViewerCSS = props => `
[data-blocknote-viewer] {
  border-radius: 6px;
  border: none;
  padding: 12px 12px 12px 0;
}
[data-blocknote-viewer] .bn-container {
  padding: 0;
}

[data-blocknote-viewer] .bn-container .bn-inline-content {
  word-wrap: break-word;
}

[data-blocknote-viewer] .bn-container a {
  text-decoration: underline;
  cursor: pointer;
}

[data-blocknote-viewer] .bn-container a:visited {
  opacity: 0.8;
}
  
[data-blocknote-viewer] .bn-container p.bn-inline-content > a,
[data-blocknote-viewer] .bn-container p.bn-inline-content *:not([data-text-color]) > a  {
  color: ${props.actionColorLink};
}

[data-blocknote-viewer] .bn-container .bn-file-block-content-wrapper .bn-visual-media-wrapper {
  box-shadow: 0 0 1px 1px ${themeConfigurations.colors?.decaLight?.[1]};
  border-radius: 4px;
  width: 100%;
}

[data-blocknote-viewer] .bn-editor {
  padding: 0;
  font-size: 14px;
  writing-mode: horizontal-tb;
  color: ${props.textColor ?? themeConfigurations.colors?.decaMono?.[0]};
  background-color: transparent;
}

[data-blocknote-viewer] .bn-editor .block-note-youtube-iframe {
  max-width: 100%;
  min-width: 280px;
  min-height: 315px;
}

[data-blocknote-viewer] .bn-editor .bn-block .bn-block-content[data-content-type] .bn-inline-content:has(> .ProseMirror-trailingBreak),
[data-blocknote-viewer] .bn-editor .bn-block-content.ProseMirror-selectednode>*, .ProseMirror-selectednode>.bn-block-content>*  {
  outline: none;
}

[data-blocknote-viewer] .bn-container.bn-html-message .bn-editor {
  font-size: 14px;
}

[data-blocknote-viewer] .bn-container.bn-html-message .bn-editor .bn-visual-media-wrapper img.bn-visual-media {
  width: 100%;
  height: auto;
  min-height: auto;
}

[data-blocknote-viewer] [data-file-block] .bn-file-caption {
  font-style: italic;
  text-align: center;
}

[data-blocknote-viewer] .bn-container.bn-html-message .bn-editor .tableWrapper {
  overflow-x: scroll;
  scrollbar-color: ${themeConfigurations.colors?.decaLight?.[5]} transparent !important;
}

[data-blocknote-viewer] .bn-block-content[data-content-type="heading"] .bn-inline-content:has(> .ProseMirror-trailingBreak:only-child)::before {
  content: '';
}

[data-blocknote-viewer] .bn-block-group .bn-block-outer:last-child .bn-inline-content:has(> .ProseMirror-trailingBreak:only-child) {
  display: none;
}

[data-blocknote-viewer] [data-file-block] .bn-visual-media {
  min-height: 100px;
}

[data-blocknote-viewer] .bn-block-outer:not([data-prev-type])>.bn-block>.bn-block-content[data-content-type=numberedListItem]:before {
  white-space: nowrap;
}

[data-blocknote-viewer] .bn-block-content[data-content-type=numberedListItem] {
  gap: 10px;
}
`;
