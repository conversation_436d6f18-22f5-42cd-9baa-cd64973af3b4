import { FileBlockConfig, imageBlockConfig, imageParse } from '@blocknote/core';
import {
  useResolveUrl,
  createReactBlockSpec,
  ReactCustomBlockRenderProps,
  FigureWithCaption,
  ResizableFileBlockWrapper,
  LinkWithCaption,
} from '@blocknote/react';
import { IconPhoto } from '@tabler/icons-react';

export const ImagePreview = (
  props: Omit<ReactCustomBlockRenderProps<FileBlockConfig, any, any>, 'contentRef'>
) => {
  const resolved = useResolveUrl(props.block.props.url || '');

  return (
    // eslint-disable-next-line @next/next/no-img-element
    <img
      className={'bn-visual-media'}
      src={resolved.loadingState === 'loading' ? props.block.props.url : (resolved.downloadUrl || props.block.props.url)}
      alt={props.block.props.caption || 'BlockNote image'}
      contentEditable={false}
      draggable={false}
    />
  );
};

export const ImageToExternalHTML = (
  props: Omit<ReactCustomBlockRenderProps<typeof imageBlockConfig, any, any>, 'contentRef'>
) => {
  if (!props.block.props.url) {
    return <p>Add image</p>;
  }

  const image = props.block.props.showPreview ? (
    // eslint-disable-next-line @next/next/no-img-element
    <img
      src={props.block.props.url}
      alt={props.block.props.name || props.block.props.caption || 'BlockNote image'}
      style={
        props.block.props.previewWidth
          ? { maxWidth: '100%', height: 'auto' }
          : undefined
      }
    />
  ) : (
    <a href={props.block.props.url}>{props.block.props.name || props.block.props.url}</a>
  );

  if (props.block.props.caption) {
    return props.block.props.showPreview ? (
      <FigureWithCaption caption={props.block.props.caption}>{image}</FigureWithCaption>
    ) : (
      <LinkWithCaption caption={props.block.props.caption}>{image}</LinkWithCaption>
    );
  }

  return image;
};

export const ImageBlock = (
  props: ReactCustomBlockRenderProps<typeof imageBlockConfig, any, any>
) => {
  const normalizedProps = {
    ...props,
    block: {
      ...props.block,
      props: {
        ...props.block.props,
        caption:
          props.block.props.caption === props.block.props.name ? '' : props.block.props.caption,
      },
    },
  };

  // Safe access to editor dictionary with fallback
  const buttonText = normalizedProps.editor.dictionary?.file_blocks?.image?.add_button_text || 'Add Image';

  return (
    <ResizableFileBlockWrapper
      {...(normalizedProps as any)}
      buttonText={buttonText}
      buttonIcon={<IconPhoto size={24} />}>
      <ImagePreview {...(normalizedProps as any)} />
    </ResizableFileBlockWrapper>
  );
};

export const ReactImageBlock = createReactBlockSpec(imageBlockConfig, {
  render: ImageBlock,
  parse: imageParse,
  toExternalHTML: ImageToExternalHTML,
});
