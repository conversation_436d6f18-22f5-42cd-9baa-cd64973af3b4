import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { MantineWrapper } from '../../utils/unitTest';
import React from 'react';

// Mock the modules before importing our components
vi.mock('@blocknote/react', () => {
  return {
    createReactBlockSpec: vi.fn().mockImplementation((config, options) => {
      return options;
    }),
    useResolveUrl: vi.fn().mockImplementation(url => ({
      loadingState: 'success',
      downloadUrl: url,
      error: null,
    })),
    ResizableFileBlockWrapper: vi
      .fn()
      .mockImplementation(({ buttonText, buttonIcon, children, block }) => {
        // If no URL, render the add button
        if (!block.props.url) {
          return <button data-testid='add-file-button'>{buttonText}</button>;
        }
        // If URL exists, render the wrapper with children
        return <div data-testid='resize-handles-wrapper'>{children}</div>;
      }),
    FigureWithCaption: vi.fn().mockImplementation(({ caption, children }) => (
      <figure data-testid='figure-with-caption'>
        {children}
        <figcaption>{caption}</figcaption>
      </figure>
    )),
    LinkWithCaption: vi.fn().mockImplementation(({ caption, children }) => (
      <div data-testid='link-with-caption'>
        {children}
        <div>{caption}</div>
      </div>
    )),
  };
});

vi.mock('@tabler/icons-react', () => ({
  IconPhoto: vi.fn().mockImplementation(() => <span data-testid='icon-photo'>Icon</span>),
}));

// Mock @blocknote/core
vi.mock('@blocknote/core', () => ({
  imageBlockConfig: {
    type: 'image',
    propSchema: {
      url: '',
      name: '',
      caption: '',
      showPreview: true,
      previewWidth: true,
    },
  },
  imageParse: vi.fn(),
}));

// Import the actual implementation
import * as originalModule from './ImageBlock';

// Constants and mock data
const MOCK_EDITOR = {
  dictionary: {
    file_blocks: {
      image: {
        add_button_text: 'Add Image',
      },
    },
  },
  domElement: {
    firstElementChild: {
      clientWidth: 800,
    },
  },
};

const MOCK_BLOCK_WITH_URL = {
  props: {
    url: 'https://example.com/image.jpg',
    name: 'test-image',
    caption: '',
    showPreview: true,
    previewWidth: true,
  },
};

const MOCK_BLOCK_WITH_CAPTION = {
  props: {
    url: 'https://example.com/image.jpg',
    name: 'test-image',
    caption: 'Test Caption',
    showPreview: true,
    previewWidth: true,
  },
};

const MOCK_BLOCK_WITHOUT_URL = {
  props: {
    url: '',
    name: '',
    caption: '',
    showPreview: false,
  },
};

const MOCK_BLOCK_WITHOUT_PREVIEW = {
  props: {
    url: 'https://example.com/image.jpg',
    name: 'test-image',
    caption: '',
    showPreview: false,
  },
};

const MOCK_BLOCK_CAPTION_SAME_AS_NAME = {
  props: {
    url: 'https://example.com/image.jpg',
    name: 'test-image',
    caption: 'test-image', // Same as name
    showPreview: true,
    previewWidth: true,
  },
};

const MOCK_BLOCK_NULL_VALUES = {
  props: {
    url: null,
    name: null,
    caption: null,
    showPreview: false,
    previewWidth: false,
  },
};

const MOCK_BLOCK_UNDEFINED_VALUES = {
  props: {
    url: undefined,
    name: undefined,
    caption: undefined,
    showPreview: undefined,
    previewWidth: undefined,
  },
};

describe('ImageBlock component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders ResizableFileBlockWrapper when no URL is provided', () => {
    // Use the ImageBlock component directly
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_WITHOUT_URL}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // The ResizableFileBlockWrapper should render the add button when no URL
    expect(screen.getByTestId('add-file-button')).toBeInTheDocument();
    expect(screen.getByText('Add Image')).toBeInTheDocument();
  });

  it('renders ResizableFileBlockWrapper with ImagePreview when URL is provided', () => {
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_WITH_URL}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // When URL is provided, it should render the image preview
    expect(screen.getByTestId('resize-handles-wrapper')).toBeInTheDocument();
    // The ImagePreview should render an image element
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('normalizes caption when caption equals name', () => {
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_CAPTION_SAME_AS_NAME}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // When caption equals name, caption should be normalized to empty string
    // So the image should use default alt text
    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'BlockNote image');
  });

  it('preserves caption when caption is different from name', () => {
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_WITH_CAPTION}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // When caption is different from name, caption should be preserved
    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'Test Caption');
  });

  it('handles null values in block props', () => {
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_NULL_VALUES}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // Should render add button when URL is null
    expect(screen.getByTestId('add-file-button')).toBeInTheDocument();
  });

  it('handles undefined values in block props', () => {
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_UNDEFINED_VALUES}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // Should render add button when URL is undefined
    expect(screen.getByTestId('add-file-button')).toBeInTheDocument();
  });

  it('handles null caption normalization', () => {
    const blockWithNullCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: null,
        caption: null,
        showPreview: true,
        previewWidth: true,
      },
    };

    render(
      <originalModule.ImageBlock
        block={blockWithNullCaption}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Should use default alt text
  });

  it('passes correct props to ResizableFileBlockWrapper', async () => {
    const { ResizableFileBlockWrapper } = vi.mocked(await import('@blocknote/react'));
    
    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_WITH_URL}
        editor={MOCK_EDITOR}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    expect(ResizableFileBlockWrapper).toHaveBeenCalledWith(
      expect.objectContaining({
        buttonText: 'Add Image',
        buttonIcon: expect.any(Object),
        block: expect.objectContaining({
          props: expect.objectContaining({
            url: 'https://example.com/image.jpg',
          }),
        }),
      }),
      expect.any(Object)
    );
  });

  it('handles missing editor dictionary gracefully', () => {
    const incompleteEditor = {
      dictionary: {
        file_blocks: {
          // Missing image property
        },
      },
      domElement: {
        firstElementChild: {
          clientWidth: 800,
        },
      },
    };

    // Should not throw error and render with fallback
    expect(() => {
      render(
        <originalModule.ImageBlock
          block={MOCK_BLOCK_WITHOUT_URL}
          editor={incompleteEditor}
          contentRef={null}
        />,
        { wrapper: MantineWrapper }
      );
    }).not.toThrow();
  });

  it('handles completely missing dictionary', () => {
    const incompleteEditor = {
      dictionary: {},
      domElement: {
        firstElementChild: {
          clientWidth: 800,
        },
      },
    };

    // Should not throw error and render with fallback
    expect(() => {
      render(
        <originalModule.ImageBlock
          block={MOCK_BLOCK_WITHOUT_URL}
          editor={incompleteEditor}
          contentRef={null}
        />,
        { wrapper: MantineWrapper }
      );
    }).not.toThrow();
  });
});

describe('ImagePreview component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders image with correct src and alt attributes', () => {
    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', 'https://example.com/image.jpg');
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Empty caption results in default alt text
  });

  it('uses caption as alt text when caption is provided', () => {
    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_CAPTION} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'Test Caption');
  });

  it('handles loading state from useResolveUrl', async () => {
    const { useResolveUrl } = vi.mocked(await import('@blocknote/react'));
    useResolveUrl.mockReturnValueOnce({
      loadingState: 'loading',
      downloadUrl: null,
      error: null,
    });

    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    // Should use original URL when loading
    expect(img).toHaveAttribute('src', 'https://example.com/image.jpg');
  });

  it('handles error state from useResolveUrl', async () => {
    const { useResolveUrl } = vi.mocked(await import('@blocknote/react'));
    useResolveUrl.mockReturnValueOnce({
      loadingState: 'error',
      downloadUrl: null,
      error: 'Failed to load image',
    });

    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    // Should use original URL when error occurs (downloadUrl is null, so falls back to original)
    expect(img).toHaveAttribute('src', 'https://example.com/image.jpg');
  });

  it('handles null caption with fallback alt text', () => {
    const blockWithNullCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: null,
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithNullCaption} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Should use default alt text
  });

  it('handles undefined caption with fallback alt text', () => {
    const blockWithUndefinedCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: undefined,
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithUndefinedCaption} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Should use default alt text
  });

  it('sets correct image attributes', () => {
    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('contentEditable', 'false');
    expect(img).toHaveAttribute('draggable', 'false');
    expect(img).toHaveClass('bn-visual-media');
  });

  it('handles empty URL', () => {
    const blockWithEmptyUrl = {
      props: {
        url: '',
        name: 'test-image',
        caption: 'Test Caption',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithEmptyUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', '');
    expect(img).toHaveAttribute('alt', 'Test Caption');
  });

  it('handles successful URL resolution', async () => {
    const { useResolveUrl } = vi.mocked(await import('@blocknote/react'));
    useResolveUrl.mockReturnValueOnce({
      loadingState: 'success',
      downloadUrl: 'https://resolved.example.com/image.jpg',
      error: null,
    });

    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', 'https://resolved.example.com/image.jpg');
  });
});

describe('ImageToExternalHTML', () => {
  it('renders placeholder when no URL is provided', () => {
    render(
      <originalModule.ImageToExternalHTML block={MOCK_BLOCK_WITHOUT_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByText('Add image')).toBeInTheDocument();
  });

  it('renders image when showPreview is true', () => {
    render(
      <originalModule.ImageToExternalHTML block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', 'https://example.com/image.jpg');
    expect(img).toHaveAttribute('alt', 'test-image'); // Uses name as alt when no caption
    // Check style attribute instead of width since previewWidth is now boolean
    expect(img).toHaveStyle({ maxWidth: '100%', height: 'auto' });
  });

  it('renders link when showPreview is false', () => {
    render(
      <originalModule.ImageToExternalHTML
        block={MOCK_BLOCK_WITHOUT_PREVIEW}
        editor={MOCK_EDITOR}
      />,
      { wrapper: MantineWrapper }
    );

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', 'https://example.com/image.jpg');
    expect(link).toHaveTextContent('test-image');
  });

  it('renders figure with caption when caption is provided and showPreview is true', () => {
    render(
      <originalModule.ImageToExternalHTML block={MOCK_BLOCK_WITH_CAPTION} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByTestId('figure-with-caption')).toBeInTheDocument();
    expect(screen.getByRole('img')).toBeInTheDocument();
    expect(screen.getByText('Test Caption')).toBeInTheDocument();
  });

  it('renders link with caption when caption is provided and showPreview is false', () => {
    const blockWithCaptionNoPreview = {
      ...MOCK_BLOCK_WITH_CAPTION,
      props: {
        ...MOCK_BLOCK_WITH_CAPTION.props,
        showPreview: false,
      },
    };

    render(
      <originalModule.ImageToExternalHTML block={blockWithCaptionNoPreview} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByTestId('link-with-caption')).toBeInTheDocument();
    expect(screen.getByRole('link')).toBeInTheDocument();
    expect(screen.getByText('Test Caption')).toBeInTheDocument();
  });

  it('handles null URL', () => {
    const blockWithNullUrl = {
      props: {
        url: null,
        name: 'test-image',
        caption: 'Test Caption',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithNullUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    expect(screen.getByText('Add image')).toBeInTheDocument();
  });

  it('handles undefined URL', () => {
    const blockWithUndefinedUrl = {
      props: {
        url: undefined,
        name: 'test-image',
        caption: 'Test Caption',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithUndefinedUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    expect(screen.getByText('Add image')).toBeInTheDocument();
  });

  it('handles previewWidth false', () => {
    const blockWithoutPreviewWidth = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: '',
        showPreview: true,
        previewWidth: false,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithoutPreviewWidth} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).not.toHaveStyle({ maxWidth: '100%' });
  });

  it('uses URL as link text when no name is provided', () => {
    const blockWithoutName = {
      props: {
        url: 'https://example.com/image.jpg',
        name: '',
        caption: '',
        showPreview: false,
        previewWidth: false,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithoutName} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveTextContent('https://example.com/image.jpg');
  });

  it('uses URL as link text when name is null', () => {
    const blockWithNullName = {
      props: {
        url: 'https://example.com/image.jpg',
        name: null,
        caption: '',
        showPreview: false,
        previewWidth: false,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithNullName} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveTextContent('https://example.com/image.jpg');
  });

  it('uses URL as link text when name is undefined', () => {
    const blockWithUndefinedName = {
      props: {
        url: 'https://example.com/image.jpg',
        name: undefined,
        caption: '',
        showPreview: false,
        previewWidth: false,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithUndefinedName} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const link = screen.getByRole('link');
    expect(link).toBeInTheDocument();
    expect(link).toHaveTextContent('https://example.com/image.jpg');
  });

  it('uses correct alt text priority: name > caption > default', () => {
    // Test case 1: Name exists, should use name
    render(
      <originalModule.ImageToExternalHTML block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );
    let img = screen.getByRole('img');
    expect(img).toHaveAttribute('alt', 'test-image'); // Uses name

    // Test case 2: Empty string name but caption exists, should use caption
    const blockWithOnlyCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: '',
        caption: 'Only Caption',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(
      <originalModule.ImageToExternalHTML block={blockWithOnlyCaption} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );
    img = screen.getAllByRole('img')[1]; // Get the second image
    expect(img).toHaveAttribute('alt', 'Only Caption'); // Uses caption when name is empty

    // Test case 3: Both name and caption are empty strings, should use default
    const blockWithNeither = {
      props: {
        url: 'https://example.com/image.jpg',
        name: '',
        caption: '',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithNeither} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });
    img = screen.getAllByRole('img')[2]; // Get the third image
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Uses default when both are empty

    // Test case 4: Name is null, caption exists, should use caption
    const blockWithNullName = {
      props: {
        url: 'https://example.com/image.jpg',
        name: null,
        caption: 'Caption Text',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(
      <originalModule.ImageToExternalHTML block={blockWithNullName} editor={MOCK_EDITOR} />,
      {
        wrapper: MantineWrapper,
      }
    );
    img = screen.getAllByRole('img')[3]; // Get the fourth image
    expect(img).toHaveAttribute('alt', 'Caption Text'); // Uses caption when name is null
  });

  it('handles null name and caption correctly', () => {
    const blockWithNullValues = {
      props: {
        url: 'https://example.com/image.jpg',
        name: null,
        caption: null,
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithNullValues} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Uses default when both are null
  });

  it('handles mixed null and undefined values', () => {
    const blockWithMixedValues = {
      props: {
        url: 'https://example.com/image.jpg',
        name: null,
        caption: undefined,
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImageToExternalHTML block={blockWithMixedValues} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'BlockNote image'); // Uses default when both are null/undefined
  });
});

describe('Caption handling in ImagePreview', () => {
  it('uses caption as alt text when caption is provided (even if same as name)', () => {
    render(
      <originalModule.ImagePreview block={MOCK_BLOCK_CAPTION_SAME_AS_NAME} editor={MOCK_EDITOR} />,
      { wrapper: MantineWrapper }
    );

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    // ImagePreview directly uses the caption prop as provided (no normalization at this level)
    expect(img).toHaveAttribute('alt', 'test-image');
  });

  it('uses default alt text when no caption is provided', () => {
    render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    // When caption is empty string, should use default alt text
    expect(img).toHaveAttribute('alt', 'BlockNote image');
  });

  it('uses empty string caption correctly', () => {
    const blockWithEmptyCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: '', // Empty caption
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithEmptyCaption} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    // When caption is empty string, should use default alt text
    expect(img).toHaveAttribute('alt', 'BlockNote image');
  });
});

describe('ReactImageBlock', () => {
  it('exports correct block specification', () => {
    // The ReactImageBlock is created during module import, so we just verify it exists
    expect(originalModule.ReactImageBlock).toBeDefined();
    expect(typeof originalModule.ReactImageBlock).toBe('object');
  });
});

describe('Edge cases and error handling', () => {
  it('handles extremely long URLs', () => {
    const longUrl = 'https://example.com/' + 'a'.repeat(2000) + '.jpg';
    const blockWithLongUrl = {
      props: {
        url: longUrl,
        name: 'test-image',
        caption: '',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithLongUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', longUrl);
  });

  it('handles special characters in URLs', () => {
    const specialUrl = 'https://example.com/image%20with%20spaces.jpg?param=value&other=test';
    const blockWithSpecialUrl = {
      props: {
        url: specialUrl,
        name: 'test-image',
        caption: '',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithSpecialUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', specialUrl);
  });

  it('handles special characters in caption', () => {
    const blockWithSpecialCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: 'Caption with "quotes" & <tags> & émojis 🎉',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithSpecialCaption} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', 'Caption with "quotes" & <tags> & émojis 🎉');
  });

  it('handles very long captions', () => {
    const longCaption = 'This is a very long caption that goes on and on and on. '.repeat(50);
    const blockWithLongCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: longCaption,
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithLongCaption} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('alt', longCaption);
  });

  it('handles missing editor domElement', () => {
    const incompleteEditor = {
      dictionary: {
        file_blocks: {
          image: {
            add_button_text: 'Add Image',
          },
        },
      },
    };

    render(
      <originalModule.ImageBlock
        block={MOCK_BLOCK_WITH_URL}
        editor={incompleteEditor}
        contentRef={null}
      />,
      { wrapper: MantineWrapper }
    );

    // Should still render the image
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('handles data URLs correctly', () => {
    const dataUrl = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    const blockWithDataUrl = {
      props: {
        url: dataUrl,
        name: 'test-image',
        caption: '',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithDataUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', dataUrl);
  });

  it('handles blob URLs correctly', () => {
    const blobUrl = 'blob:https://example.com/12345678-1234-1234-1234-123456789012';
    const blockWithBlobUrl = {
      props: {
        url: blobUrl,
        name: 'test-image',
        caption: '',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(<originalModule.ImagePreview block={blockWithBlobUrl} editor={MOCK_EDITOR} />, {
      wrapper: MantineWrapper,
    });

    const img = screen.getByRole('img');
    expect(img).toBeInTheDocument();
    expect(img).toHaveAttribute('src', blobUrl);
  });

     it('handles URL resolution with different states', async () => {
     const { useResolveUrl } = vi.mocked(await import('@blocknote/react'));
     
     // Test different resolution states
     const testCases = [
       { loadingState: 'loading', downloadUrl: null, error: null },
       { loadingState: 'success', downloadUrl: 'https://resolved.com/image.jpg', error: null },
       { loadingState: 'error', downloadUrl: null, error: 'Network error' },
     ];

     testCases.forEach((mockReturn, index) => {
       vi.clearAllMocks();
       useResolveUrl.mockReturnValueOnce(mockReturn);

       const { container } = render(<originalModule.ImagePreview block={MOCK_BLOCK_WITH_URL} editor={MOCK_EDITOR} />, {
         wrapper: MantineWrapper,
       });

       const img = container.querySelector('img');
       
       if (mockReturn.loadingState === 'success' && mockReturn.downloadUrl) {
         expect(img).toHaveAttribute('src', mockReturn.downloadUrl);
       } else {
         expect(img).toHaveAttribute('src', MOCK_BLOCK_WITH_URL.props.url);
       }
       
       container.remove();
     });
   });
});

describe('Integration tests', () => {
  it('handles complete block lifecycle', () => {
    const blockWithoutUrl = { props: { url: '', name: '', caption: '', showPreview: false } };
    const blockWithUrl = { props: { url: 'https://example.com/image.jpg', name: 'test', caption: '', showPreview: true, previewWidth: true } };

    // Start with no URL
    const { rerender } = render(
      <originalModule.ImageBlock block={blockWithoutUrl} editor={MOCK_EDITOR} contentRef={null} />,
      { wrapper: MantineWrapper }
    );

    expect(screen.getByTestId('add-file-button')).toBeInTheDocument();

    // Add URL
    rerender(
      <originalModule.ImageBlock block={blockWithUrl} editor={MOCK_EDITOR} contentRef={null} />
    );

    expect(screen.getByTestId('resize-handles-wrapper')).toBeInTheDocument();
    expect(screen.getByRole('img')).toBeInTheDocument();
  });

  it('handles caption normalization correctly in integration', () => {
    const blockWithMatchingCaption = {
      props: {
        url: 'https://example.com/image.jpg',
        name: 'test-image',
        caption: 'test-image',
        showPreview: true,
        previewWidth: true,
      },
    };

    render(
      <originalModule.ImageBlock block={blockWithMatchingCaption} editor={MOCK_EDITOR} contentRef={null} />,
      { wrapper: MantineWrapper }
    );

    // ImageBlock should normalize caption to empty string when it equals name
    // ImagePreview should then use default alt text
    const img = screen.getByRole('img');
    expect(img).toHaveAttribute('alt', 'BlockNote image');
  });

  it('handles external HTML export with various props', () => {
    const testCases = [
      { props: { url: '', name: '', caption: '', showPreview: false }, expected: 'Add image' },
      { props: { url: 'https://example.com/image.jpg', name: 'test', caption: '', showPreview: true, previewWidth: true }, expected: 'img' },
      { props: { url: 'https://example.com/image.jpg', name: 'test', caption: '', showPreview: false }, expected: 'link' },
      { props: { url: 'https://example.com/image.jpg', name: 'test', caption: 'Caption', showPreview: true, previewWidth: true }, expected: 'figure' },
    ];

    testCases.forEach(({ props, expected }) => {
      const { container } = render(
        <originalModule.ImageToExternalHTML block={{ props }} editor={MOCK_EDITOR} />,
        { wrapper: MantineWrapper }
      );

      if (expected === 'Add image') {
        expect(screen.getByText('Add image')).toBeInTheDocument();
      } else if (expected === 'img') {
        expect(screen.getByRole('img')).toBeInTheDocument();
      } else if (expected === 'link') {
        expect(screen.getByRole('link')).toBeInTheDocument();
      } else if (expected === 'figure') {
        expect(screen.getByTestId('figure-with-caption')).toBeInTheDocument();
      }

      container.remove();
    });
  });
});
