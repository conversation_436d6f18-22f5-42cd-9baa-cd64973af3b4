import { SchemaField } from '../components/FormChema/type';
import orderBy from 'lodash/orderBy';
import isNumber from 'lodash/isNumber';
import isObject from 'lodash/isObject';
import { v4 as uuidv4 } from 'uuid';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';

export const allowedTypes = [
  'text',
  'number',
  'datetime',
  'string',
  'keyvalue',
  'password',
  'checkbox',
  'radio',
  'options',
  'multiOptions',
  'select',
  'textarea',
  'code',
  'arrayObject',
  'array',
  'object',
  'comboboxDataPoint',
];

export const getDefaultValue = (schema: SchemaField) => {
  if (schema.type === 'keyvalue') {
    return Object.entries(schema.default || {}).reduce(
      (acc, [key, value]) => {
        acc[key] = getDefaultValue(value as unknown as SchemaField);
        return acc;
      },
      {} as Record<string, any>
    );
  }

  if (schema.type === 'arrayObject') {
    return [
      Object.values(schema.items?.properties || {}).reduce<Record<string, any>>(
        (acc, item: any) => {
          acc[item.name] = getDefaultValue(item as unknown as SchemaField);
          return acc;
        },
        { id: uuidv4() }
      ),
    ];
  }

  return schema.default;
};

export const getDefaultValues = (schema: SchemaField) => {
  const defaultValues = {};
  if (!schema || typeof schema !== 'object' || Array.isArray(schema)) {
    return undefined;
  }

  Object.entries(schema).forEach(([key, value]) => {
    if (key === 'properties') {
      Object.entries(value).forEach(([key, value]) => {
        defaultValues[key] = getDefaultValue(value as unknown as SchemaField);
      });
      return;
    }
    if (value?.type && allowedTypes.includes(value?.type)) {
      defaultValues[key] = getDefaultValue(value as unknown as SchemaField);
    } else if (typeof value === 'object' && value) {
      defaultValues[key] = getDefaultValues(value as unknown as SchemaField);
    }
  });

  return defaultValues;
};

export const getUniqueOptions = <T extends Record<string, any>>({
  options,
  key,
}: {
  options: T[];
  key: keyof T;
}): T[] => {
  if (!options?.length) return [];

  const uniqueValues = new Set<string>();

  return options.filter(option => {
    const keyValue = option?.[key];
    if (!keyValue) return false;
    if (uniqueValues.has(String(keyValue))) return false;
    uniqueValues.add(String(keyValue));
    return true;
  });
};

export const objectToSortedArray = <T extends Record<string, any>>(obj: T, key: keyof T): any[] => {
  if (!obj || !isObject(obj)) return [];
  const array = Object.values(obj);
  return orderBy(array, [item => isNumber(item.order), key], 'desc');
};

type RefCache = Map<string, any>;
type ObjectCache = WeakMap<object, any>;

export const resolveRefs = (
  obj: any,
  root: any,
  pathCache: RefCache = new Map(),
  objectCache: ObjectCache = new WeakMap()
): any => {
  if (obj === null || typeof obj !== 'object') return obj;

  if (objectCache.has(obj)) {
    return objectCache.get(obj);
  }

  const cloned = Array.isArray(obj) ? [] : { ...obj };
  objectCache.set(obj, cloned);

  if (Array.isArray(obj)) {
    return obj.map(item => resolveRefs(item, root, pathCache, objectCache));
  }

  if ('$ref' in obj) {
    const ref = obj.$ref;
    if (typeof ref === 'string' && ref.startsWith('#/')) {
      if (pathCache.has(ref)) {
        return pathCache.get(ref);
      }

      const refPath = ref.slice(2).split('/');
      const resolved = get(root, refPath);
      if (!resolved) {
        console.warn(`Reference not found: ${ref}`);
        return {};
      }

      const merged = {
        ...resolveRefs(resolved, root, pathCache, objectCache),
        ...Object.fromEntries(Object.entries(cloned).filter(([key]) => key !== '$ref')),
      };

      pathCache.set(ref, merged);
      return merged;
    }
  }

  for (const [key, value] of Object.entries(obj)) {
    cloned[key] = resolveRefs(value, root, pathCache, objectCache);
  }

  return cloned;
};

export const getSelectedProperties = (
  itemObject: any,
  selectedObject: any,
  defaultToFirstObject: boolean
): Record<string, any> => {
  if (!selectedObject) {
    return {};
  }

  const selectedObjectKey = defaultToFirstObject ? Object.keys(itemObject)[0] : selectedObject;
  if (!selectedObjectKey) return {};
  return (
    itemObject[selectedObjectKey]?.properties ||
    itemObject[selectedObjectKey]?.data?.properties ||
    {}
  );
};

export const getNodeApiCallPayloads = (
  schema: Record<string, any>,
  objectType: 'action' | 'trigger',
  selectedObject?: any
) => {
  const objectBlock = objectType === 'action' ? schema?.actions : schema?.triggers;
  const selectedProperties = getSelectedProperties(objectBlock, selectedObject || {}, false);

  if (!isObject(selectedProperties)) {
    return [];
  }

  return Object.entries(selectedProperties)
    .filter(([, property]) => {
      return isObject(property) && 'apiCall' in property;
    })
    .map(([_, property]: [string, Record<string, any>]) => {
      // Get the action/trigger name from the selectedObject
      const actionOrTriggerName = selectedObject || Object.keys(objectBlock)[0];
      const attribute = `${actionOrTriggerName}.${property.name}`;
      return {
        node: schema.name,
        attribute,
        payload: property.apiCall.payload || {},
        groupName: property.name,
        always: !!property.apiCall?.always,
      };
    });
};

const hasValueReference = (value: string) => {
  return typeof value === 'string' && value.startsWith('@');
};

const resolveValueReference = (value: string, nodeApiCallResults: Record<string, any>): any => {
  const path = value.slice(1);
  const resolvedValue = get(nodeApiCallResults, path);
  return resolvedValue;
};

export const resolvePropertiesApiCallResults = (
  nodeApiCallResults: Record<string, any>,
  selectedProperties: Record<string, any>
): Record<string, any> => {
  Object.values(selectedProperties).forEach(prop => {
    if (!('apiCall' in prop)) return;

    if (hasValueReference(prop.value)) {
      prop.default = resolveValueReference(prop.value, nodeApiCallResults);
      return;
    }

    if (!isObject(prop.properties) || isEmpty(prop.properties)) return;

    Object.values(prop.properties).forEach((subField: any) => {
      if (hasValueReference(subField.value)) {
        subField.default = resolveValueReference(subField.value, nodeApiCallResults);
      }
    });
  });

  return selectedProperties;
};
