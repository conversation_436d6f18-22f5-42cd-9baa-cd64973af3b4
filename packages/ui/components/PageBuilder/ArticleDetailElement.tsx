import { Box, Divider, Flex, rem, Text, Tooltip } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { BlockNoteViewer } from '@resola-ai/blocknote-editor';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
import { ListType } from '../../types/pageBuilder';
import {
  generateResponsiveMaxWidth,
  generateResponsivePadding,
  generateResponsiveStyles,
} from '../../utils';
import { DecaButton } from '../DecaButton';
import { withTolgee } from '../hoc/withTolgee';

const useStyles = createStyles(theme => ({
  tooltip: {
    '[data-mantine-color-scheme="light"] &': {
      backgroundColor: theme.colors.decaMono[1],
      color: theme.colors.decaGrey[9],
      border: `1px solid ${theme.colors.decaLight[4]}`,
    },
  },
}));

const ArticleDetailElement = (props: Record<string, any>) => {
  const { t } = useTranslate('page-builder');
  const { classes } = useStyles();
  const {
    article,
    maxWidth,
    type,
    backgroundColor,
    padding,
    hasMaxWidth,
    textColor,
    dividerColor,
    contentBackgroundColor,
    borderColor,
  } = props;

  if (!article) return null;

  const handleCopy = async () => {
    if (!props?.onShortenUrl) return;
    const url = await props.onShortenUrl();
    await navigator.clipboard.writeText(url);
  };

  return (
    <Flex
      styles={generateResponsiveStyles({ padding: generateResponsivePadding(padding) })}
      w={'100%'}
      bg={backgroundColor}
    >
      <Flex
        styles={generateResponsiveStyles({
          maxWidth: generateResponsiveMaxWidth(hasMaxWidth, maxWidth),
          borderRadius: type === ListType.BlankPage ? '0' : rem(20),
          background: type === ListType.BlankPage ? 'transparent' : contentBackgroundColor,
          border: type === ListType.BlankPage ? 'none' : `1px solid ${borderColor}`,
        })}
        mih={rem(360)}
        m={'0'}
      >
        <Flex p='lg' w={'100%'} h={'100%'}>
          <Flex direction='column' w={'100%'} gap={rem(20)}>
            <Flex>
              <Box>
                <Text fw={500} size={'xl'} c={textColor}>
                  {article.title}
                </Text>
              </Box>
            </Flex>
            <Flex gap={rem(20)} align={'center'} justify={'space-between'}>
              <Box c={textColor}>
                {t('lastUpdated')}:{' '}
                {article ? dayjs(article.updated_at).format(t('lastUpdatedDateFormat')) : ''}
              </Box>
              <Tooltip
                label={t('linkCopied')}
                position='bottom-end'
                opened={props?.isCopied || false}
                classNames={{
                  tooltip: classes.tooltip,
                }}
              >
                <DecaButton
                  variant='neutral'
                  size='sm'
                  style={{ borderRadius: rem(16) }}
                  onClick={() => handleCopy()}
                >
                  {t('copyLink')}
                </DecaButton>
              </Tooltip>
            </Flex>
            <Divider color={dividerColor} />
            <Flex>
              <BlockNoteViewer initialHTML={article?.content_raw || ''} textColor={textColor} />
            </Flex>
          </Flex>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default withTolgee(ArticleDetailElement);
