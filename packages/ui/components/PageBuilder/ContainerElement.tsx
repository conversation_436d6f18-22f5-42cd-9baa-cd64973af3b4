import { Flex, rem } from '@mantine/core';
import { BackgroundType, LinkBehaviorType, LinkDestinationType } from '../../types/pageBuilder';
import {
  createResponsiveValue,
  generateResponsiveDimension,
  generateResponsivePadding,
  generateResponsiveStyles,
  isResponsiveProp,
} from '../../utils';

const ContainerElement = (props: Record<string, any>) => {
  const {
    gap,
    padding,
    align,
    justify,
    direction,
    mediaUrl,
    objectFit,
    width,
    height,
    maxWidth,
    backgroundType,
    backgroundColor,
    overlay,
    link,
  } = props;

  // Check if link is valid and has required properties
  const isValidLink = link && link.url && link.destination;
  const href = isValidLink
    ? (link.destination === LinkDestinationType.Page ? `/${link.url}` : link.url)
    : undefined;

  const handleClick = (e: React.MouseEvent) => {
    if (isValidLink) {
      e.preventDefault();
      if (link.behavior === LinkBehaviorType.NewTab) {
        window.open(href, '_blank', 'noopener,noreferrer');
      } else {
        window.location.href = href!;
      }
    }
  };

  return (
    <Flex
      onClick={handleClick}
      styles={generateResponsiveStyles({
        flex: '1 1 auto',
        width: generateResponsiveDimension(width),
        height: generateResponsiveDimension(height),
        alignItems: align ?? 'flex-start',
        justifyContent: justify ?? 'flex-start',
        flexDirection: direction ?? 'column',
        gap: generateResponsiveDimension(gap, '0px'),
        ...(backgroundType === BackgroundType.Image && {
          backgroundImage: `url(${mediaUrl})`,
        }),
        ...(backgroundType === BackgroundType.Color && {
          backgroundColor: backgroundColor,
        }),
        ...(backgroundType === BackgroundType.None && {
          backgroundColor: 'transparent',
        }),
        padding: generateResponsivePadding(padding),
        backgroundSize: createResponsiveValue(
          ['cover', 'fill'].includes(objectFit?.mobile) ? 'cover' : 'contain',
          ['cover', 'fill'].includes(objectFit?.tablet) ? 'cover' : 'contain',
          ['cover', 'fill'].includes(objectFit?.desktop) ? 'cover' : 'contain'
        ),
        overflow: 'hidden',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        maxWidth: isResponsiveProp(maxWidth)
          ? createResponsiveValue(
            maxWidth.mobile ? rem(maxWidth.mobile) : 'none',
            maxWidth.tablet ? rem(maxWidth.tablet) : 'none',
            maxWidth.desktop ? rem(maxWidth.desktop) : 'none'
          )
          : maxWidth,
        position: 'relative',
        ...(isValidLink && {
          cursor: 'pointer',
          textDecoration: 'none',
          color: 'inherit',
        }),
        '&::before': createResponsiveValue(
          overlay?.mobile?.enabled
            ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: overlay.mobile.color,
              opacity: overlay.mobile.opacity / 100,
              pointerEvents: 'none',
              zIndex: 1,
            }
            : {},
          overlay?.tablet?.enabled
            ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: overlay.tablet.color,
              opacity: overlay.tablet.opacity / 100,
              pointerEvents: 'none',
              zIndex: 1,
            }
            : {},
          overlay?.desktop?.enabled
            ? {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundColor: overlay.desktop.color,
              opacity: overlay.desktop.opacity / 100,
              pointerEvents: 'none',
              zIndex: 1,
            }
            : {}
        ),
        '& > *': {
          position: 'relative',
          zIndex: 2,
        },
      })}>
      {props.children}
    </Flex>
  );
};

export default ContainerElement;