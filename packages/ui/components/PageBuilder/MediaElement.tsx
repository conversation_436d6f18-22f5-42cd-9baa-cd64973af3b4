import { Box, Center, Image } from '@mantine/core';
import { IconPhoto } from '@tabler/icons-react';
import { LinkBehaviorType, LinkDestinationType } from '../../types/pageBuilder';
import {
  createResponsiveValue,
  generateResponsiveDimension,
  generateResponsiveStyles,
} from '../../utils';

const getObjectFit = (objectFit: string) => {
  return objectFit === 'crop' || objectFit === 'fill' ? 'cover' : 'contain';
};

const MediaElement = (props: Record<string, any>) => {
  const { mediaUrl, objectFit, overlay, width, height, cornerRadius, link } = props;
  
  return (
    <a
      href={
        link ? (link.destination === LinkDestinationType.Page ? `/${link.url}` : link.url) : null
      }
      target={link?.behavior === LinkBehaviorType.NewTab ? '_blank' : undefined}
      rel={link?.behavior === LinkBehaviorType.NewTab ? 'noopener noreferrer' : undefined}
      style={{ display: 'block', width: '100%' }}>
      {mediaUrl ? (
        <Box pos='relative' component='div'>
          <Image
            alt='Media Element'
            src={mediaUrl}
            styles={generateResponsiveStyles({
              width: generateResponsiveDimension(width),
              height: generateResponsiveDimension(height),
              objectFit: createResponsiveValue(
                getObjectFit(objectFit.mobile),
                getObjectFit(objectFit.tablet),
                getObjectFit(objectFit.desktop)
              ),
              borderRadius: generateResponsiveDimension(cornerRadius),
            })}
          />
          {overlay?.enabled && (
            <Box
              pos='absolute'
              top={0}
              left={0}
              right={0}
              bottom={0}
              styles={generateResponsiveStyles({
                backgroundColor: overlay.color,
                opacity: overlay.opacity / 100,
                borderRadius: generateResponsiveDimension(cornerRadius),
              })}
            />
          )}
        </Box>
      ) : (
        <Center
          styles={generateResponsiveStyles({
            height: generateResponsiveDimension(height),
          })}
          sx={theme => ({
            backgroundColor: theme.colors.gray[0],
            borderRadius: 8,
            color: theme.colors.gray[6],
          })}>
          <IconPhoto size={32} />
        </Center>
      )}
    </a>
  );
};

export default MediaElement;
