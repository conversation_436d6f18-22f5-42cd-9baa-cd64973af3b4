import { Text } from '@mantine/core';
import { LinkBehaviorType, LinkDestinationType } from '../../types/pageBuilder';
import { generateResponsiveStyles } from '../../utils/pageBuilder';

export const TEXT_SIZE_MAP = {
  heading1: { fontSize: { mobile: '32px', tablet: '40px', desktop: '50px' } },
  heading2: { fontSize: { mobile: '28px', tablet: '36px', desktop: '40px' } },
  heading3: { fontSize: { mobile: '24px', tablet: '28px', desktop: '32px' } },
  heading4: { fontSize: { mobile: '20px', tablet: '24px', desktop: '28px' } },
  heading5: { fontSize: { mobile: '18px', tablet: '20px', desktop: '24px' } },
  paragraph1: { fontSize: { mobile: '16px', tablet: '16px', desktop: '16px' } },
  paragraph2: { fontSize: { mobile: '14px', tablet: '14px', desktop: '14px' } },
  caption: { fontSize: { mobile: '12px', tablet: '12px', desktop: '12px' } },
};

const FONT_WEIGHT_MAP = {
  regular: 400,
  medium: 500,
  bold: 700,
};

const TextElement = (props: Record<string, any>) => {
  const {
    width,
    height,
    align = 'left',
    color,
    type,
    text = 'This is a text element',
    link,
    typographySettings,
  } = props;

  const { fontSize } = TEXT_SIZE_MAP[type] ?? {};
  const fontSettings = type?.includes('heading')
    ? typographySettings?.heading
    : typographySettings?.caption;

  // Check if link is valid and has required properties
  const isValidLink = link && link.url && link.destination;
  const href = isValidLink
    ? (link.destination === LinkDestinationType.Page ? `/${link.url}` : link.url)
    : undefined;
  const target = isValidLink && link.behavior === LinkBehaviorType.NewTab ? '_blank' : undefined;
  const rel = isValidLink && link.behavior === LinkBehaviorType.NewTab ? 'noopener noreferrer' : undefined;

  const handleClick = () => {
    if (isValidLink) {
      if (link.behavior === LinkBehaviorType.NewTab) {
        window.open(href, '_blank', 'noopener,noreferrer');
      } else {
        window.location.href = href!;
      }
    }
  };

  return (
    <Text
      component={isValidLink ? 'a' : 'div'}
      href={href}
      target={target}
      rel={rel}
      onClick={isValidLink ? handleClick : undefined}
      styles={generateResponsiveStyles({
        width: width,
        height: height,
        textAlign: align,
        fontWeight: FONT_WEIGHT_MAP[fontSettings?.font_weight] || 400,
        '--text-fz': fontSize,
        '--text-color': color,
        ...(isValidLink && {
          cursor: 'pointer',
          textDecoration: 'none',
          display: 'inline-block',
          width: '100%',
        }),
      })}>
      {text}
    </Text>
  );
};

export default TextElement;