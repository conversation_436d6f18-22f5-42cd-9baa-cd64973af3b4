{"name": "deca-chatbot", "displayName": "DECA Chatbot", "icon": "🤖", "group": "deca", "category": ["deca-cloud-tools"], "description": "Interact with DECA Chatbot", "type": "deca-chatbot", "settings": {"credential": {"name": "credential", "displayName": "Credential", "type": "credential", "allowedCredentials": ["api_key"], "required": true, "description": "Credential to use for authentication", "order": 1}}, "credentials": {"api_key": {"name": "api_key", "displayName": "DECA API Key", "description": "API Key for authenticating with DECA Chatbot", "properties": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "API Key", "description": "Your DECA Chatbot API key", "type": "text", "required": true, "order": 1}, {"name": "baseUrl", "displayName": "Base URL", "description": "The base URL for the DECA Chatbot API", "type": "text", "required": false, "order": 2}]}}, "schemas": {"bot": {"properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "Unique identifier for the bot"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the bot"}, "avatar": {"name": "avatar", "displayName": "Avatar", "type": "string", "description": "Avatar URL for the bot"}, "organizationId": {"name": "organizationId", "displayName": "Organization ID", "type": "string", "description": "ID of the organization that owns the bot"}, "status": {"name": "status", "displayName": "Status", "type": "string", "description": "Current status of the bot (DRAFT, PUBLISHED, UNPUBLISHED)"}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the bot"}, "diagramVersion": {"name": "diagramVersion", "displayName": "Diagram Version", "type": "number", "description": "Version number of the bot's diagram"}, "isConnected": {"name": "isConnected", "displayName": "Is Connected", "type": "boolean", "description": "Whether the bot is currently connected"}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "description": "Additional metadata for the bot"}, "settings": {"name": "settings", "displayName": "Settings", "type": "object", "description": "Configuration settings for the bot", "properties": {"intentGlobalNoMatch": {"name": "intentGlobalNoMatch", "displayName": "Intent Global No Match", "type": "string", "description": "Global no match intent setting"}, "timeoutFlowEnabled": {"name": "timeoutFlowEnabled", "displayName": "Timeout Flow Enabled", "type": "boolean", "description": "Whether timeout flow is enabled"}, "endConversationMessage": {"name": "endConversationMessage", "displayName": "End Conversation Message", "type": "object", "description": "End conversation message settings", "properties": {"enable": {"name": "enable", "displayName": "Enable", "type": "boolean", "description": "Whether to enable end conversation message"}, "message": {"name": "message", "displayName": "Message", "type": "string", "description": "End conversation message text"}}}}}, "created": {"name": "created", "displayName": "Created", "type": "string", "description": "Timestamp when the bot was created"}, "updated": {"name": "updated", "displayName": "Updated", "type": "string", "description": "Timestamp when the bot was last updated"}}}, "diagram": {"properties": {"flows": {"name": "flows", "displayName": "Flows", "type": "array", "description": "Flows in the diagram", "properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "ID of the flow"}, "botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot this flow belongs to"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the flow"}, "category": {"name": "category", "displayName": "Category", "type": "string", "description": "Category of the flow"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of the flow"}, "nodes": {"name": "nodes", "displayName": "Nodes", "type": "object", "description": "Nodes in the flow"}, "configs": {"name": "configs", "displayName": "Configs", "type": "object", "description": "Configuration for the flow", "properties": {"intentNodes": {"name": "intentNodes", "displayName": "Intent Nodes", "type": "array", "description": "Intent nodes configuration", "properties": {"intentScope": {"name": "intentScope", "displayName": "Intent <PERSON>", "type": "string", "description": "Scope of the intent"}, "intentId": {"name": "intentId", "displayName": "Intent ID", "type": "string", "description": "ID of the intent"}, "ID": {"name": "ID", "displayName": "ID", "type": "string", "description": "ID of the flow"}}}}}, "created": {"name": "created", "displayName": "Created", "type": "string", "description": "Timestamp when the flow was created"}, "updated": {"name": "updated", "displayName": "Updated", "type": "string", "description": "Timestamp when the flow was last updated"}}}, "version": {"name": "version", "displayName": "Version", "type": "number", "description": "Version number of the diagram"}, "variables": {"name": "variables", "displayName": "Variables", "type": "array", "description": "Variables in the diagram", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot this variable belongs to"}, "id": {"name": "id", "displayName": "ID", "type": "string", "description": "ID of the variable"}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the variable"}, "created": {"name": "created", "displayName": "Created", "type": "string", "description": "Timestamp when the variable was created"}, "updated": {"name": "updated", "displayName": "Updated", "type": "string", "description": "Timestamp when the variable was last updated"}}}, "intents": {"name": "intents", "displayName": "Intents", "type": "array", "description": "Intents in the diagram", "properties": {"id": {"name": "id", "displayName": "ID", "type": "string", "description": "ID of the intent"}, "label": {"name": "label", "displayName": "Label", "type": "string", "description": "Label of the intent"}, "language": {"name": "language", "displayName": "Language", "type": "string", "description": "Language of the intent"}, "botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot this intent belongs to"}, "utterances": {"name": "utterances", "displayName": "Utterances", "type": "array", "description": "List of utterances for the intent"}, "created": {"name": "created", "displayName": "Created", "type": "string", "description": "Timestamp when the intent was created"}, "updated": {"name": "updated", "displayName": "Updated", "type": "string", "description": "Timestamp when the intent was last updated"}}}, "settings": {"name": "settings", "displayName": "Settings", "type": "object", "description": "Settings for the diagram"}}}, "pagination": {"properties": {"limit": {"name": "limit", "displayName": "Limit", "type": "number", "description": "Limit the number of items to get", "required": false}, "next": {"name": "next", "displayName": "Next", "type": "string", "description": "Next cursor to get the next page of items", "required": false}, "data": {"name": "data", "displayName": "Data", "type": "array", "description": "Data of the items"}}}, "diagram_version": {"properties": {"name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the diagram version"}, "botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot this diagram version belongs to"}, "created": {"name": "created", "displayName": "Created", "type": "string", "description": "Timestamp when the diagram version was created"}, "updated": {"name": "updated", "displayName": "Updated", "type": "string", "description": "Timestamp when the diagram version was last updated"}, "version": {"name": "version", "displayName": "Version", "type": "number", "description": "Version number of the diagram"}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of the diagram version"}}}}, "triggers": {}, "actions": {"create_bot": {"name": "create_bot", "displayName": "Create <PERSON><PERSON>", "description": "Create a new chatbot", "properties": {"name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the bot", "required": true}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the bot", "required": false}, "avatar": {"name": "avatar", "displayName": "Avatar", "type": "string", "description": "Avatar URL for the bot", "required": false}, "isConnected": {"name": "isConnected", "displayName": "Is Connected", "type": "boolean", "description": "Whether the bot should be connected", "required": false}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "description": "Additional metadata for the bot", "required": false}}, "data": {"$ref": "#/schemas/bot"}}, "get_bots": {"name": "get_bots", "displayName": "<PERSON>ts", "description": "Get all chatbots", "properties": {"limit": {"name": "limit", "displayName": "Limit", "type": "number", "description": "Limit the number of bots to get", "required": false}, "next": {"name": "next", "displayName": "Next", "type": "string", "description": "Next cursor to get the next page of bots", "required": false}}, "data": {"$ref": "#/schemas/pagination"}}, "update_bot": {"name": "update_bot", "displayName": "Update Bot", "description": "Update an existing chatbot", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to update", "required": true}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the bot", "required": false}, "description": {"name": "description", "displayName": "Description", "type": "string", "description": "Description of the bot", "required": false}, "avatar": {"name": "avatar", "displayName": "Avatar", "type": "string", "description": "Avatar URL for the bot", "required": false}, "metadata": {"name": "metadata", "displayName": "<PERSON><PERSON><PERSON>", "type": "object", "description": "Additional metadata for the bot", "required": false}, "settings": {"name": "settings", "displayName": "Settings", "type": "object", "description": "Configuration settings for the bot", "required": false}, "isConnected": {"name": "isConnected", "displayName": "Is Connected", "type": "boolean", "description": "Whether the bot should be connected", "required": false}, "status": {"name": "status", "displayName": "Status", "type": "string", "description": "Status of the bot (DRAFT, PUBLISHED, UNPUBLISHED)", "required": false, "enum": ["DRAFT", "PUBLISHED", "UNPUBLISHED"]}}, "data": {"$ref": "#/schemas/bot"}}, "get_bot": {"name": "get_bot", "displayName": "Get Bot", "description": "Get an existing chatbot", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to get", "required": true}}, "data": {"$ref": "#/schemas/bot"}}, "delete_bot": {"name": "delete_bot", "displayName": "Delete Bot", "description": "Delete an existing chatbot", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to delete", "required": true}}}, "get_diagram": {"name": "get_diagram", "displayName": "Get Diagram", "description": "Get the diagram for a chatbot", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to get the diagram for", "required": true}}, "data": {"$ref": "#/schemas/diagram"}}, "import_diagram": {"name": "import_diagram", "displayName": "Import Diagram", "description": "Import a diagram for a chatbot", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to import the diagram for", "required": true}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the diagram to import", "required": false}, "diagram": {"name": "diagram", "displayName": "Diagram", "type": "object", "description": "Diagram to import", "required": true}, "url": {"name": "url", "displayName": "URL", "type": "string", "description": "URL", "required": false}}}, "get_diagram_versions": {"name": "get_diagram_versions", "displayName": "Get Diagram Versions", "description": "Get all versions of the diagram for a chatbot", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to get the diagram versions for", "required": true}, "limit": {"name": "limit", "displayName": "Limit", "type": "number", "description": "Limit the number of versions to get", "required": false}, "next": {"name": "next", "displayName": "Next", "type": "string", "description": "Next cursor to get the next page of versions", "required": false}, "type": {"name": "type", "displayName": "Type", "type": "string", "description": "Type of the diagram version", "required": false, "enum": ["manual", "system"]}}, "data": {"$ref": "#/schemas/pagination"}}, "create_diagram_version": {"name": "create_diagram_version", "displayName": "Create Diagram Version", "description": "Create a new version of the diagram", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to create the diagram version for", "required": true}, "name": {"name": "name", "displayName": "Name", "type": "string", "description": "Name of the diagram version", "required": false}}}, "get_diagram_version": {"name": "get_diagram_version", "displayName": "Get Diagram Version", "description": "Get a version of the diagram", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to get the diagram version for", "required": true}, "version": {"name": "version", "displayName": "Version", "type": "number", "description": "Version number of the diagram to get", "required": true}}, "data": {"$ref": "#/schemas/diagram_version"}}, "delete_diagram_version": {"name": "delete_diagram_version", "displayName": "Delete Diagram Version", "description": "Delete a version of the diagram", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to delete the diagram version for", "required": true}, "version": {"name": "version", "displayName": "Version", "type": "number", "description": "Version number of the diagram to delete", "required": true}}}, "restore_diagram_version": {"name": "restore_diagram_version", "displayName": "Restore Diagram Version", "description": "Restore a version of the diagram", "properties": {"botId": {"name": "botId", "displayName": "Bot ID", "type": "string", "description": "ID of the bot to restore the diagram version for", "required": true}, "version": {"name": "version", "displayName": "Version", "type": "number", "description": "Version number of the diagram to restore", "required": true}}}}}