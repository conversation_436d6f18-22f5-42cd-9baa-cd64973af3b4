{"name": "code", "displayName": "Code", "icon": "🧩", "group": "core", "category": ["builtin-popular", "logic"], "description": "Execute custom script code", "settings": {}, "schemas": {"codeOutput": {"properties": {"results": {"name": "results", "displayName": "Results", "type": ["object", "array", "string", "integer", "boolean"], "description": "The results of the code execution", "order": 1}, "error": {"name": "error", "displayName": "Error", "type": ["string", "object"], "description": "The error message if the code execution fails", "order": 2}, "logs": {"name": "logs", "displayName": "Logs", "type": ["array", "string"], "description": "The logs of the code execution", "order": 3}, "duration": {"name": "duration", "displayName": "Duration", "type": "integer", "description": "The duration of the code execution in milliseconds", "order": 4}}}}, "credentials": {}, "triggers": {}, "actions": {"run_javascript": {"name": "run_javascript", "displayName": "Run JavaScript", "description": "Executes the provided JavaScript code", "order": 1, "properties": {"code": {"name": "code", "displayName": "Code", "description": "The code to execute", "type": "string", "required": true, "order": 1}, "input": {"name": "input", "displayName": "Input Params", "description": "The input params to process", "type": "keyvalue", "default": {"var1": {"name": "var1", "displayName": "Variable 1", "type": "text", "description": "The input params to process", "order": 1}}, "order": 2}}, "data": {"$ref": "#/schemas/codeOutput"}}}}