import {
  CustomIconProps,
  DecaIconAIWidget,
  DecaIconChatbot,
  DecaIconCRM,
  DecaIconKB,
  DecaIconLivechat,
  DecaIconPages,
  DecaIconTables,
  IconAnthropic,
  IconAwsBedrock,
  IconCode,
  IconFilter,
  IconFormatter,
  IconFunction,
  IconGmail,
  IconGoogleAiStudio,
  IconGoogleCalendar,
  IconGoogleDocs,
  IconGoogleDrive,
  IconGoogleMeet,
  IconGoogleSheets,
  IconGoogleSlides,
  IconGoogleVertexAi,
  IconHttp,
  IconHubspot,
  IconLine,
  IconLooping,
  IconMicrosoftTeams,
  IconNewTrigger,
  IconNotion,
  IconOpenai,
  IconOutlook,
  IconPath,
  IconPerplexity,
  IconSalesforce,
  IconSchedule,
  IconSlack,
  IconStorage,
  IconWait,
  IconWebhook,
  IconZendesk,
  IconZoom,
} from '../Icons';

export type IconMapper = {
  component: React.ComponentType<CustomIconProps>;
  withPrimaryColor?: boolean;
  addSize?: number;
};

const iconMapper: Record<string, IconMapper> = {
  // Build-in tools
  code: {
    component: IconCode,
    withPrimaryColor: true,
  },
  filter: {
    component: IconFilter,
    withPrimaryColor: true,
  },
  formatter: {
    component: IconFormatter,
    withPrimaryColor: true,
  },
  function: {
    component: IconFunction,
    withPrimaryColor: true,
  },
  http: {
    component: IconHttp,
    withPrimaryColor: true,
  },
  'new-trigger': {
    component: IconNewTrigger,
    withPrimaryColor: true,
  },
  path: {
    component: IconPath,
    withPrimaryColor: true,
  },
  storage: {
    component: IconStorage,
    withPrimaryColor: true,
  },
  wait: {
    component: IconWait,
    withPrimaryColor: true,
  },
  loop: {
    component: IconLooping,
    withPrimaryColor: true,
  },
  schedule: {
    component: IconSchedule,
    withPrimaryColor: true,
  },
  // External tools
  gmail: {
    component: IconGmail,
  },
  openai: {
    component: IconOpenai,
  },
  'zoom-meetings': {
    component: IconZoom,
  },
  'google-calendar': {
    component: IconGoogleCalendar,
  },
  'google-drive': {
    component: IconGoogleDrive,
  },
  hubspot: {
    component: IconHubspot,
  },
  slack: {
    component: IconSlack,
  },
  'google-slides': {
    component: IconGoogleSlides,
  },
  'google-sheets': {
    component: IconGoogleSheets,
  },
  'google-docs': {
    component: IconGoogleDocs,
  },
  webhook: {
    component: IconWebhook,
  },
  anthropic: {
    component: IconAnthropic,
  },
  'aws-bedrock': {
    component: IconAwsBedrock,
  },
  'google-ai-studio': {
    component: IconGoogleAiStudio,
  },
  'google-meet': {
    component: IconGoogleMeet,
  },
  'google-vertex-ai': {
    component: IconGoogleVertexAi,
  },
  line: {
    component: IconLine,
  },
  'microsoft-teams': {
    component: IconMicrosoftTeams,
  },
  notion: {
    component: IconNotion,
  },
  outlook: {
    component: IconOutlook,
  },
  perplexity: {
    component: IconPerplexity,
  },
  salesforce: {
    component: IconSalesforce,
  },
  zendesk: {
    component: IconZendesk,
  },
  // Deca services
  'deca-kb': {
    component: DecaIconKB,
    addSize: 3,
  },
  'deca-ai-widgets': {
    component: DecaIconAIWidget,
    addSize: 3,
  },
  'deca-chatbot': {
    component: DecaIconChatbot,
    addSize: 3,
  },
  'deca-crm': {
    component: DecaIconCRM,
    addSize: 3,
  },
  'deca-tables': {
    component: DecaIconTables,
    addSize: 3,
  },
  'deca-livechat': {
    component: DecaIconLivechat,
    addSize: 4,
  },
  pages: {
    component: DecaIconPages,
    addSize: 3,
  },
  // Other
  'virtual-node-sub-path': {
    component: IconPath,
    withPrimaryColor: true,
  },
};

export default iconMapper;
