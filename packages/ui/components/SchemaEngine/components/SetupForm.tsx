import { <PERSON><PERSON><PERSON><PERSON>, Stack } from '@mantine/core';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import AppSelector from '../../AppSelector';
import { StepperActions, Stepper, Step } from './Stepper';
import {
  NodeApiCallCallback,
  ObjectType,
  Schema,
  SchemaFormCallback,
  SetupFormInput,
} from '../types';
import { FormActions, FormCredential, FormTriggerEvent } from '../../FormChema';
import { useForm } from 'react-hook-form';
import { useCallbackRef } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { ICredential, ICredentialPayload } from '../../Credential';
import { getNodeApiCallPayloads } from '../../../utils/schema';
import set from 'lodash/set';

export interface SetupFormProps {
  schema: Schema;
  steps?: Step[];
  activeStep?: number;
  completedStep?: number;
  objectType: ObjectType;
  onOpenAppCatalog?: () => void;
  onStepChange?: (step: number) => void;
  credentials: ICredential[];
  isCredentialsLoading: boolean;
  createCredential?: (credential: ICredentialPayload) => Promise<ICredential | null>;
  form: SetupFormInput;
  onFormChange?: SchemaFormCallback;
  onNodeApiCall?: NodeApiCallCallback;
  onNodeApiCallResultChange?: Dispatch<SetStateAction<Record<string, any>>>;
  completedApiCalls?: Record<string, boolean>;
  onCompletedApiCallChange?: Dispatch<SetStateAction<Record<string, boolean>>>;
}

const SetupForm = (props: SetupFormProps) => {
  const {
    schema,
    activeStep = 0,
    completedStep = -1,
    steps = [],
    objectType,
    credentials,
    isCredentialsLoading,
    createCredential = async () => null,
    onOpenAppCatalog = () => {},
    onStepChange = () => {},
    onFormChange = () => {},
    form = {},
    onNodeApiCall,
    completedApiCalls = {},
    onCompletedApiCallChange,
    onNodeApiCallResultChange,
  } = props;

  const { t } = useTranslate();
  const formChangeCallback = useCallbackRef(onFormChange);
  const [isLoading, setIsLoading] = useState(false);

  const selectedApp = {
    displayName: schema?.displayName,
    name: schema?.name,
  };

  const hasCredential = Object.keys(schema?.credentials || {}).length > 0;

  const methods = useForm<SetupFormInput>();
  const { control, watch, formState } = methods;
  const { isValid } = formState;
  const formValues = watch();

  const submitButtonText = useMemo(() => {
    if (hasCredential && !formValues.credential) {
      return t('form.chooseCredential');
    }
    if (!formValues[objectType]) {
      return objectType === 'action' ? t('form.chooseAction') : t('form.chooseTrigger');
    }
    return t('button.continue');
  }, [formValues, objectType, hasCredential, t]);

  const handleNext = useCallback(async () => {
    const apiCallPayloads = getNodeApiCallPayloads(schema, objectType, form[objectType] || {});

    if (apiCallPayloads.length > 0) {
      if (!onNodeApiCall) {
        console.warn('onNodeApiCall is not defined');
        return;
      }

      const apiCallsToMake = apiCallPayloads.filter(apiCall => {
        const groupName = apiCall.groupName;
        const isAlreadyCompleted = completedApiCalls[groupName];
        return apiCall.always || !isAlreadyCompleted;
      });

      if (apiCallsToMake.length > 0) {
        const updatedNodeApiCallResults: Record<string, any> = {};
        const updatedCompletedApiCalls: Record<string, boolean> = { ...completedApiCalls };

        try {
          setIsLoading(true);
          const apiCallPromises = apiCallsToMake.map(apiCall =>
            onNodeApiCall(apiCall.node, apiCall.attribute, apiCall.payload)
          );

          const results = await Promise.allSettled(apiCallPromises);

          results.forEach((result, index) => {
            const { groupName } = apiCallsToMake[index];
            if (result.status === 'fulfilled') {
              set(updatedNodeApiCallResults, `${groupName}.apiCall`, result.value);
              updatedCompletedApiCalls[groupName] = true;
            }
          });
        } catch (error) {
          console.warn('Error processing API calls:', error);
        } finally {
          setIsLoading(false);
        }

        onCompletedApiCallChange?.(prev => ({ ...prev, ...updatedCompletedApiCalls }));
        onNodeApiCallResultChange?.(prev => ({ ...prev, ...updatedNodeApiCallResults }));
      }
    }
    onStepChange?.(activeStep + 1);
  }, [
    activeStep,
    onStepChange,
    schema,
    form,
    objectType,
    onNodeApiCall,
    completedApiCalls,
    onCompletedApiCallChange,
    onNodeApiCallResultChange,
  ]);

  const updateCompletedStep = useCallback(() => {
    if (!isValid || activeStep <= completedStep) return;
    const formValuesTmp = watch();
    formChangeCallback({ ...formValuesTmp, completedFormStep: activeStep }, 'completedFormStep');
    Object.keys(formValuesTmp).forEach(key => {
      formChangeCallback({ ...formValuesTmp, completedFormStep: activeStep }, key);
    });
  }, [activeStep, completedStep, formChangeCallback, isValid, watch]);

  useEffect(() => {
    updateCompletedStep();
  }, [updateCompletedStep]);

  useEffect(() => {
    const subscription = watch((formValues, { name }) => {
      if (!name) return;
      formChangeCallback(formValues as any, name);
    });

    return () => subscription.unsubscribe();
  }, [watch, formChangeCallback]);

  return (
    <>
      <ScrollArea sx={{ flex: 1 }}>
        <Stack gap='lg'>
          <Stepper
            steps={steps}
            activeStep={activeStep}
            completedStep={completedStep}
            currentStepValid={isValid}
            onStepChange={onStepChange}
          />

          <AppSelector selectedApp={selectedApp} onChangeClick={onOpenAppCatalog} required />

          {hasCredential && (
            <FormCredential
              control={control}
              name={'credential'}
              schema={schema}
              credentials={credentials}
              isLoading={isCredentialsLoading}
              createCredential={createCredential}
              defaultValue={form['settings']?.['credential']}
              rules={{ required: true }}
            />
          )}

          {objectType === 'action' ? (
            <FormActions
              control={control}
              name={objectType}
              schema={schema}
              defaultValue={form[objectType]}
              rules={{ required: true }}
            />
          ) : (
            <FormTriggerEvent
              control={control}
              name={objectType}
              schema={schema}
              defaultValue={form[objectType]}
              rules={{ required: true }}
            />
          )}
        </Stack>
      </ScrollArea>

      <StepperActions
        activeStep={activeStep}
        showBackBtn={false}
        nextBtnLabel={submitButtonText}
        disabledNextBtn={!isValid}
        onNext={handleNext}
        isLoading={isLoading}
      />
    </>
  );
};

export default SetupForm;
