import React, { useCallback, useRef, useImperative<PERSON>andle, useEffect, useMemo } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import { useStyles } from './useStyles';
import { Editor, Node } from '@tiptap/core';
import { IComboboxOption } from './ComboboxOption';
import { Group } from '@mantine/core';
import { IconPlus } from '@tabler/icons-react';
import { IconNumberInput } from '../../../Icons';
import { debounce } from 'lodash';

// Custom TipTap Node for inline variable
const VariableNode = Node.create({
  name: 'variableNode',
  group: 'inline',
  inline: true,
  selectable: true,
  atom: true,
  addAttributes() {
    return {
      icon: { default: '' },
      label: { default: '' },
      nodeId: { default: '' },
      value: { default: '' },
    };
  },
  parseHTML() {
    return [
      {
        tag: 'span.variable-node',
        getAttrs: node => {
          if (typeof node === 'string') return false;
          const element = node as HTMLElement;
          return {
            icon: element.getAttribute('data-icon'),
            label: element.getAttribute('data-label'),
            nodeId: element.getAttribute('data-node-id'),
            value: element.getAttribute('data-value'),
          };
        },
      },
    ];
  },
  renderHTML({ HTMLAttributes }) {
    return [
      'span',
      {
        'data-icon': HTMLAttributes.icon,
        'data-label': HTMLAttributes.label,
        'data-node-id': HTMLAttributes.nodeId,
        'data-value': HTMLAttributes.value,
        class: 'variable-node',
      },
      ['span', { class: 'variable-icon' }, HTMLAttributes.icon],
      ['span', { class: 'variable-label' }, HTMLAttributes.label],
      [
        'span',
        {
          class: 'variable-value',
        },
        HTMLAttributes.value,
      ],
    ];
  },
  renderText({ node }) {
    return node.attrs.value;
  },
});
const parseValue = (value: string) => {
  try {
    return JSON.parse(value);
  } catch (error) {
    return {}
  }
};

export interface ComboboxRichInputProps {
  value?: { value: string; valueRaw: string };
  placeholder?: string;
  className?: string;
  allowInput?: boolean;
  isOneLine?: boolean;
  onSlashTrigger?: (open: boolean, searchValue?: string) => void;
  onChange?: (data: { value: string; valueRaw: string }) => void;
  onSearchValueChange?: (value: string) => void;
}

export interface ComboboxRichInputRef {
  insertVariable: (option: IComboboxOption) => void;
  setVariable: (option: IComboboxOption) => void;
}

export const ComboboxRichInput = React.forwardRef<ComboboxRichInputRef, ComboboxRichInputProps>(
  (
    {
      value,
      onChange,
      placeholder = 'Type or use / to insert variable',
      className,
      allowInput,
      isOneLine = false,
      onSlashTrigger,
      onSearchValueChange,
    },
    ref
  ) => {
    const { classes } = useStyles({ allowInput: allowInput ?? false });
    const editorRef = useRef<any>(null);
    const initialContent = useRef(parseValue(value?.valueRaw ?? '{}'));

    const editor = useEditor({
      editable: allowInput,
      extensions: [
        StarterKit as any,
        VariableNode,
        Placeholder.configure({
          placeholder,
          emptyEditorClass: 'is-editor-empty',
        }),
      ],
      content: initialContent.current,
      onUpdate: ({ editor }) => {
        debouncedOnChange(editor);
      },
      editorProps: {
        attributes: {
          class: `${classes.tiptapInput ?? ''} ${className ?? ''}`.trim(),
        },
        handleKeyDown(_, event) {
          if (event.key === '/') {
            onSlashTrigger?.(true);
            return false;
          }
          if (event.key === ' ') {
            onSlashTrigger?.(false);
          }
          if (event.key === 'Enter') {
            return isOneLine;
          }
          if (event.key.length === 1) {
            if (!editor) return false;
            const { state } = editor;
            const { selection } = state;
            const { from } = selection;
            const textBeforeCursor = state.doc.textBetween(0, from);
            const lastSlashIndex = textBeforeCursor.lastIndexOf('/');
            if (lastSlashIndex !== -1) {
              const textAfterSlash = textBeforeCursor.slice(lastSlashIndex + 1);
              if (!textAfterSlash.includes(' ')) {
                onSearchValueChange?.(textAfterSlash + event.key);
                return false;
              }
            }
          }
          return false;
        },
      },
    });

    useEffect(() => {
      if (editor && value !== undefined && JSON.stringify(parseValue(value?.valueRaw)) !== JSON.stringify(editor.getJSON())) {
        editor.commands.setContent(parseValue(value?.valueRaw), false);
      }
    }, [editor, value]);

    const insertVariable = useCallback(
      (option: IComboboxOption) => {
        if (!editor) return;
        const { state } = editor;
        const { selection } = state;
        const { from } = selection;
        const textBefore = state.doc.textBetween(from - 1, from);
        if (textBefore === '/') {
          editor
            .chain()
            .deleteRange({ from: from - 1, to: from })
            .run();
        }
        editor
          .chain()
          .focus()
          .insertContent({
            type: 'variableNode',
            attrs: {
              icon: option.icon,
              label: option.label,
              nodeId: option.value,
              value: option.value,
            },
          })
          .run();
      },
      [editor]
    );

    const setVariable = useCallback(
      (option: IComboboxOption) => {
        if (!editor) return;
        editor
          .chain()
          .deleteRange({ from: 0, to: editor.state.doc.content.size })
          .insertContent({
            type: 'variableNode',
            attrs: {
              icon: option.icon,
              label: option.label,
              nodeId: option.value,
              value: option.value,
            },
          })
          .run();
      },
      [editor]
    );

    useImperativeHandle(
      ref,
      () => ({
        insertVariable,
        setVariable,
      }),
      [insertVariable, setVariable]
    );

    const handleChange = useCallback(
      (editor: Editor) => {
        onChange?.({ value: editor.getText(), valueRaw: JSON.stringify(editor.getJSON()) });
      },
      [onChange]
    );

    const debouncedOnChange = useMemo(() => debounce(handleChange, 500), [handleChange]);

    return (
      <Group
        className={classes.tiptapInputWrapper}
        onClick={() => {
          if (!allowInput) {
            onSlashTrigger?.(true);
          }
        }}>
        {!allowInput && editor && editor.isEmpty && (
          <span className='custom-placeholder'>{placeholder}</span>
        )}
        <EditorContent
          editor={editor}
          ref={editorRef}
          className={`${classes.tiptapInput ?? ''} ${className ?? ''}`.trim()}
        />
        {allowInput ? (
          <IconPlus size={16} onClick={() => onSlashTrigger?.(true)} className={classes.iconPlus} />
        ) : (
          <IconNumberInput className={classes.iconNumberInput} />
        )}
      </Group>
    );
  }
);

ComboboxRichInput.displayName = 'ComboboxRichInput';

export default ComboboxRichInput;
